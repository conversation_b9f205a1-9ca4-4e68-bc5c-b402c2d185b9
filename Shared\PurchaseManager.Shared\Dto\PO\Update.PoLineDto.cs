using System.ComponentModel.DataAnnotations;
namespace PurchaseManager.Shared.Dto.PO;

public class UpdatePoLineDto
{
    [Required]
    public string DocumentNumber { get; set; } = null!;
    [Required]
    public int LineNumber { get; set; }
    [Required]
    public int DocumentType { get; set; }
    [Required]
    public string ItemNumber { get; set; } = null!;
    [Required]
    public string UnitOfMeasure { get; set; } = null!;
    /// <summary>
    /// Số lượng đặt hàng
    /// </summary>
    [Required]
    public decimal Quantity { get; set; }
    /// <summary>
    /// Còn lại
    /// </summary>
    [Required]
    public decimal QuantityToReceive { get; set; }
    /// <summary>
    /// Đã nhận hàng
    /// </summary>
    [Required]
    public decimal QuantityReceived { get; set; }
    [Required]
    public decimal Vat { get; set; }
    [Required]
    public decimal LineDiscountPercent { get; set; }
    [Required]
    public decimal LineDiscountAmount { get; set; }
    [Required]
    public decimal Amount { get; set; }
    [Required]
    public decimal AmountIncludingVat { get; set; }
    [Required]
    public decimal ProfitPercent { get; set; }
    [Required]
    public decimal VatAmount { get; set; }
    [Required]
    public decimal VatBaseAmount { get; set; }
    [Required]
    public decimal UnitCost { get; set; }
    [Required]
    public string LotNo { get; set; } = null!;
    [Required]
    public DateOnly ExpirationDate { get; set; }
    [Required]
    public decimal LastUnitCost { get; set; }
    public string Desc { get; set; } = string.Empty;
    public int RowID { get; set; }
}
