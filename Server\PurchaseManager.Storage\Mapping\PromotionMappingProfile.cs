﻿using AutoMapper;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.Promotions;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
namespace PurchaseManager.Storage.Mapping;

public class PromotionMappingProfile : Profile
{
    public PromotionMappingProfile()
    {
        CreateMap<PromotionHeader, GetPromotionHeaderDto>()
            .ForMember(destinationMember: dest => dest.ProgramCode, memberOptions: opt => opt.MapFrom(src => src.Number))
            .ForMember(destinationMember: dest => dest.VendorName,
            memberOptions: opt => opt.MapFrom(src => src.Vendor != null ? src.Vendor.Name : string.Empty))
            .ForMember(destinationMember: dest => dest.FrontMargins,
            memberOptions: opt => opt.MapFrom(src => src.PromotionFrontMargins))
            .ReverseMap()
            .ForMember(destinationMember: dest => dest.Number, memberOptions: opt => opt.MapFrom(src => src.ProgramCode))
            .ForMember(destinationMember: dest => dest.Vendor, memberOptions: opt => opt.Ignore())
            .ForMember(destinationMember: dest => dest.PromotionFrontMargins,
            memberOptions: opt => opt.MapFrom(src => src.FrontMargins));

        CreateMap<CreatePromotionHeaderDto, PromotionHeader>()
            .ForMember(destinationMember: dest => dest.Vendor, memberOptions: opt => opt.Ignore())
            .ForMember(destinationMember: dest => dest.PromotionFrontMargins, memberOptions: opt => opt.Ignore())
            .ForMember(destinationMember: dest => dest.PromotionConditions, memberOptions: opt => opt.Ignore())
            .ForMember(destinationMember: dest => dest.PromotionRewards, memberOptions: opt => opt.Ignore())
            .ReverseMap();

        CreateMap<PromotionHeader, UpdatePromotionHeaderDto>()
            .ReverseMap();

        CreateMap<GetPromotionHeaderDto, UpdatePromotionHeaderDto>()
            .ReverseMap();

        // PromotionFrontMargin mappings with all discount types support
        CreateMap<PromotionFrontMargin, GetPromotionFrontMarginDto>()
            .ForMember(destinationMember: dest => dest.Number, memberOptions: opt
                => opt.MapFrom(src => src.Number))
            .ForMember(destinationMember: dest => dest.ProgramName, memberOptions: opt
                => opt.MapFrom(src => src.PromotionHeader.ProgramName))
            .ForMember(destinationMember: dest => dest.StartDate, memberOptions: opt
                => opt.MapFrom(src => src.PromotionHeader.StartDate))
            .ForMember(destinationMember: dest => dest.EndDate, memberOptions: opt
                => opt.MapFrom(src => src.PromotionHeader.EndDate))
            .ForMember(destinationMember: dest => dest.DiscountTypeName, memberOptions: opt
                => opt.MapFrom(src => GetDiscountTypeName(src.DiscountType)))
            .ForMember(destinationMember: dest => dest.IsPercentageDiscount, memberOptions: opt
                => opt.MapFrom(src => src.DiscountType == 1))
            .ForMember(destinationMember: dest => dest.IsFixedAmountDiscount, memberOptions: opt
                => opt.MapFrom(src => src.DiscountType == 2))
            .ForMember(destinationMember: dest => dest.IsSameItemGift, memberOptions: opt
                => opt.MapFrom(src => src.DiscountType == 3))
            .ForMember(destinationMember: dest => dest.IsDifferentItemGift, memberOptions: opt
                => opt.MapFrom(src => src.DiscountType == 4))
            .ReverseMap()
            .ForMember(destinationMember: dest => dest.PromotionHeader, memberOptions: opt
                => opt.Ignore())
            // .ForMember(destinationMember: dest => dest.Number, memberOptions: opt
            //     => opt.Ignore())
            .ForMember(destinationMember: dest => dest.CreatedBy, memberOptions: opt
                => opt.Ignore())
            .ForMember(destinationMember: dest => dest.CreatedAt, memberOptions: opt
                => opt.Ignore())
            .ForMember(destinationMember: dest => dest.LastModifiedBy, memberOptions: opt
                => opt.Ignore())
            .ForMember(destinationMember: dest => dest.LastModifiedAt, memberOptions: opt
                => opt.Ignore())
            .ForMember(destinationMember: dest => dest.ModificationStatus, memberOptions: opt
                => opt.Ignore())
            .ForMember(destinationMember: dest => dest.RowId, memberOptions: opt
                => opt.Ignore());

        CreateMap<CreatePromotionFrontMarginDto, PromotionFrontMargin>()
            .ForMember(destinationMember: dest => dest.Number, memberOptions: opt
                => opt.Ignore())
            .ForMember(destinationMember: dest => dest.PromotionHeader, memberOptions: opt
                => opt.Ignore())
            .ForMember(destinationMember: dest => dest.CreatedBy, memberOptions: opt
                => opt.Ignore())
            .ForMember(destinationMember: dest => dest.CreatedAt, memberOptions: opt
                => opt.Ignore())
            .ForMember(destinationMember: dest => dest.LastModifiedBy, memberOptions: opt
                => opt.Ignore())
            .ForMember(destinationMember: dest => dest.LastModifiedAt, memberOptions: opt
                => opt.Ignore())
            .ForMember(destinationMember: dest => dest.ModificationStatus, memberOptions: opt
                => opt.Ignore())
            .ForMember(destinationMember: dest => dest.RowId, memberOptions: opt
                => opt.Ignore())
            .ReverseMap();

        CreateMap<UpdatePromotionFrontMarginDto, PromotionFrontMargin>()
            .ForMember(destinationMember: dest => dest.Number, memberOptions: opt => opt.Ignore())
            .ForMember(destinationMember: dest => dest.ProgramNumber, memberOptions: opt => opt.Ignore())
            .ForMember(destinationMember: dest => dest.LineNumber, memberOptions: opt => opt.Ignore())
            .ForMember(destinationMember: dest => dest.PromotionHeader, memberOptions: opt => opt.Ignore())
            .ForMember(destinationMember: dest => dest.CreatedBy, memberOptions: opt => opt.Ignore())
            .ForMember(destinationMember: dest => dest.CreatedAt, memberOptions: opt => opt.Ignore())
            .ForMember(destinationMember: dest => dest.RowId, memberOptions: opt => opt.Ignore())
            .ReverseMap();

        // Cross-DTO mappings for Front Margin
        CreateMap<GetPromotionFrontMarginDto, CreatePromotionFrontMarginDto>()
            .ReverseMap();

        CreateMap<GetPromotionFrontMarginDto, UpdatePromotionFrontMarginDto>()
            .ReverseMap();

        CreateMap<CreatePromotionFrontMarginDto, UpdatePromotionFrontMarginDto>()
            .ReverseMap();

        // Legacy mappings (keep for backward compatibility)
        CreateMap<UpdatePromotionFrontMarginDto, PromotionFrontMargin>()
            .ReverseMap();


        // Tracking a Front Margin usage
        CreateMap<CreatePromotionFrontMarginUsageDto, PromotionFrontMarginUsage>()
            .ReverseMap();
        CreateMap<GetPromotionFrontMarginUsageDto, PromotionFrontMarginUsage>()
            .ReverseMap();

        CreateMap<PromotionFrontMarginUsage, GetPromotionFrontMarginUsageDto>()
            .ForMember(destinationMember: dest => dest.Id,
            memberOptions: opt
                => opt.MapFrom(src => src.RowId))
            .ForMember(destinationMember: dest => dest.PromotionProgramName,
            memberOptions: opt
                => opt.MapFrom(src => src.PromotionFrontMargin.PromotionHeader.ProgramName ?? ""))
            .ForMember(destinationMember: dest => dest.VendorName,
            memberOptions: opt
                => opt.MapFrom(src => src.PromotionFrontMargin.PromotionHeader.Vendor.Name))
            .ForMember(destinationMember: dest => dest.DiscountTypeName,
            memberOptions: opt
                => opt.MapFrom(src => GetDiscountTypeName(src.DiscountType)))
            .ForMember(destinationMember: dest => dest.StatusName,
            memberOptions: opt
                => opt.MapFrom(src => nameof(src.Status)))
            .ForMember(destinationMember: dest => dest.CreatedDate,
            memberOptions: opt
                => opt.MapFrom(src => src.CreatedAt))
            .ForMember(destinationMember: dest => dest.ModifiedDate,
            memberOptions: opt
                => opt.MapFrom(src => src.LastModifiedAt))
            .ForMember(destinationMember: dest => dest.ModifiedBy,
            memberOptions: opt
                => opt.MapFrom(src => src.LastModifiedBy));

        // Map PromotionFrontMarginUsage to AppliedPromotionMappingDto
        CreateMap<PromotionFrontMarginUsage, AppliedPromotionMappingDto>()
            .ForMember(destinationMember: dest => dest.UsageId, memberOptions: opt => opt.MapFrom(src => src.RowId))
            .ForMember(destinationMember: dest => dest.PromotionNumber, memberOptions: opt => opt.MapFrom(src => src.PromotionNumber))
            .ForMember(destinationMember: dest => dest.ProgramName,
            memberOptions: opt => opt.MapFrom(src => src.PromotionFrontMargin.PromotionHeader.ProgramName ?? "Unknown"))
            .ForMember(destinationMember: dest => dest.DiscountType,
            memberOptions: opt => opt.MapFrom(src => src.PromotionFrontMargin.DiscountType))
            .ForMember(destinationMember: dest => dest.Status, memberOptions: opt => opt.MapFrom(src => src.Status))
            .ForMember(destinationMember: dest => dest.AppliedDate, memberOptions: opt => opt.MapFrom(src => src.AppliedDate))
            .ForMember(destinationMember: dest => dest.AppliedBy, memberOptions: opt => opt.MapFrom(src => src.AppliedBy ?? ""))
            // Item details
            .ForMember(destinationMember: dest => dest.ItemNumber, memberOptions: opt => opt.MapFrom(src => src.ItemNumber))
            .ForMember(destinationMember: dest => dest.ItemName, memberOptions: opt => opt.MapFrom(src => src.ItemName))
            .ForMember(destinationMember: dest => dest.VendorCode, memberOptions: opt => opt.MapFrom(src => src.VendorCode))
            // Promotion details
            .ForMember(destinationMember: dest => dest.DiscountPercentage,
            memberOptions: opt => opt.MapFrom(src => src.PromotionFrontMargin.DiscountPercentage))
            .ForMember(destinationMember: dest => dest.FixedDiscountAmount,
            memberOptions: opt => opt.MapFrom(src => src.PromotionFrontMargin.FixedDiscountAmount))
            .ForMember(destinationMember: dest => dest.BuyQuantity,
            memberOptions: opt => opt.MapFrom(src => src.PromotionFrontMargin.BuyQuantity))
            .ForMember(destinationMember: dest => dest.GiftQuantity,
            memberOptions: opt => opt.MapFrom(src => src.PromotionFrontMargin.GiftQuantity))
            .ForMember(destinationMember: dest => dest.GiftItemNumber,
            memberOptions: opt => opt.MapFrom(src => src.PromotionFrontMargin.GiftItemNumber));
    }
    /// <summary>
    ///     Get display name for discount type
    /// </summary>
    private static string GetDiscountTypeName(int discountType)
    {
        return discountType switch
        {
            1 => "Chiết khấu theo phần trăm",
            2 => "Chiết khấu số tiền cố định",
            3 => "Mua hàng tặng hàng cùng loại",
            4 => "Tặng hàng khác loại",
            _ => "Không xác định"
        };
    }

}
