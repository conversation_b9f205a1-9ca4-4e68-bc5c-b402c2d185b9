namespace PurchaseManager.Shared.Dto.Promotions.FrontMargins;

/// <summary>
/// DTO for creating Front Margin promotion usage records
/// </summary>
public class CreatePromotionFrontMarginUsageDto
{
    /// <summary>
    /// Reference to the Front Margin promotion that was applied
    /// </summary>
    public string PromotionNumber { get; set; } = string.Empty;

    /// <summary>
    /// PO Number where the promotion was applied
    /// </summary>
    public string PONumber { get; set; } = string.Empty;

    /// <summary>
    /// PO Line Number where the promotion was applied
    /// </summary>
    public int POLineNumber { get; set; }

    /// <summary>
    /// Item number that received the promotion
    /// </summary>
    public string ItemNumber { get; set; } = string.Empty;

    /// <summary>
    /// Item name for display purposes
    /// </summary>
    public string ItemName { get; set; } = string.Empty;

    /// <summary>
    /// Vendor code
    /// </summary>
    public string VendorCode { get; set; } = string.Empty;

    /// <summary>
    /// Discount type applied: 1=Percentage, 2=FixedAmount, 3=SameItemGift, 4=DifferentItemGift
    /// </summary>
    public int DiscountType { get; set; }

    /// <summary>
    /// Original quantity before promotion
    /// </summary>
    public decimal OriginalQuantity { get; set; }

    /// <summary>
    /// Final quantity after promotion (for same item gifts)
    /// </summary>
    public decimal FinalQuantity { get; set; }

    /// <summary>
    /// Original unit cost before promotion
    /// </summary>
    public decimal OriginalUnitCost { get; set; }

    /// <summary>
    /// Final unit cost after promotion
    /// </summary>
    public decimal FinalUnitCost { get; set; }

    /// <summary>
    /// Discount percentage applied (for percentage discounts)
    /// </summary>
    public decimal DiscountPercentage { get; set; }

    /// <summary>
    /// Fixed discount amount applied (for fixed amount discounts)
    /// </summary>
    public decimal FixedDiscountAmount { get; set; }

    /// <summary>
    /// Total discount amount calculated
    /// </summary>
    public decimal TotalDiscountAmount { get; set; }

    /// <summary>
    /// Original line amount before promotion
    /// </summary>
    public decimal OriginalLineAmount { get; set; }

    /// <summary>
    /// Final line amount after promotion
    /// </summary>
    public decimal FinalLineAmount { get; set; }

    /// <summary>
    /// Gift quantity for same item gifts
    /// </summary>
    public decimal GiftQuantity { get; set; }

    /// <summary>
    /// Gift item number for different item gifts
    /// </summary>
    public string? GiftItemNumber { get; set; }

    /// <summary>
    /// Gift item name for different item gifts
    /// </summary>
    public string? GiftItemName { get; set; }

    /// <summary>
    /// Gift item quantity for different item gifts
    /// </summary>
    public decimal GiftItemQuantity { get; set; }

    /// <summary>
    /// Gift line PO number (for different item gifts)
    /// </summary>
    public string? GiftPOLineNumber { get; set; }

    /// <summary>
    /// User who applied the promotion
    /// </summary>
    public string AppliedBy { get; set; } = string.Empty;

    /// <summary>
    /// Additional notes about the promotion application
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Gift calculation type: 1=Progressive, 2=Milestone
    /// Only used for DiscountType = 3 (Same Item Gift) and 4 (Different Item Gift)
    /// </summary>
    public int? GiftCalculationType { get; set; } = 1; // Default to Progressive

    /// <summary>
    /// Quantity threshold for tier bonus (e.g., 100 units)
    /// Used with DiscountType = 1 (Percentage) and 2 (Fixed Amount) for tier bonuses
    /// </summary>
    public decimal? TierQuantityThreshold { get; set; }

    /// <summary>
    /// Additional percentage discount when tier threshold is met (e.g., 5%)
    /// Used with DiscountType = 1 (Percentage)
    /// </summary>
    public decimal? TierBonusPercentage { get; set; }

    /// <summary>
    /// Additional fixed amount discount when tier threshold is met (e.g., 2,000,000)
    /// Used with DiscountType = 2 (Fixed Amount)
    /// </summary>
    public decimal? TierBonusAmount { get; set; }

    /// <summary>
    /// Status: 1=Draft, 2=Active, 3=Cancelled, 4=Modified
    /// </summary>
    public int Status { get; set; } = 1;
}
