# =============================================
# Entity Framework Migration Generator for Back Margin
# Created: 2025-01-14
# Description: Generates EF Core migration for Back Margin entities
# =============================================

param(
    [Parameter(Mandatory=$false)]
    [string]$ProjectPath = "Server\PurchaseManager.Storage",
    
    [Parameter(Mandatory=$false)]
    [string]$MigrationName = "AddBackMarginTables",
    
    [Parameter(Mandatory=$false)]
    [string]$StartupProject = "Server\PurchaseManager.Server",
    
    [Parameter(Mandatory=$false)]
    [switch]$WhatIf = $false
)

# Function to write colored output
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

Write-ColorOutput Cyan "=== Entity Framework Migration Generator ==="
Write-ColorOutput White "Project Path: $ProjectPath"
Write-ColorOutput White "Migration Name: $MigrationName"
Write-ColorOutput White "Startup Project: $StartupProject"
Write-ColorOutput White "What-If Mode: $WhatIf"
Write-ColorOutput White ""

# Check if dotnet CLI is available
try {
    $dotnetVersion = dotnet --version
    Write-ColorOutput Green "Found .NET CLI version: $dotnetVersion"
} catch {
    Write-ColorOutput Red "Error: .NET CLI not found. Please install .NET SDK."
    exit 1
}

# Check if Entity Framework tools are installed
try {
    $efVersion = dotnet ef --version
    Write-ColorOutput Green "Found Entity Framework tools: $efVersion"
} catch {
    Write-ColorOutput Yellow "Entity Framework tools not found. Installing..."
    if (-not $WhatIf) {
        dotnet tool install --global dotnet-ef
    } else {
        Write-ColorOutput Yellow "Would install: dotnet tool install --global dotnet-ef"
    }
}

# Verify project paths exist
if (-not (Test-Path $ProjectPath)) {
    Write-ColorOutput Red "Error: Project path not found: $ProjectPath"
    exit 1
}

if (-not (Test-Path $StartupProject)) {
    Write-ColorOutput Red "Error: Startup project path not found: $StartupProject"
    exit 1
}

# Generate migration commands
$commands = @(
    "# Navigate to project directory",
    "cd `"$ProjectPath`"",
    "",
    "# Add migration for ApplicationDb context",
    "dotnet ef migrations add $MigrationName --context ApplicationDbContext --startup-project `"..\..\$StartupProject`" --verbose",
    "",
    "# Update database (optional - uncomment if you want to apply immediately)",
    "# dotnet ef database update --context ApplicationDbContext --startup-project `"..\..\$StartupProject`" --verbose",
    "",
    "# Generate SQL script (optional - for review before applying)",
    "# dotnet ef migrations script --context ApplicationDbContext --startup-project `"..\..\$StartupProject`" --output `"BackMargin_Migration_$MigrationName.sql`""
)

if ($WhatIf) {
    Write-ColorOutput Yellow "=== WHAT-IF MODE: Commands that would be executed ==="
    foreach ($cmd in $commands) {
        if ($cmd.StartsWith("#")) {
            Write-ColorOutput Gray $cmd
        } elseif ([string]::IsNullOrWhiteSpace($cmd)) {
            Write-Output ""
        } else {
            Write-ColorOutput White $cmd
        }
    }
    Write-ColorOutput Yellow "=== END OF COMMANDS ==="
} else {
    Write-ColorOutput Green "Executing Entity Framework migration commands..."
    
    # Change to project directory
    Push-Location $ProjectPath
    
    try {
        # Add migration
        Write-ColorOutput Yellow "Adding migration: $MigrationName"
        $addMigrationCmd = "dotnet ef migrations add $MigrationName --context ApplicationDbContext --startup-project `"..\..\$StartupProject`" --verbose"
        Write-ColorOutput Gray "Executing: $addMigrationCmd"
        Invoke-Expression $addMigrationCmd
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput Green "Migration added successfully!"
            
            # Generate SQL script for review
            Write-ColorOutput Yellow "Generating SQL script for review..."
            $scriptCmd = "dotnet ef migrations script --context ApplicationDbContext --startup-project `"..\..\$StartupProject`" --output `"BackMargin_Migration_$MigrationName.sql`""
            Write-ColorOutput Gray "Executing: $scriptCmd"
            Invoke-Expression $scriptCmd
            
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput Green "SQL script generated: BackMargin_Migration_$MigrationName.sql"
            }
            
        } else {
            Write-ColorOutput Red "Failed to add migration. Exit code: $LASTEXITCODE"
        }
        
    } catch {
        Write-ColorOutput Red "Error executing migration commands: $($_.Exception.Message)"
    } finally {
        # Return to original directory
        Pop-Location
    }
}

Write-ColorOutput Cyan ""
Write-ColorOutput Cyan "=== Next Steps ==="
Write-ColorOutput White "1. Review the generated migration files in: $ProjectPath\Migrations\ApplicationDb\"
Write-ColorOutput White "2. Review the generated SQL script (if created)"
Write-ColorOutput White "3. Test the migration in a development environment first"
Write-ColorOutput White "4. Apply the migration to database:"
Write-ColorOutput Gray "   dotnet ef database update --context ApplicationDbContext --startup-project `"$StartupProject`""
Write-ColorOutput White ""
Write-ColorOutput White "5. Verify the new tables were created:"
Write-ColorOutput Gray "   - PromotionBackMarginTiers"
Write-ColorOutput Gray "   - PromotionBackMarginTrackings"
Write-ColorOutput Gray "   - PromotionBackMarginEarneds"
Write-ColorOutput Gray "   - PromotionBackMarginPayments"

Write-ColorOutput Cyan ""
Write-ColorOutput Cyan "=== Usage Examples ==="
Write-ColorOutput White "# Generate migration with default settings:"
Write-ColorOutput Gray ".\Generate_EF_Migration.ps1"
Write-ColorOutput White ""
Write-ColorOutput White "# Preview what will be executed:"
Write-ColorOutput Gray ".\Generate_EF_Migration.ps1 -WhatIf"
Write-ColorOutput White ""
Write-ColorOutput White "# Custom migration name:"
Write-ColorOutput Gray ".\Generate_EF_Migration.ps1 -MigrationName `"BackMarginSystem_v1`""
Write-ColorOutput White ""
Write-ColorOutput White "# Custom project paths:"
Write-ColorOutput Gray ".\Generate_EF_Migration.ps1 -ProjectPath `"Custom\Path\Storage`" -StartupProject `"Custom\Path\Server`""
