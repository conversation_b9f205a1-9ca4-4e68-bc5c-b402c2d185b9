﻿namespace PurchaseManager.Shared.Dto.Promotions.FrontMargins;

public class GetPromotionFrontMarginDto
{
    public string Number { get; set; } = null!;
    public string ProgramName { get; set; } = null!;

    // Header information (mapped from PromotionHeader)
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }

    // Helper properties for UI and display (mapped by AutoMapper)
    public string DiscountTypeName { get; set; } = string.Empty;
    public bool IsPercentageDiscount { get; set; }
    public bool IsFixedAmountDiscount { get; set; }
    public bool IsSameItemGift { get; set; }
    public bool IsDifferentItemGift { get; set; }
    public string ProgramNumber { get; set; } = null!;
    public int LineNumber { get; set; }
    public string ItemNumber { get; set; } = null!;
    public string ItemName { get; set; } = null!;
    public string UnitOfMeasure { get; set; } = null!;

    /// <summary>
    ///     - <PERSON><PERSON><PERSON> chiết khấu:
    ///     1 = Percentage
    ///     2 = FixedAmount
    ///     3 = SameItemGift
    ///     4 = DifferentItemGift
    /// </summary>
    public int DiscountType { get; set; } = 1;
    // CASE I.1: Percentage Discount
    public decimal DiscountPercentage { get; set; }
    // CASE I.2: Fixed Amount Discount
    public decimal FixedDiscountAmount { get; set; }
    // CASE II: Same Item Gift (Buy X Get Y Free)
    public decimal BuyQuantity { get; set; }
    public decimal GiftQuantity { get; set; }
    // CASE III: Different Item Gift
    public string? GiftItemNumber { get; set; }
    public string? GiftItemName { get; set; }
    public string? GiftItemUOM { get; set; }
    public decimal GiftItemQuantity { get; set; }
    // Conditions
    public decimal MinimumQuantity { get; set; }
    public decimal MinimumAmount { get; set; }
    public decimal MaximumDiscountAmount { get; set; }

    // Progressive/Tiered Discount Fields
    /// <summary>
    /// Quantity threshold for tier bonus (e.g., 100 units)
    /// </summary>
    public decimal? TierQuantityThreshold { get; set; }

    /// <summary>
    /// Additional percentage discount when tier threshold is met (e.g., 5%)
    /// Used with DiscountType = 1 (Percentage)
    /// </summary>
    public decimal? TierBonusPercentage { get; set; }

    /// <summary>
    /// Additional fixed amount discount when tier threshold is met (e.g., 2,000,000)
    /// Used with DiscountType = 2 (Fixed Amount)
    /// </summary>
    public decimal? TierBonusAmount { get; set; }

    /// <summary>
    /// Gift calculation type: 1=Progressive, 2=Milestone
    /// Only used for DiscountType = 3 (Same Item Gift) and 4 (Different Item Gift)
    /// Progressive: Gift quantity is proportional to buy quantity (Luỹ tiến)
    /// Milestone: Gift quantity is fixed when threshold is reached (Mốc)
    /// </summary>
    public int GiftCalculationType { get; set; }

    /// <summary>
    ///     1 = Active
    ///     2 = Inactive
    /// </summary>
    public int Status { get; set; } = 1;
    public string? Notes { get; set; }

    // Computed properties for display
    public string DisplayText => GetDisplayText();
    public string DiscountSummary => GetDiscountSummary();
    public bool HasValidConfiguration => ValidateConfiguration();

    /// <summary>
    /// Get display text for UI
    /// </summary>
    private string GetDisplayText()
    {
        var baseText = DiscountType switch
        {
            1 => $"{ItemName} - Chiết khấu {DiscountPercentage:N2}%",
            2 => $"{ItemName} - Chiết khấu {FixedDiscountAmount:N0} VND",
            3 => $"{ItemName} - Mua {BuyQuantity:N0} tặng {GiftQuantity:N0}",
            4 => $"{ItemName} - Tặng {GiftItemQuantity:N0} {GiftItemName}",
            _ => $"{ItemName} - Loại chiết khấu không xác định"
        };

        // Add tier information if applicable
        if (!HasTierBonus()) return baseText;
        var tierText = DiscountType switch
        {
            1 => $" + {TierBonusPercentage}% khi mua ≥{TierQuantityThreshold}",
            2 => $" + {TierBonusAmount:N0} VND khi mua ≥{TierQuantityThreshold}",
            _ => ""
        };
        baseText += tierText;

        return baseText;
    }

    /// <summary>
    /// Get discount summary for display
    /// </summary>
    private string GetDiscountSummary()
    {
        var baseSummary = DiscountType switch
        {
            1 => $"{DiscountPercentage:N2}% off",
            2 => $"{FixedDiscountAmount:N0} VND off",
            3 => $"Buy {BuyQuantity:N0} get {GiftQuantity:N0} free",
            4 => $"Free {GiftItemQuantity:N0} {GiftItemName}",
            _ => "Unknown discount"
        };

        // Add tier bonus summary
        if (!HasTierBonus()) return baseSummary;
        var tierSummary = DiscountType switch
        {
            1 => $" (+ {TierBonusPercentage}% if qty ≥ {TierQuantityThreshold})",
            2 => $" (+ {TierBonusAmount:N0} if qty ≥ {TierQuantityThreshold})",
            _ => ""
        };
        baseSummary += tierSummary;

        return baseSummary;
    }

    /// <summary>
    /// Check if this promotion has tier bonus
    /// </summary>
    private bool HasTierBonus()
    {
        return TierQuantityThreshold is > 0 &&
               (DiscountType == 1 && TierBonusPercentage is > 0 ||
                DiscountType == 2 && TierBonusAmount is > 0);
    }

    /// <summary>
    /// Validate configuration based on discount type
    /// </summary>
    private bool ValidateConfiguration()
    {
        return DiscountType switch
        {
            1 => DiscountPercentage is > 0 and <= 100,
            2 => FixedDiscountAmount > 0,
            3 => BuyQuantity > 0 &&
                 GiftQuantity > 0,
            4 => !string.IsNullOrEmpty(GiftItemNumber) &&
                 !string.IsNullOrEmpty(GiftItemName) &&
                 GiftItemQuantity > 0,
            _ => false
        };
    }
}
