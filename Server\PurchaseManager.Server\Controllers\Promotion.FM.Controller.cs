﻿using Microsoft.AspNetCore.Mvc;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using ClosedXML.Excel;
using PurchaseManager.Constants.Enum;

namespace PurchaseManager.Server.Controllers;

public partial class PromotionController
{
    [HttpPost("front-margin")]
    public async Task<ApiResponse> CreatePromotionFrontMargin([FromBody] CreatePromotionFrontMarginDto createDto)
    {
        return await _promotionService.CreatePromotionFrontMarginAsync(createDto);
    }

    [HttpPut("front-margin/{number}")]
    public async Task<ApiResponse> UpdatePromotionFrontMargin(string number, [FromBody] UpdatePromotionFrontMarginDto updateDto)
    {
        return await _promotionService.UpdatePromotionFrontMarginAsync(number, updateDto);
    }

    [HttpDelete("front-margin")]
    public async Task<ApiResponse> DeletePromotionFrontMargin([FromBody] List<string> numbers)
    {
        return await _promotionManager.DeletePromotionFrontMarginAsync(numbers);
    }

    [HttpGet("front-margin/template")]
    public IActionResult DownloadPromotionTemplate()
    {
        using var workbook = new XLWorkbook();
        var worksheet = workbook.Worksheets.Add("Promotion");

        // Headers with description
        var headers = new[]
        {
            "ItemNumber",
            "UnitOfMeasure",
            "DiscountType",
            "DiscountPercentage",
            "FixedDiscountAmount",
            "BuyQuantity",
            "GiftQuantity",
            "GiftItemNumber",
            "GiftItemUOM",
            "GiftItemQuantity",
            "MinimumQuantity",
            "MinimumAmount",
            "MaximumDiscountAmount",
            "TierQuantityThreshold",
            "TierBonusPercentage",
            "TierBonusAmount",
            "GiftCalculationType",
            "Notes"
        };

        // Header row with styling
        for (var i = 0; i < headers.Length; i++)
        {
            var cell = worksheet.Cell(1, i + 1);
            cell.Value = headers[i];
            cell.Style.Font.SetBold(true);
            cell.Style.Fill.BackgroundColor = XLColor.LightBlue;
            cell.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
        }

        // Instructions row
        var instructions = new[]
        {
            "Mã sản phẩm",
            "Đơn vị tính",
            "Loại KM (1=% CK, 2=Tiền CK, 3=Tặng cùng SP, 4=Tặng khác SP)",
            "% chiết khấu (cho type 1)",
            "Số tiền CK (cho type 2)",
            "Số lượng mua (cho type 3,4)",
            "Số lượng tặng (cho type 3)",
            "Mã SP tặng (cho type 4)",
            "ĐVT SP tặng (cho type 4)",
            "SL SP tặng (cho type 4)",
            "SL tối thiểu",
            "Giá trị tối thiểu",
            "Giá trị CK tối đa",
            "Ngưỡng tier",
            "% bonus tier",
            "Tiền bonus tier",
            "Loại tặng (1=Luỹ tiến, 2=Mốc)",
            "Ghi chú"
        };

        for (var i = 0; i < instructions.Length; i++)
        {
            var cell = worksheet.Cell(2, i + 1);
            cell.Value = instructions[i];
            cell.Style.Font.SetItalic(true);
            cell.Style.Font.FontSize = 9;
            cell.Style.Fill.BackgroundColor = XLColor.LightGray;
        }

        // Sample data for each case
        var sampleData = new[]
        {
            // Case 1: Percentage Discount - Basic
            new object[]
            {
                "ITEM001",
                "PCE",
                1,
                10.5,
                null,
                null,
                null,
                null,
                null,
                null,
                10,
                500000,
                null,
                null,
                null,
                null,
                1,
                "Chiết khấu 10.5% - cơ bản"
            },

            // Case 1: Percentage Discount - With Tier Bonus
            new object[]
            {
                "ITEM002",
                "BOX",
                1,
                8.0,
                null,
                null,
                null,
                null,
                null,
                null,
                5,
                300000,
                null,
                50,
                2.5,
                null,
                1,
                "CK 8% + bonus 2.5% khi mua >= 50 sản phẩm"
            },

            // Case 1: Percentage Discount - With Max Discount
            new object[]
            {
                "ITEM003",
                "SET",
                1,
                15.0,
                null,
                null,
                null,
                null,
                null,
                null,
                20,
                1000000,
                200000,
                null,
                null,
                null,
                1,
                "CK 15% tối đa 200,000 VND"
            },

            // Case 2: Fixed Amount Discount - Basic
            new object[]
            {
                "ITEM004",
                "PCE",
                2,
                null,
                50000,
                null,
                null,
                null,
                null,
                null,
                15,
                800000,
                null,
                null,
                null,
                null,
                1,
                "Chiết khấu cố định 50,000 VND"
            },

            // Case 2: Fixed Amount Discount - With Tier Bonus
            new object[]
            {
                "ITEM005",
                "BOX",
                2,
                null,
                100000,
                null,
                null,
                null,
                null,
                null,
                10,
                1500000,
                null,
                100,
                null,
                50000,
                1,
                "CK 100K + bonus 50K khi mua >= 100 sản phẩm"
            },

            // Case 3: Same Item Gift - Progressive
            new object[]
            {
                "ITEM006",
                "PCE",
                3,
                null,
                null,
                10,
                1,
                null,
                null,
                null,
                5,
                null,
                null,
                null,
                null,
                null,
                1,
                "Mua 10 tặng 1 cùng sản phẩm (luỹ tiến)"
            },

            // Case 3: Same Item Gift - Milestone
            new object[]
            {
                "ITEM007",
                "BOX",
                3,
                null,
                null,
                20,
                3,
                null,
                null,
                null,
                10,
                null,
                null,
                null,
                null,
                null,
                2,
                "Mua 20 tặng 3 cùng sản phẩm (theo mốc)"
            },

            // Case 4: Different Item Gift - Basic
            new object[]
            {
                "ITEM008",
                "PCE",
                4,
                null,
                null,
                5,
                null,
                "GIFT001",
                "PCE",
                1,
                3,
                null,
                null,
                null,
                null,
                null,
                1,
                "Mua 5 tặng 1 túi quà"
            },

            // Case 4: Different Item Gift - Multiple gifts
            new object[]
            {
                "ITEM009",
                "SET",
                4,
                null,
                null,
                12,
                null,
                "GIFT002",
                "BỘ",
                1,
                6,
                2000000,
                null,
                null,
                null,
                null,
                2,
                "Mua 12 set tặng 1 bộ ly sứ cao cấp"
            },

            // Case 4: Different Item Gift - Service voucher
            new object[]
            {
                "ITEM010",
                "PCE",
                4,
                null,
                null,
                8,
                null,
                "VOUCHER50K",
                "PHIẾU",
                2,
                5,
                1200000,
                null,
                null,
                null,
                null,
                1,
                "Mua 8 sản phẩm tặng 2 voucher 50K"
            },

            // Complex case: High-value item with multiple conditions
            new object[]
            {
                "ITEM011",
                "PCE",
                1,
                12.0,
                null,
                null,
                null,
                null,
                null,
                null,
                50,
                5000000,
                500000,
                200,
                5.0,
                null,
                1,
                "Sản phẩm cao cấp: CK 12% + 5% bonus, max 500K"
            },

            // Seasonal promotion example
            new object[]
            {
                "ITEM012",
                "BOX",
                2,
                null,
                150000,
                null,
                null,
                null,
                null,
                null,
                25,
                3000000,
                300000,
                100,
                null,
                75000,
                1,
                "Khuyến mãi mùa lễ: CK 150K + bonus 75K"
            },

            // Edge case test data for comprehensive testing
            new object[]
            {
                "ITEM_ĐẶC_BIỆT",
                "CÁI",
                1,
                0.01,
                null,
                null,
                null,
                null,
                null,
                null,
                1,
                1000,
                null,
                null,
                null,
                null,
                1,
                "Tối thiểu 0.01% discount"
            },
            new object[]
            {
                "ITEM@#$",
                "SET",
                1,
                100,
                null,
                null,
                null,
                null,
                null,
                null,
                10,
                500000,
                null,
                null,
                null,
                null,
                1,
                "Tối đa 100% discount với ký tự đặc biệt"
            },
            new object[]
            {
                "ITEM015",
                "KG",
                2,
                null,
                999999999,
                null,
                null,
                null,
                null,
                null,
                1,
                1000000,
                null,
                null,
                null,
                null,
                1,
                "Fixed discount cao nhất"
            },
            new object[]
            {
                "ITEM016",
                "PCE",
                3,
                null,
                null,
                1,
                1,
                null,
                null,
                null,
                1,
                null,
                null,
                null,
                null,
                null,
                1,
                "Mua 1 tặng 1 (100% ratio)"
            },
            new object[]
            {
                "商品001", "件", 4, null, null, 2, null, "GIFT_中文", "件", 1, 1, null, null, null, null, null, 1, "Unicode characters test"
            }
        };

        // Add sample data rows
        for (var i = 0; i < sampleData.Length; i++)
        {
            var rowIndex = i + 3;// Start from row 3 (after header and instructions)
            var rowData = sampleData[i];

            for (var j = 0; j < rowData.Length; j++)
            {
                var cell = worksheet.Cell(rowIndex, j + 1);

                // Handle null values and proper conversion
                if (rowData[j] != null)
                {
                    cell.Value = rowData[j].ToString();
                }

                // Apply different background colors for different discount types
                var discountType = (int)rowData[2];
                cell.Style.Fill.BackgroundColor = discountType switch
                {
                    (int)FrontMarginDiscountTypeTypeEnum.PercentageDiscount =>// Percentage
                        XLColor.LightGreen,
                    (int)FrontMarginDiscountTypeTypeEnum.FixedAmountDiscount =>// Fixed Amount
                        XLColor.LightYellow,
                    (int)FrontMarginDiscountTypeTypeEnum.SameItemGift =>// Same Item Gift
                        XLColor.LightBlue,
                    (int)FrontMarginDiscountTypeTypeEnum.DifferentItemGift =>// Different Item Gift
                        XLColor.LightPink,
                    _ => cell.Style.Fill.BackgroundColor
                };
            }
        }

        // Add legend
        var legendRow = sampleData.Length + 5;
        worksheet.Cell(legendRow, 1).Value = "Chú thích màu sắc:";
        worksheet.Cell(legendRow, 1).Style.Font.SetBold(true);

        worksheet.Cell(legendRow + 1, 1).Value = "Xanh lá: Chiết khấu %";
        worksheet.Cell(legendRow + 1, 1).Style.Fill.BackgroundColor = XLColor.LightGreen;

        worksheet.Cell(legendRow + 2, 1).Value = "Vàng: Chiết khấu số tiền";
        worksheet.Cell(legendRow + 2, 1).Style.Fill.BackgroundColor = XLColor.LightYellow;

        worksheet.Cell(legendRow + 3, 1).Value = "Xanh dương: Tặng cùng SP";
        worksheet.Cell(legendRow + 3, 1).Style.Fill.BackgroundColor = XLColor.LightBlue;

        worksheet.Cell(legendRow + 4, 1).Value = "Hồng: Tặng khác SP";
        worksheet.Cell(legendRow + 4, 1).Style.Fill.BackgroundColor = XLColor.LightPink;

        // Auto-fit columns
        worksheet.Columns().AdjustToContents();

        // Add border to data area (include all sample data rows)
        var dataRange = worksheet.Range(1, 1, sampleData.Length + 2, headers.Length);
        dataRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
        dataRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;

        using var stream = new MemoryStream();
        workbook.SaveAs(stream);
        stream.Position = 0;

        return File(stream.ToArray(),
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "Import_Promotion_Template.xlsx");
    }

    [HttpPost("front-margin/{promotionNumber}/import")]
    public async Task<ApiResponse> ImportPromotionFrontMargins(string promotionNumber, IFormFile file)
    {
        return await _promotionService.ImportPromotionFrontMarginsAsync(promotionNumber, file);
    }
}
