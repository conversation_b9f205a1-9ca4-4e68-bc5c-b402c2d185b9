using System.Net.Http.Json;
using Microsoft.Extensions.Logging;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Dto.Promotions;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
namespace PurchaseManager.Shared.Services;

/// <summary>
/// Service for Front Margin integration in UI components
/// </summary>
public interface IFrontMarginUIService
{
    /// <summary>
    /// Check if vendor has active Front Margin promotions
    /// </summary>
    Task<bool> HasActiveFrontMarginAsync(string vendorCode);

    /// <summary>
    /// Get Front Margin discount for PO line with business rules validation
    /// </summary>
    Task<FrontMarginDiscountInfo?> GetLineDiscountAsync(string vendorCode, string itemNumber, DateTime orderDate, decimal? orderValue = null);

    /// <summary>
    /// Get all applicable Front Margin promotions for vendor with priority ordering
    /// </summary>
    Task<List<FrontMarginPromotionInfo>> GetApplicablePromotionsAsync(string vendorCode, DateTime orderDate, decimal? orderValue = null);

    /// <summary>
    /// Preview Front Margin impact on entire PO with validation
    /// </summary>
    Task<FrontMarginPreviewResult> PreviewPOImpactAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines);

    /// <summary>
    /// Apply Front Margin to PO lines with auto-apply logic and priority handling
    /// </summary>
    Task<List<POLineGetDto>> ApplyFrontMarginToLinesAsync(List<POLineGetDto> lines, string vendorCode, DateTime orderDate);

    /// <summary>
    /// Validate PO against Front Margin business rules
    /// </summary>
    Task<FrontMarginValidationResult> ValidatePOAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines);

    /// <summary>
    /// Get Front Margin promotions by priority for conflict resolution
    /// </summary>
    Task<List<FrontMarginPromotionInfo>> GetPromotionsByPriorityAsync(string vendorCode, DateTime orderDate, decimal orderValue);
}

public class FrontMarginUIService : IFrontMarginUIService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<FrontMarginUIService> _logger;

    public FrontMarginUIService(HttpClient httpClient, ILogger<FrontMarginUIService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<bool> HasActiveFrontMarginAsync(string vendorCode)
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<ApiResponseDto<List<GetPromotionFrontMarginDto>>>(
                $"api/FrontMargin/available/{vendorCode}");

            return response?.IsSuccessStatusCode == true && response.Result?.Any() == true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking active Front Margin for vendor {VendorCode}", vendorCode);
            return false;
        }
    }

    public async Task<FrontMarginDiscountInfo?> GetLineDiscountAsync(string vendorCode, string itemNumber, DateTime orderDate, decimal? orderValue = null)
    {
        try
        {
            // Get promotions with header information for business rules validation
            var promotions = await GetApplicablePromotionsAsync(vendorCode, orderDate, orderValue);

            // Find the highest priority promotion for this item
            var applicablePromotion = promotions
                .Where(p => p.ItemNumber == itemNumber)
                .OrderBy(p => p.Priority) // Lower number = higher priority
                .FirstOrDefault();

            if (applicablePromotion != null)
            {
                return new FrontMarginDiscountInfo
                {
                    PromotionNumber = applicablePromotion.Number,
                    PromotionName = applicablePromotion.ProgramNumber,
                    DiscountType = applicablePromotion.DiscountType,
                    DiscountValue = applicablePromotion.DiscountValue,
                    DiscountPercentage = applicablePromotion.DiscountPercentage,
                    HasGifts = applicablePromotion.DiscountType == 3 || applicablePromotion.DiscountType == 4,
                    GiftQuantity = applicablePromotion.GiftQuantity,
                    GiftItemNumber = applicablePromotion.GiftItemNumber,
                    Priority = applicablePromotion.Priority,
                    AutoApply = applicablePromotion.AutoApply
                };
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Front Margin discount for {VendorCode}/{ItemNumber}", vendorCode, itemNumber);
            return null;
        }
    }

    public async Task<List<FrontMarginPromotionInfo>> GetApplicablePromotionsAsync(string vendorCode, DateTime orderDate, decimal? orderValue = null)
    {
        try
        {
            // Get promotions with header information
            var response = await _httpClient.GetFromJsonAsync<ApiResponseDto<List<GetPromotionHeaderDto>>>(
                $"api/Promotion/filtered?vendorCode={vendorCode}&programType=1&status=2");

            if (response?.IsSuccessStatusCode == true && response.Result != null)
            {
                var promotionInfos = new List<FrontMarginPromotionInfo>();

                foreach (var header in response.Result)
                {
                    // Check if promotion is active for the order date
                    if (header.StartDate <= orderDate && header.EndDate >= orderDate)
                    {

                        // Add each Front Margin line as a separate promotion info
                        foreach (var fm in header.FrontMargins)
                        {
                            promotionInfos.Add(new FrontMarginPromotionInfo
                            {
                                Number = fm.Number,
                                ProgramNumber = fm.ProgramNumber,
                                ItemNumber = fm.ItemNumber,
                                ItemName = fm.ItemName,
                                DiscountType = fm.DiscountType,
                                DiscountTypeName = GetDiscountTypeName(fm.DiscountType),
                                DiscountValue = GetDiscountValue(fm),
                                DiscountPercentage = CalculateDiscountPercentage(fm),
                                StartDate = header.StartDate, // From header
                                EndDate = header.EndDate, // From header
                                Status = fm.Status,

                                // Business Rules from Header (simplified)
                                Priority = header.Priority,
                                AutoApply = header.AutoApply,

                                // Gift Properties
                                GiftQuantity = fm.GiftQuantity,
                                GiftItemNumber = fm.GiftItemNumber
                            });
                        }
                    }
                }

                // Sort by priority (lower number = higher priority)
                return promotionInfos.OrderBy(p => p.Priority).ToList();
            }

            return [];
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting applicable promotions for vendor {VendorCode}", vendorCode);
            return [];
        }
    }

    public async Task<FrontMarginPreviewResult> PreviewPOImpactAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines)
    {
        try
        {
            var previewData = new
            {
                Header = poHeader,
                Lines = poLines
            };

            var response = await _httpClient.PostAsJsonAsync("api/POFrontMargin/preview", previewData);
            var result = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();

            if (result?.IsSuccessStatusCode == true)
            {
                // Parse the preview result
                return ParsePreviewResult(result.Result);
            }

            return new FrontMarginPreviewResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error previewing Front Margin impact for PO");
            return new FrontMarginPreviewResult();
        }
    }

    public async Task<List<POLineGetDto>> ApplyFrontMarginToLinesAsync(List<POLineGetDto> lines, string vendorCode, DateTime orderDate)
    {
        var updatedLines = new List<POLineGetDto>();

        foreach (var line in lines)
        {
            var updatedLine = new POLineGetDto
            {
                // Copy all properties
                LineNumber = line.LineNumber,
                ItemNumber = line.ItemNumber,
                Description = line.Description,
                Quantity = line.Quantity,
                UnitOfMeasure = line.UnitOfMeasure,
                UnitCost = line.UnitCost,
                UnitPrice = line.UnitPrice,
                Amount = line.Amount,
                DocumentType = line.DocumentType,
                LotNo = line.LotNo
            };

            // Try to apply Front Margin discount
            var discount = await GetLineDiscountAsync(vendorCode, line.ItemNumber, orderDate);
            if (discount != null)
            {
                // Apply discount
                if (discount.DiscountType == 1) // Percentage
                {
                    updatedLine.UnitCost = line.UnitCost * (1 - discount.DiscountPercentage / 100);
                }
                else if (discount.DiscountType == 2) // Fixed amount
                {
                    updatedLine.UnitCost = line.UnitCost - (discount.DiscountValue / line.Quantity);
                }

                updatedLine.Amount = updatedLine.UnitCost * updatedLine.Quantity;
            }

            updatedLines.Add(updatedLine);
        }

        return updatedLines;
    }

    public async Task<FrontMarginValidationResult> ValidatePOAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines)
    {
        var result = new FrontMarginValidationResult();

        try
        {
            var totalOrderValue = poLines.Sum(line => line.Amount);
            result.TotalOrderValue = totalOrderValue;

            var promotions = await GetApplicablePromotionsAsync(poHeader.BuyFromVendorNumber, poHeader.OrderDate, totalOrderValue);

            if (promotions.Any())
            {
                // Check for conflicting promotions (same priority)
                var conflictGroups = promotions.GroupBy(p => p.Priority).Where(g => g.Count() > 1);
                foreach (var group in conflictGroups)
                {
                    result.ConflictingPromotions.AddRange(group);
                    result.WarningMessages.Add($"Multiple promotions found with same priority {group.Key}");
                }

                // Calculate total discount
                decimal totalDiscount = 0;
                foreach (var line in poLines)
                {
                    var applicablePromotion = promotions
                        .Where(p => p.ItemNumber == line.ItemNumber)
                        .OrderBy(p => p.Priority)
                        .FirstOrDefault();

                    if (applicablePromotion != null)
                    {
                        var lineDiscount = CalculateLineDiscount(line, applicablePromotion);
                        totalDiscount += lineDiscount;
                    }
                }

                result.CalculatedDiscount = totalDiscount;

                // Note: MaxDiscountAmount validation removed - Front Margin is simple
                // Complex validation rules will be handled in Back Margin with PromotionCondition/PromotionReward
            }

            result.IsValid = !result.ErrorMessages.Any();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating PO against Front Margin rules");
            result.IsValid = false;
            result.ErrorMessages.Add("Validation failed due to system error");
        }

        return result;
    }

    public async Task<List<FrontMarginPromotionInfo>> GetPromotionsByPriorityAsync(string vendorCode, DateTime orderDate, decimal orderValue)
    {
        var promotions = await GetApplicablePromotionsAsync(vendorCode, orderDate, orderValue);
        return promotions.OrderBy(p => p.Priority).ToList();
    }

    #region Private Methods

    private static decimal CalculateLineDiscount(POLineGetDto line, FrontMarginPromotionInfo promotion)
    {
        return promotion.DiscountType switch
        {
            1 => line.Amount * (promotion.DiscountPercentage / 100), // Percentage
            2 => Math.Min(promotion.DiscountValue, line.Amount), // Fixed amount
            3 => CalculateGiftDiscount(line, promotion), // Same item gift
            4 => CalculateGiftDiscount(line, promotion), // Different item gift
            _ => 0
        };
    }

    private static decimal CalculateGiftDiscount(POLineGetDto line, FrontMarginPromotionInfo promotion)
    {
        // For gift promotions, calculate the value of free items
        if (promotion.GiftQuantity > 0)
        {
            var giftValue = line.UnitCost * promotion.GiftQuantity;
            return Math.Min(giftValue, line.Amount);
        }
        return 0;
    }

    private static decimal GetDiscountValue(GetPromotionFrontMarginDto fm)
    {
        return fm.DiscountType switch
        {
            1 => fm.DiscountPercentage, // Percentage discount
            2 => fm.FixedDiscountAmount,// Fixed amount discount
            3 => fm.GiftQuantity,// Same item gift quantity
            4 => fm.GiftItemQuantity,// Different item gift quantity
            _ => 0
        };
    }

    private static decimal CalculateDiscountPercentage(GetPromotionFrontMarginDto fm)
    {
        return fm.DiscountType switch
        {
            1 => fm.DiscountPercentage, // Already percentage
            2 => 0, // Fixed amount - percentage depends on item price
            3 => CalculateGiftDiscountPercentage(fm), // Same item gift
            4 => CalculateGiftDiscountPercentage(fm), // Different item gift
            _ => 0
        };
    }

    private static decimal CalculateGiftDiscountPercentage(GetPromotionFrontMarginDto fm)
    {
        if (fm.BuyQuantity > 0 && fm.GiftQuantity > 0)
        {
            var totalQuantity = fm.BuyQuantity + fm.GiftQuantity;
            return fm.GiftQuantity / totalQuantity * 100;
        }
        return 0;
    }

    private static string GetDiscountTypeName(int discountType)
    {
        return discountType switch
        {
            1 => "Percentage Discount",
            2 => "Fixed Amount Discount",
            3 => "Same Item Gift",
            4 => "Different Item Gift",
            _ => "Unknown"
        };
    }

    private static FrontMarginPreviewResult ParsePreviewResult(object? data)
    {
        // Parse the preview result from API response
        // This would depend on the actual structure returned by the API
        // For now, return empty result as placeholder
        return new FrontMarginPreviewResult
        {
            TotalOriginalAmount = 0,
            TotalDiscountAmount = 0,
            TotalFinalAmount = 0,
            DiscountPercentage = 0,
            AppliedPromotions = [],
            GiftLines = []
        };
    }

    #endregion
}

#region DTOs for UI

public class FrontMarginDiscountInfo
{
    public string PromotionNumber { get; set; } = string.Empty;
    public string PromotionName { get; set; } = string.Empty;
    public int DiscountType { get; set; }
    public decimal DiscountValue { get; set; }
    public decimal DiscountPercentage { get; set; }
    public bool HasGifts { get; set; }
    public decimal GiftQuantity { get; set; }
    public string? GiftItemNumber { get; set; }

    // Minimal Business Rules Properties from PromotionHeader
    public int Priority { get; set; } = 1;
    public bool AutoApply { get; set; } = true;
}

public class FrontMarginPromotionInfo
{
    public string Number { get; set; } = string.Empty;
    public string ProgramNumber { get; set; } = string.Empty;
    public string ItemNumber { get; set; } = string.Empty;
    public string ItemName { get; set; } = string.Empty;
    public int DiscountType { get; set; }
    public string DiscountTypeName { get; set; } = string.Empty;
    public decimal DiscountValue { get; set; }
    public decimal DiscountPercentage { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int Status { get; set; }

    // Minimal Business Rules Properties from PromotionHeader
    public int Priority { get; set; } = 1;
    public bool AutoApply { get; set; } = true;

    // Gift Properties (for Front Margin types 3 & 4)
    public decimal GiftQuantity { get; set; }
    public string? GiftItemNumber { get; set; }
}

public class FrontMarginPreviewResult
{
    public decimal TotalOriginalAmount { get; set; }
    public decimal TotalDiscountAmount { get; set; }
    public decimal TotalFinalAmount { get; set; }
    public decimal DiscountPercentage { get; set; }
    public List<string> AppliedPromotions { get; set; } = [];
    public List<POLineGetDto> GiftLines { get; set; } = [];
    public bool IsValid { get; set; } = true;
    public List<string> ValidationMessages { get; set; } = [];
}

public class FrontMarginValidationResult
{
    public bool IsValid { get; set; } = true;
    public List<string> ErrorMessages { get; set; } = [];
    public List<string> WarningMessages { get; set; } = [];
    public decimal TotalOrderValue { get; set; }
    public decimal MaxAllowedDiscount { get; set; }
    public decimal CalculatedDiscount { get; set; }
    public bool ExceedsMaxDiscount { get; set; }
    public bool BelowMinOrderValue { get; set; }
    public bool ExceedsMaxOrderValue { get; set; }
    public List<FrontMarginPromotionInfo> ConflictingPromotions { get; set; } = [];
}

#endregion
