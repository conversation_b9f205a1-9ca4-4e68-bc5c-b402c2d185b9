namespace PurchaseManager.Shared.Extensions;

public static class ConvertDataTypeExtension
{
    public static string ToInteger(this decimal value)
    {
        return value.ToString("N0");
    }

    public static int ToInt(this decimal value)
    {
        return (int)Math.Round(value, MidpointRounding.AwayFromZero);
    }

    public static string ToD2(this decimal value)
    {
        return value.ToString("N2");
    }
}
