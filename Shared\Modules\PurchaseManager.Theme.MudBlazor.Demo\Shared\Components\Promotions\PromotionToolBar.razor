@inject IStringLocalizer<Global> L
@inject IJSRuntime JsRuntime

<MudToolBar Gutters="false" Dense Class="mb-3">
    <MudIconButton Color="Color.Default" Icon="@Icons.Material.Filled.ArrowBack"
                   OnClick="@(async () => { await JsRuntime.InvokeVoidAsync("history.back"); })"
                   Variant="Variant.Text">
    </MudIconButton>
    <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.Center">
        <MudText Typo="Typo.h6">@Title</MudText>
    </MudStack>
    <MudSpacer/>

    @* Save/Edit Button *@
    <MudButton Size="Size.Small" OnClick="@OnSaveOrEdit" Class="mr-6"
               Variant="Variant.Filled" Color="Color.Primary">
        @if (IsEdit)
        {
            @L["Save"]
        }
        else
        { 
            @("Open")
        }
    </MudButton>

    <MudButtonGroup Size="Size.Small" Variant="Variant.Outlined">
        @if (ShowEmailButton)
        {
            <AuthorizeView Policy="@(Policies.IsPurchaseUser)">
                <MudIconButton Icon="@Icons.Material.Outlined.Email"
                               Disabled="@EmailButtonDisabled"
                               Color="Color.Default" Size="Size.Small"
                               OnClick="@OnEmail">
                    Email
                </MudIconButton>
            </AuthorizeView>
        }

        @if (ShowPrintButton)
        {
            <MudIconButton Icon="@Icons.Material.Outlined.Print" Color="Color.Default" Size="Size.Small"
                           OnClick="@OnPrint"
                           Disabled="@PrintButtonDisabled">
                Print
            </MudIconButton>
        }

        @if (ShowExportButton)
        {
            <MudTooltip Placement="Placement.Bottom" Text="@ExportTooltipText">
                <MudIconButton Icon="@Icons.Custom.FileFormats.FileExcel" Color="Color.Default" Size="Size.Small"
                               OnClick="@OnExport"
                               Disabled="@ExportButtonDisabled">
                    Export
                </MudIconButton>
            </MudTooltip>
        }
    </MudButtonGroup>
</MudToolBar>

@code {
    [Parameter] public string Title { get; set; } = "Promotion";
    [Parameter] public bool IsEdit { get; set; } 

    // Button states
    [Parameter] public bool EmailButtonDisabled { get; set; } 
    [Parameter] public bool PrintButtonDisabled { get; set; } 
    [Parameter] public bool ExportButtonDisabled { get; set; } 

    // Button visibility
    [Parameter] public bool ShowEmailButton { get; set; } = true;
    [Parameter] public bool ShowPrintButton { get; set; } = true;
    [Parameter] public bool ShowExportButton { get; set; } = true;

    // Button texts and tooltips
    [Parameter] public string ExportTooltipText { get; set; } = "Export Promotion Detail To Excel";

    // Event callbacks
    [Parameter] public EventCallback OnSaveOrEdit { get; set; }
    [Parameter] public EventCallback<int> OnStatusChanged { get; set; }
    [Parameter] public EventCallback OnEmail { get; set; }
    [Parameter] public EventCallback OnPrint { get; set; }
    [Parameter] public EventCallback OnExport { get; set; }
}
