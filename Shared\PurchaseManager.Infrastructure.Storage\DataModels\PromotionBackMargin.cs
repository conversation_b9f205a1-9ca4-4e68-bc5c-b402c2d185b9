using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;
namespace PurchaseManager.Infrastructure.Storage.DataModels;

/// <summary>
/// Back Margin promotion configuration entity
/// Supports 5 discount types: Sales Progressive, Sales Tiered, Quantity Progressive, Quantity Tiered, Early Payment
/// </summary>
[Table("PromotionBackMargins")]
public class PromotionBackMargin : FullTrackingEntity
{
    /// <summary>
    /// Program number from PromotionHeader
    /// </summary>
    [Required]
    [StringLength(50)]
    public string ProgramNumber { get; set; } = string.Empty;

    /// <summary>
    /// Line number for multiple items in same program
    /// </summary>
    [Required]
    public int LineNumber { get; set; }

    /// <summary>
    /// Item number (can be null for program-level promotions)
    /// </summary>
    [StringLength(50)]
    public string? ItemNumber { get; set; }

    /// <summary>
    /// Item name (can be null for program-level promotions)
    /// </summary>
    [StringLength(250)]
    public string? ItemName { get; set; }

    /// <summary>
    /// Unit of measure (can be null for program-level promotions)
    /// </summary>
    [StringLength(50)]
    public string? UnitOfMeasure { get; set; }

    /// <summary>
    /// Back Margin discount type:
    /// 1 = RevenueProgressive (Doanh số lũy tiến)
    /// 2 = RevenueStepwise (Doanh số bậc thang)
    /// 3 = QuantityProgressive (Số lượng lũy tiến)
    /// 4 = QuantityStepwise (Số lượng bậc thang)
    /// 5 = EarlyPayment (Chiết khấu thanh toán sớm)
    /// </summary>
    [Required]
    public int DiscountType { get; set; }

    /// <summary>
    /// Support type number (FK to PromotionBackMarginSupportType)
    /// Maps to "Loại hỗ trợ" in requirements
    /// </summary>
    [StringLength(50)]
    public string? SupportTypeNumber { get; set; }

    /// <summary>
    /// Payment method:
    /// 1 = Cash (Tiền mặt)
    /// 2 = Transfer (Chuyển khoản)
    /// 3 = DebtOffset (Trừ công nợ)
    /// 4 = Goods (Hàng)
    /// </summary>
    [Required]
    public int PaymentMethod { get; set; } = 1;

    /// <summary>
    /// Payment timing:
    /// 1 = NextOrder (Đơn hàng tiếp theo)
    /// 2 = AfterPayment (Sau khi thanh toán đơn hàng)
    /// 3 = Plus30Days (n + 30 ngày)
    /// 4 = Plus60Days (n + 60 ngày)
    /// 5 = Plus90Days (n + 90 ngày)
    /// </summary>
    [Required]
    public int PaymentTiming { get; set; } = 1;

    /// <summary>
    /// Discount value type: 1=Percentage, 2=FixedAmount
    /// </summary>
    [Required]
    public int DiscountValueType { get; set; } = 1;

    /// <summary>
    /// For Early Payment: Number of days from delivery to payment
    /// For others: Not used
    /// </summary>
    public int? EarlyPaymentDays { get; set; }

    /// <summary>
    /// For Early Payment: Discount percentage
    /// For others: Not used (use tiers instead)
    /// </summary>
    [Column(TypeName = "decimal(5,2)")]
    public decimal? EarlyPaymentDiscountPercentage { get; set; }

    /// <summary>
    /// Status: 1=Active, 2=Inactive
    /// </summary>
    [Required]
    public int Status { get; set; } = 1;

    /// <summary>
    /// Notes/Description
    /// </summary>
    [StringLength(500)]
    public string? Notes { get; set; }

    // Navigation Properties
    [ForeignKey(nameof(ProgramNumber))]
    public virtual PromotionHeader PromotionHeader { get; set; } = null!;

    [ForeignKey(nameof(SupportTypeNumber))]
    public virtual PromotionBackMarginSupportType? SupportType { get; set; }

    /// <summary>
    /// Tiers for progressive/stepwise calculations
    /// </summary>
    public virtual ICollection<PromotionBackMarginTier> Tiers { get; set; } = new List<PromotionBackMarginTier>();
}
