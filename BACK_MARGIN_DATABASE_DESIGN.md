# Back Margin Database Schema Design

## Tổ<PERSON> quan
Database schema cho Back Margin system đượ<PERSON> thiết kế để tận dụng `PromotionHeader` hiện có và thêm các bảng chuyên biệt cho Back Margin. Hệ thống hỗ trợ 5 loại chiết khấu chính và tracking đầy đủ từ khi tạo chương trình đến khi trả thưởng.

## Cấu trúc bảng

### 1. PromotionHeader (Tái sử dụng)
- **Mụ<PERSON> đích**: <PERSON><PERSON><PERSON><PERSON> lý chung cho cả Front Margin và Back Margin
- **Trường quan trọng**: 
  - `ProgramType`: 1=FrontMargin, 2=BackMargin
  - `AccumulateRevenue`: C<PERSON> tích lũy doanh số qua các chu kỳ không
  - `SupportTypeNumber`: FK đến PromotionBackMarginSupportType

### 2. PromotionBackMargin (Mới)
- **<PERSON><PERSON><PERSON>**: <PERSON><PERSON><PERSON> hình chi tiết chương trình Back Margin
- **Trường chính**:
  - `DiscountType`: 1=RevenueProgressive, 2=RevenueStepwise, 3=QuantityProgressive, 4=QuantityStepwise, 5=EarlyPayment
  - `PaymentMethod`: 1=Cash, 2=Transfer, 3=DebtOffset, 4=Goods
  - `PaymentTiming`: 1=NextOrder, 2=AfterPayment, 3=Plus30Days, 4=Plus60Days, 5=Plus90Days
  - `EarlyPaymentDays`: Số ngày thanh toán sớm (chỉ cho EarlyPayment type)

### 3. PromotionBackMarginTier (Mới)
- **Mục đích**: Lưu các mốc/bậc thang cho tính toán Back Margin
- **Trường chính**:
  - `TierLevel`: Thứ tự bậc (1, 2, 3...)
  - `MinimumThreshold`/`MaximumThreshold`: Ngưỡng min/max cho bậc thang
  - `MilestoneValue`: Giá trị mốc cho lũy tiến
  - `DiscountPercentage`/`FixedDiscountAmount`: Mức chiết khấu
  - `TierType`: "PROGRESSIVE" hoặc "TIERED"

### 4. PromotionBackMarginTracking (Mới)
- **Mục đích**: Tracking doanh số/số lượng theo thời gian
- **Trường chính**:
  - `PONumber`/`POLineNumber`: Liên kết với PO
  - `Quantity`/`TotalAmount`: Số lượng và giá trị giao dịch
  - `AccumulatedQuantity`/`AccumulatedAmount`: Tích lũy đến thời điểm hiện tại
  - `PaymentDate`/`PaymentDays`: Cho tính toán thanh toán sớm
  - `ProcessingStatus`: 1=Pending, 2=Processed, 3=Paid

### 5. PromotionBackMarginEarned (Mới)
- **Mục đích**: Lưu Back Margin đã đạt điều kiện chờ trả thưởng
- **Trường chính**:
  - `PeriodStartDate`/`PeriodEndDate`: Chu kỳ tính toán
  - `TotalQuantity`/`TotalAmount`: Tổng đạt được trong chu kỳ
  - `EarnedAmount`: Số tiền Back Margin được tính
  - `CalculationFormula`/`CalculationDetails`: Chi tiết tính toán
  - `Status`: 1=Calculated, 2=Approved, 3=Paid, 4=Cancelled

### 6. PromotionBackMarginPayment (Mới)
- **Mục đích**: Tracking việc trả thưởng Back Margin
- **Trường chính**:
  - `PaymentDate`/`PaymentAmount`: Ngày và số tiền trả
  - `PaymentMethod`: Hình thức trả (Cash/Transfer/DebtOffset/Goods)
  - `PaymentReference`: Mã tham chiếu giao dịch
  - `PaymentStatus`: 1=Pending, 2=Completed, 3=Failed, 4=Cancelled
  - `ApprovalStatus`: 1=Pending, 2=Approved, 3=Rejected

## Công thức tính toán

### 1. Doanh số lũy tiến (RevenueProgressive)
```
Back Margin = Phần nguyên của (Doanh số mua / Doanh số mốc) × Mức chiết khấu
```

### 2. Doanh số bậc thang (RevenueStepwise)
```
Nếu Doanh số tối thiểu < Doanh số đạt < Doanh số tối đa
=> Back Margin = Doanh số đạt × Mức chiết khấu của bậc
```

### 3. Số lượng lũy tiến (QuantityProgressive)
```
Back Margin = Phần nguyên của (Số lượng mua / Số lượng mốc) × Mức chiết khấu
```

### 4. Số lượng bậc thang (QuantityStepwise)
```
Nếu Số lượng tối thiểu < Số lượng đạt < Số lượng tối đa
=> Back Margin = Số lượng đạt × Mức chiết khấu của bậc
```

### 5. Chiết khấu thanh toán sớm (EarlyPayment)
```
Nếu Ngày thanh toán <= Ngày nhận hàng + Số ngày quy định
=> Back Margin = Doanh số × Tỷ lệ chiết khấu
```

## Workflow

1. **Tạo chương trình**: PromotionHeader + PromotionBackMargin + PromotionBackMarginTier
2. **Tracking**: Mỗi PO tạo record trong PromotionBackMarginTracking
3. **Tính toán**: Định kỳ tính toán và tạo PromotionBackMarginEarned
4. **Trả thưởng**: Tạo PromotionBackMarginPayment khi trả thưởng
5. **Tích hợp PO**: Trong PO có thể chọn các PromotionBackMarginEarned để trả thưởng

## Enum mới được thêm

- `BackMarginPaymentMethodEnum`: Hình thức chi trả
- `BackMarginPaymentTimingEnum`: Thời điểm chi trả  
- `BackMarginTrackingStatusEnum`: Trạng thái tracking
- `BackMarginEarnedStatusEnum`: Trạng thái earned
- `BackMarginPaymentStatusEnum`: Trạng thái thanh toán
- `BackMarginPaymentApprovalStatusEnum`: Trạng thái phê duyệt

## Lưu ý kỹ thuật

1. **Tận dụng PromotionHeader**: Không cần tạo bảng header riêng cho Back Margin
2. **Flexible Design**: Hỗ trợ cả program-level và item-level Back Margin
3. **Audit Trail**: Đầy đủ tracking từ tạo đến trả thưởng
4. **Performance**: Index trên VendorCode, ProgramNumber, TransactionDate
5. **Data Integrity**: Foreign key constraints đảm bảo tính nhất quán
