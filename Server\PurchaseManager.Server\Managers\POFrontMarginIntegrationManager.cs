using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Server.Services.Promotions.Interface;
using PurchaseManager.Shared.Dto.MarginDto.FrontMargin.ResultServices;
using PurchaseManager.Shared.Dto.PO;
namespace PurchaseManager.Server.Managers;

public class POFrontMarginIntegrationManager : IPOFrontMarginIntegrationManager
{
    private readonly IFrontMarginIntegrationService _integrationService;
    private readonly ILogger<POFrontMarginIntegrationManager> _logger;
    public POFrontMarginIntegrationManager(ILogger<POFrontMarginIntegrationManager> logger,
        IFrontMarginIntegrationService integrationService)
    {
        _logger = logger;
        _integrationService = integrationService;
    }

    public async Task<ApiResponse> ApplyFrontMarginToPOAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines)
    {
        try
        {
            _logger.LogInformation("Applying Front Margin to PO {PONumber} for vendor {VendorCode}",
            poHeader.Number, poHeader.BuyFromVendorNumber);

            // Validate PO first
            var validation = await _integrationService.ValidatePOForFrontMarginAsync(poHeader, poLines);
            if (!validation.IsValid)
            {
                return ApiResponse.S400("PO validation failed");
            }

            // Apply Front Margin
            var result = await _integrationService.ApplyFrontMarginToPOAsync(poHeader, poLines);

            if (!result.Success)
            {
                return ApiResponse.S400(result.Message);
            }

            // Log the application for audit
            await LogFrontMarginApplicationAsync(poHeader.Number, result);

            return ApiResponse.S200("Front Margin applied successfully to PO", new
            {
                PONumber = poHeader.Number,
                OriginalAmount = result.TotalOriginalAmount,
                FinalAmount = result.TotalFinalAmount,
                result.TotalSavings,
                DiscountPercentage = result.TotalDiscountPercentage,
                UpdatedLines = result.UpdatedPOLines.Count,
                GiftLines = result.GiftLines.Count,
                AppliedPromotions = result.AppliedPromotions.Count,
                Details = new
                {
                    result.UpdatedPOLines, result.GiftLines, result.AppliedPromotions
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying Front Margin to PO {PONumber}", poHeader.Number);
            return ApiResponse.S500($"Error applying Front Margin: {ex.Message}");
        }
    }

    public async Task<ApiResponse> PreviewFrontMarginImpactAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines)
    {
        try
        {
            _logger.LogInformation("Previewing Front Margin impact for PO {PONumber}", poHeader.Number);

            var preview = await _integrationService.PreviewFrontMarginImpactAsync(poHeader, poLines);

            if (!string.IsNullOrEmpty(preview.ErrorMessage))
            {
                return ApiResponse.S400(preview.ErrorMessage);
            }

            return ApiResponse.S200("Front Margin impact preview generated", new
            {
                preview.PONumber,
                preview.VendorCode,
                Summary = new
                {
                    preview.TotalOriginalAmount,
                    preview.TotalProjectedSavings,
                    preview.ProjectedDiscountPercentage,
                    preview.LinesWithPromotions,
                    preview.TotalApplicablePromotions
                },
                LineDetails = preview.LineImpacts,
                Recommendations = GenerateRecommendations(preview)
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error previewing Front Margin impact for PO {PONumber}", poHeader.Number);
            return ApiResponse.S500($"Error previewing impact: {ex.Message}");
        }
    }

    public async Task<ApiResponse> ValidatePOForFrontMarginAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines)
    {
        try
        {
            var validation = await _integrationService.ValidatePOForFrontMarginAsync(poHeader, poLines);

            return ApiResponse.S200("PO validation completed", new
            {
                validation.IsValid,
                validation.ValidationMessages,
                validation.Warnings,
                CanApplyFrontMargin = validation.IsValid,
                RecommendedActions = GenerateValidationRecommendations(validation)
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating PO {PONumber} for Front Margin", poHeader.Number);
            return ApiResponse.S500($"Error validating PO: {ex.Message}");
        }
    }

    public Task<ApiResponse> RemoveFrontMarginFromPOAsync(string poNumber)
    {
        try
        {
            _logger.LogInformation("Removing Front Margin from PO {PONumber}", poNumber);

            // This would need to get the current PO data first
            // For now, return a placeholder response
            return Task.FromResult(ApiResponse.S200($"Front Margin removed from PO {poNumber}", new
            {
                PONumber = poNumber, RemovedSavings = 0m, RestoredLines = 0, RemovedGiftLines = 0
            }));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing Front Margin from PO {PONumber}", poNumber);
            return Task.FromResult(ApiResponse.S500($"Error removing Front Margin: {ex.Message}"));
        }
    }

    public Task<ApiResponse> GetPOFrontMarginSummaryAsync(string poNumber)
    {
        try
        {
            // This would query the PO and its Front Margin applications
            // For now, return a placeholder response
            var summary = new
            {
                PONumber = poNumber,
                HasFrontMargin = false,
                TotalSavings = 0m,
                DiscountPercentage = 0m,
                AppliedPromotions = 0,
                GiftLines = 0,
                LastApplied = (DateTime?)null,
                AppliedBy = (string?)null
            };

            return Task.FromResult(ApiResponse.S200($"Front Margin summary for PO {poNumber}", summary));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Front Margin summary for PO {PONumber}", poNumber);
            return Task.FromResult(ApiResponse.S500($"Error getting summary: {ex.Message}"));
        }
    }

    public Task<ApiResponse> RecalculateFrontMarginAsync(string poNumber)
    {
        try
        {
            _logger.LogInformation("Recalculating Front Margin for PO {PONumber}", poNumber);

            // This would get current PO data and recalculate
            // For now, return a placeholder response
            return Task.FromResult(ApiResponse.S200($"Front Margin recalculated for PO {poNumber}", new
            {
                PONumber = poNumber,
                RecalculatedAt = DateTime.UtcNow,
                PreviousSavings = 0m,
                NewSavings = 0m,
                SavingsDifference = 0m
            }));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recalculating Front Margin for PO {PONumber}", poNumber);
            return Task.FromResult(ApiResponse.S500($"Error recalculating: {ex.Message}"));
        }
    }

    public async Task<ApiResponse> GetVendorFrontMarginStatsAsync(string vendorCode, DateTime? fromDate = null,
        DateTime? toDate = null)
    {
        try
        {
            var history = await _integrationService.GetUsageHistoryAsync(vendorCode, fromDate, toDate);

            var stats = new
            {
                VendorCode = vendorCode,
                Period = new
                {
                    FromDate = fromDate ?? DateTime.Now.AddMonths(-3), ToDate = toDate ?? DateTime.Now
                },
                Summary = new
                {
                    TotalPOs = history.Count,
                    TotalOriginalAmount = history.Sum(h => h.OriginalAmount),
                    TotalSavings = history.Sum(h => h.DiscountAmount),
                    AverageDiscountPercentage = history.Count > 0 ?
                        history.Average(h => h.OriginalAmount > 0 ? h.DiscountAmount / h.OriginalAmount * 100 : 0) : 0,
                    TotalPromotionsUsed = history.Sum(h => h.PromotionsUsed)
                },
                RecentActivity = history.OrderByDescending(h => h.PODate)
                    .Take(10),
                MonthlyTrends = history
                    .GroupBy(h => new
                    {
                        h.PODate.Year, h.PODate.Month
                    })
                    .Select(g => new
                    {
                        g.Key.Year, g.Key.Month, POCount = g.Count(), TotalSavings = g.Sum(h => h.DiscountAmount)
                    })
                    .OrderBy(m => m.Year)
                    .ThenBy(m => m.Month)
            };

            return ApiResponse.S200($"Front Margin statistics for vendor {vendorCode}", stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Front Margin stats for vendor {VendorCode}", vendorCode);
            return ApiResponse.S500($"Error getting statistics: {ex.Message}");
        }
    }

    public async Task<ApiResponse> AutoApplyFrontMarginAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines)
    {
        try
        {
            _logger.LogInformation("Auto-applying Front Margin to PO {PONumber}", poHeader.Number);

            // Check if auto-apply is enabled for this vendor
            var autoApplyEnabled = await IsAutoApplyEnabledAsync(poHeader.BuyFromVendorNumber);

            if (!autoApplyEnabled)
            {
                return ApiResponse.S200("Auto-apply is disabled for this vendor", new
                {
                    PONumber = poHeader.Number, AutoApplyEnabled = false, RequiresManualApplication = true
                });
            }

            // Apply Front Margin automatically
            var result = await ApplyFrontMarginToPOAsync(poHeader, poLines);

            if (result.IsSuccessStatusCode)
            {
                _logger.LogInformation("Front Margin auto-applied successfully to PO {PONumber}", poHeader.Number);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error auto-applying Front Margin to PO {PONumber}", poHeader.Number);
            return ApiResponse.S500($"Error auto-applying Front Margin: {ex.Message}");
        }
    }

    #region Private Methods
    private Task LogFrontMarginApplicationAsync(string poNumber, POIntegrationResult result)
    {
        try
        {
            // This would log to an audit table
            _logger.LogInformation("Front Margin applied to PO {PONumber}: {Savings:N0} VND savings ({Percentage:F2}%)",
            poNumber, result.TotalSavings, result.TotalDiscountPercentage);
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to log Front Margin application for PO {PONumber}", poNumber);
            return Task.CompletedTask;
        }
    }

    private List<string> GenerateRecommendations(POIntegrationPreview preview)
    {
        var recommendations = new List<string>();

        if (preview.TotalProjectedSavings == 0)
        {
            recommendations.Add("No Front Margin promotions available for this PO");
        }
        else if (preview.ProjectedDiscountPercentage > 20)
        {
            recommendations.Add("Excellent savings opportunity! Consider applying Front Margin");
        }
        else if (preview.ProjectedDiscountPercentage > 10)
        {
            recommendations.Add("Good savings available with Front Margin");
        }
        else
        {
            recommendations.Add("Modest savings available with Front Margin");
        }

        if (preview.LinesWithPromotions < preview.LineImpacts.Count / 2)
        {
            recommendations.Add("Consider reviewing item selection for better promotion coverage");
        }

        return recommendations;
    }

    private static List<string> GenerateValidationRecommendations(POValidationResult validation)
    {
        var recommendations = new List<string>();

        if (!validation.IsValid)
        {
            recommendations.Add("Fix validation errors before applying Front Margin");
        }

        if (validation.Warnings.Count > 0)
        {
            recommendations.Add("Review warnings to optimize Front Margin benefits");
        }

        return recommendations;
    }

    private static Task<bool> IsAutoApplyEnabledAsync(string vendorCode)
    {
        // This would check vendor settings or system configuration
        // For now, return true as default
        return Task.FromResult(true);
    }
    #endregion
}
