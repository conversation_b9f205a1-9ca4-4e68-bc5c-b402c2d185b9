using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;
using PurchaseManager.Server.Factories;
using PurchaseManager.Server.Services.Promotions.Interface;
using PurchaseManager.Storage;
namespace PurchaseManager.Server.Services.Promotions;

public class FrontMarginCacheService : IFrontMarginCacheService
{
    private readonly ApplicationDbContext _context;
    private readonly IMemoryCache _cache;
    private readonly ILogger<FrontMarginCacheService> _logger;
    
    private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(30);
    private const string CacheKeyPrefix = "FrontMargin_";
    private const string VendorCacheKey = "Vendor_";
    private const string ItemCacheKey = "Item_";
    private readonly IDbContextFactory _dbContextFactory;
    private static readonly object _lockObject = new object();
    private static int _cacheHits = 0;
    private static int _cacheMisses = 0;

    public FrontMarginCacheService(
        ApplicationDbContext context,
        IMemoryCache cache,
        ILogger<FrontMarginCacheService> logger, IDbContextFactory dbContextFactory)
    {
        _context = context;
        _cache = cache;
        _logger = logger;
        _dbContextFactory = dbContextFactory;
    }

    public async Task<List<PromotionFrontMargin>> GetActivePromotionsAsync(string vendorCode)
    {
        var cacheKey = $"{CacheKeyPrefix}{VendorCacheKey}{vendorCode}";
        
        if (_cache.TryGetValue(cacheKey, out List<PromotionFrontMargin>? cachedPromotions))
        {
            Interlocked.Increment(ref _cacheHits);
            _logger.LogDebug("Cache hit for vendor {VendorCode}", vendorCode);
            return cachedPromotions ?? new List<PromotionFrontMargin>();
        }

        Interlocked.Increment(ref _cacheMisses);
        _logger.LogDebug("Cache miss for vendor {VendorCode}, loading from database", vendorCode);

        var promotions = await LoadActivePromotionsFromDatabaseAsync(vendorCode);
        
        var cacheOptions = new MemoryCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = _cacheExpiration,
            SlidingExpiration = TimeSpan.FromMinutes(10),
            Priority = CacheItemPriority.Normal
        };

        _cache.Set(cacheKey, promotions, cacheOptions);
        
        return promotions;
    }

    public async Task<List<PromotionFrontMargin>> GetPromotionsForItemAsync(string vendorCode, string itemNumber)
    {
        var cacheKey = $"{CacheKeyPrefix}{ItemCacheKey}{vendorCode}_{itemNumber}";
        
        if (_cache.TryGetValue(cacheKey, out List<PromotionFrontMargin>? cachedPromotions))
        {
            Interlocked.Increment(ref _cacheHits);
            return cachedPromotions ?? new List<PromotionFrontMargin>();
        }

        Interlocked.Increment(ref _cacheMisses);

        var promotions = await LoadPromotionsForItemFromDatabaseAsync(vendorCode, itemNumber);
        
        var cacheOptions = new MemoryCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = _cacheExpiration,
            SlidingExpiration = TimeSpan.FromMinutes(5),
            Priority = CacheItemPriority.High // Item-specific queries are high priority
        };

        _cache.Set(cacheKey, promotions, cacheOptions);
        
        return promotions;
    }

    public void ClearVendorCache(string vendorCode)
    {
        var vendorCacheKey = $"{CacheKeyPrefix}{VendorCacheKey}{vendorCode}";
        _cache.Remove(vendorCacheKey);
        
        // Also clear item-specific caches for this vendor
        // Note: In a real implementation, you might want to track cache keys more systematically
        _logger.LogInformation("Cleared cache for vendor {VendorCode}", vendorCode);
    }

    public void ClearAllCache()
    {
        // Note: IMemoryCache doesn't have a clear all method
        // In a real implementation, you might want to use a different caching strategy
        // or track cache keys to clear them individually
        
        if (_cache is MemoryCache memoryCache)
        {
            memoryCache.Compact(1.0); // Remove all entries
        }
        
        _logger.LogInformation("Cleared all Front Margin cache");
    }

    public async Task WarmUpCacheAsync(List<string> vendorCodes)
    {
        _logger.LogInformation("Warming up cache for {Count} vendors", vendorCodes.Count);
        
        var tasks = vendorCodes.Select(async vendorCode =>
        {
            try
            {
                await GetActivePromotionsAsync(vendorCode);
                _logger.LogDebug("Warmed up cache for vendor {VendorCode}", vendorCode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error warming up cache for vendor {VendorCode}", vendorCode);
            }
        });

        await Task.WhenAll(tasks);
        
        _logger.LogInformation("Cache warm-up completed");
    }

    public FrontMarginCacheStats GetCacheStats()
    {
        var totalRequests = _cacheHits + _cacheMisses;
        var hitRatio = totalRequests > 0 ? (double)_cacheHits / totalRequests * 100 : 0;

        return new FrontMarginCacheStats
        {
            CacheHits = _cacheHits,
            CacheMisses = _cacheMisses,
            TotalRequests = totalRequests,
            HitRatio = hitRatio,
            CacheExpiration = _cacheExpiration
        };
    }

    #region Private Methods

    private async Task<List<PromotionFrontMargin>> LoadActivePromotionsFromDatabaseAsync(string vendorCode)
    {
        var currentDate = DateTime.Now;

        await using var dbContext = _dbContextFactory.CreateDbContext();

        return await dbContext.PromotionFrontMargins
            .Include(p => p.PromotionHeader)
            .Where(p => p.PromotionHeader.VendorCode == vendorCode &&
                       p.PromotionHeader.ProgramType == 1 && // Front Margin
                       p.PromotionHeader.Status == 2 && // Active
                       p.PromotionHeader.StartDate <= currentDate &&
                       p.PromotionHeader.EndDate >= currentDate &&
                       p.Status == 2 && // Active
                       p.ModificationStatus == (int)ModificationStatusEnum.ACTIVE)
            .OrderBy(p => p.ProgramNumber)
            .ThenBy(p => p.LineNumber)
            .ToListAsync();
    }

    private async Task<List<PromotionFrontMargin>> LoadPromotionsForItemFromDatabaseAsync(string vendorCode, string itemNumber)
    {
        var currentDate = DateTime.Now;
        
        return await _context.PromotionFrontMargins
            .Include(p => p.PromotionHeader)
            .Where(p => p.PromotionHeader.VendorCode == vendorCode &&
                       p.ItemNumber == itemNumber &&
                       p.PromotionHeader.ProgramType == 1 && // Front Margin
                       p.PromotionHeader.Status == 2 && // Active
                       p.PromotionHeader.StartDate <= currentDate &&
                       p.PromotionHeader.EndDate >= currentDate &&
                       p.Status == 2 && // Active
                       p.ModificationStatus == (int)ModificationStatusEnum.ACTIVE)
            .OrderBy(p => p.ProgramNumber)
            .ThenBy(p => p.LineNumber)
            .ToListAsync();
    }

    #endregion
}

/// <summary>
/// Cache statistics for monitoring
/// </summary>
public class FrontMarginCacheStats
{
    public int CacheHits { get; set; }
    public int CacheMisses { get; set; }
    public int TotalRequests { get; set; }
    public double HitRatio { get; set; }
    public TimeSpan CacheExpiration { get; set; }
}

/// <summary>
/// Background service to manage cache warm-up and cleanup
/// </summary>
public class FrontMarginCacheBackgroundService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<FrontMarginCacheBackgroundService> _logger;
    private readonly TimeSpan _warmUpInterval = TimeSpan.FromHours(1);

    public FrontMarginCacheBackgroundService(
        IServiceProvider serviceProvider,
        ILogger<FrontMarginCacheBackgroundService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await WarmUpCacheForActiveVendors();
                await Task.Delay(_warmUpInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Front Margin cache background service");
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken); // Wait before retrying
            }
        }
    }

    private async Task WarmUpCacheForActiveVendors()
    {
        using var scope = _serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        var cacheService = scope.ServiceProvider.GetRequiredService<IFrontMarginCacheService>();

        try
        {
            // Get vendors with active Front Margin promotions
            var activeVendors = await context.PromotionHeaders
                .Where(h => h.ProgramType == 1 && // Front Margin
                           h.Status == 2 && // Active
                           h.StartDate <= DateTime.Now &&
                           h.EndDate >= DateTime.Now &&
                           h.ModificationStatus == (int)ModificationStatusEnum.ACTIVE)
                .Select(h => h.VendorCode)
                .Distinct()
                .ToListAsync();

            if (activeVendors.Any())
            {
                await cacheService.WarmUpCacheAsync(activeVendors);
                _logger.LogInformation("Cache warmed up for {Count} active vendors", activeVendors.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error warming up cache for active vendors");
        }
    }
}
