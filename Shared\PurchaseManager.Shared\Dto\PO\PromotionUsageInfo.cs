namespace PurchaseManager.Shared.Dto.PO;

/// <summary>
/// Information about promotion usage for tracking purposes
/// </summary>
public class PromotionUsageInfo
{
    public string PromotionNumber { get; set; } = string.Empty;
    public int DiscountType { get; set; }
    public decimal OriginalQuantity { get; set; }
    public decimal FinalQuantity { get; set; }
    public decimal OriginalUnitCost { get; set; }
    public decimal FinalUnitCost { get; set; }
    public decimal OriginalAmount { get; set; }
    public decimal FinalAmount { get; set; }
    public decimal DiscountPercentage { get; set; }
    public decimal FixedDiscountAmount { get; set; }
}
