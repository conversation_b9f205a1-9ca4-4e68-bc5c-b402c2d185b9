@page "/PO/{PONumber}/detail"
@using MudBlazor.Extensions.Components
@using PurchaseManager.Shared.Dto.Item
@using PurchaseManager.Shared.Dto.PO
@using PurchaseManager.Theme.Material.Demo.Pages.PurchaseOrder.Services
@using PurchaseManager.Theme.Material.Shared.Utils
@inject IJSRuntime Js
@attribute [Authorize]
<PageTitle>Purchase Order Detail</PageTitle>

@if (IsLoad)
{
    <LoadingBackground>
        <label>@L["Loading"]</label>
    </LoadingBackground>
}
else
{
    <CascadingAuthenticationState>
        <MudToolBar Gutters="false" Dense Class="mb-3">
            <MudIconButton Color="Color.Default" Icon="@Icons.Material.Filled.ArrowBack"
                           OnClick="@(async () => { await Js.InvokeVoidAsync("history.back"); })" Variant="Variant.Text">
            </MudIconButton>
            <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.Center">
                <MudText Typo="Typo.h6">@L["PurchaseOrderDetail"] - @POHeader.Number</MudText>
                @foreach (DocNoOccurrenceEnum docEnum in Enum.GetValues(typeof(DocNoOccurrenceEnum)))
                {
                    @if ((int)docEnum == POHeader.DocNoOccurrence)
                    {
                        <MudChip T="string" Size="Size.Small" Variant="Variant.Text" Color="Color.Info"> @L[docEnum.GetDisplayName()] </MudChip>
                    }
                }
            </MudStack>
            <MudSpacer/>
            @if (POHeader.Status <= 3 && (!IsWarehouse || IsAdmin))
            {
                <MudButton Size="Size.Small" OnClick="@(() => SaveChangeHeaderPOOrOpenPO())" Class="mr-6"
                           Variant="Variant.Filled" Color="Color.Primary">
                    @if (IsEdit)
                    {
                        @L["Save"]
                    }
                    else
                    {
                        @("Open")
                    }
                </MudButton>
            }
            <MudButtonGroup Size="Size.Small" Variant="Variant.Outlined" Color="Color.Success" Class="mr-6">
                @if (IsPurchaseManager || IsMKT)
                {
                    <MudButton Disabled="@(!IsEdit)" OnClick="PurchaserApprove" Variant="Variant.Outlined" Size="Size.Small"
                               Color="@(string.IsNullOrEmpty(POHeader.PurchaserApprovalBy) ? Color.Surface : Color.Success)"
                               EndIcon="@(string.IsNullOrEmpty(POHeader.PurchaserApprovalBy) ? string.Empty : Icons.Material.Outlined.Check)">
                        <MudTooltip Text="Purchaser Approve">
                            <MudText> Approve</MudText>
                        </MudTooltip>
                    </MudButton>
                    <MudButton Disabled="@(!IsEdit)" OnClick="VendorApprove" Variant="Variant.Outlined" Size="Size.Small"
                               Color="@(string.IsNullOrEmpty(POHeader.VendorApprovalBy) ? Color.Surface : Color.Success)"
                               EndIcon="@(string.IsNullOrEmpty(POHeader.VendorApprovalBy) ? string.Empty : Icons.Material.Outlined.Check)">
                        <MudTooltip Text="Purchase approve instead Vendor">
                            <MudText>Approve vendor</MudText>
                        </MudTooltip>
                    </MudButton>
                }
                <AuthorizeView Policy="@(Policies.IsVendor)">
                    <MudButton Disabled="@(!IsEdit)" OnClick="VendorApprove" Variant="Variant.Outlined" Size="Size.Small"
                               Color="@(string.IsNullOrEmpty(POHeader.VendorApprovalBy) ? Color.Surface : Color.Success)"
                               EndIcon="@(string.IsNullOrEmpty(POHeader.VendorApprovalBy) ? string.Empty : Icons.Material.Outlined.Check)">
                        <MudTooltip Text="Vendor Approve">
                            <MudText> Approve</MudText>
                        </MudTooltip>
                    </MudButton>
                </AuthorizeView>
            </MudButtonGroup>

            @* Promotion History Button *@
            <MudButton OnClick="ShowPromotionHistoryDialog" Variant="Variant.Outlined" Size="Size.Small"
                       Color="Color.Info" StartIcon="@Icons.Material.Filled.Campaign" Class="mr-2"
                       Disabled="@(POLines == null || !POLines.Any())">
                <MudTooltip Text="Xem lịch sử khuyến mãi cho các sản phẩm trong PO">
                    Khuyến Mãi
                </MudTooltip>
            </MudButton>

            <MudButtonGroup Size="Size.Small" Variant="Variant.Outlined">
                <AuthorizeView Policy="@(Policies.IsPurchaseUser)">
                    <MudIconButton Icon="@Icons.Material.Outlined.Email"
                                   Disabled="@(string.IsNullOrEmpty(POHeader.VendorApprovalBy) || string.IsNullOrEmpty(POHeader.PurchaserApprovalBy))"
                                   Color="Color.Default" Size="Size.Small" OnClick="OnClickPreviewEmail">
                        Email
                    </MudIconButton>
                </AuthorizeView>
                <MudIconButton Icon="@Icons.Material.Outlined.Print" Color="Color.Default" Size="Size.Small" OnClick="DownloadReportPO">
                    Print
                </MudIconButton>
                <MudTooltip Placement="Placement.Bottom" Text="Export PO Detail To Excel">
                    <MudIconButton Icon="@Icons.Custom.FileFormats.FileExcel" Color="Color.Default" Size="Size.Small" OnClick="ExportDetailPo">
                        Print
                    </MudIconButton>
                </MudTooltip>
            </MudButtonGroup>
        </MudToolBar>
        <MudDialog Visible="@(!string.IsNullOrWhiteSpace(ErrorMessage))"
                   Options="new DialogOptions { FullWidth = true, MaxWidth = MaxWidth.Medium, CloseOnEscapeKey = false, BackdropClick = false }"
                   TitleClass="mud-secondary" ContentStyle="min-height:200px">
            <TitleContent>
                <MudText Inline Typo="Typo.h5" Align="Align.Start"
                         Class="font-weight-bold">@L["List error create Line"]</MudText>
            </TitleContent>
            <DialogContent>
                @if (GroupedDuplicateErrors != null && GroupedDuplicateErrors.Any() && GroupedDuplicateErrors.SelectMany(g => g.Value).Any() ||
                     GroupedVatErrors != null && GroupedVatErrors.Any() && GroupedVatErrors.SelectMany(g => g.Value).Any())
                {
                    <MudGrid>
                        <MudItem xs="12" sm="6">
                            <MudText Typo="Typo.subtitle2" Color="Color.Primary" Class="font-weight-bold"><b>Duplicate
                                    Errors</b></MudText>
                            <MudList Dense T="String">
                                @if (GroupedDuplicateErrors != null && GroupedDuplicateErrors.Any() && GroupedDuplicateErrors.SelectMany(g => g.Value).Any())
                                {
                                    @foreach (var error in GroupedDuplicateErrors.SelectMany(g => g.Value))
                                    {
                                        <MudListItem T="String">@error</MudListItem>
                                    }
                                }
                                else
                                {
                                    <MudListItem T="String">No duplicate errors.</MudListItem>
                                }
                            </MudList>
                        </MudItem>
                        <MudItem xs="12" sm="6">
                            <MudText Typo="Typo.subtitle2" Color="Color.Primary" Class="font-weight-bold"><b>VAT
                                    Errors</b>
                            </MudText>
                            <MudList Dense T="String">
                                @if (GroupedVatErrors != null && GroupedVatErrors.Any() && GroupedVatErrors.SelectMany(g => g.Value).Any())
                                {
                                    @foreach (var error in GroupedVatErrors.SelectMany(g => g.Value))
                                    {
                                        <MudListItem T="String">@error</MudListItem>
                                    }
                                }
                                else
                                {
                                    <MudListItem T="String">No VAT errors.</MudListItem>
                                }
                            </MudList>
                        </MudItem>
                    </MudGrid>
                }
                else
                {
                    <MudText Color="Color.Success">@L["No errors found."]</MudText>
                }
            </DialogContent>
            <DialogActions>
                <MudButton Variant="Variant.Text" Color="Color.Error" OnClick="@(_ => ErrorMessage = string.Empty)">@L["Clear"]</MudButton>
            </DialogActions>
        </MudDialog>
        <MudForm Spacing="4">
            <MudGrid Class="mb-2">
                <MudItem xs="5">
                    <MudStack Spacing="0">
                        <MudStack Row>
                            <MudField Variant="Variant.Outlined" Margin="Margin.Dense"
                                      Label="@L["VendorName"]">
                                @VendorInfo.Name <MudText>Number:
                                    <MudText Color="Color.Primary" Inline><b>@VendorInfo.Number</b></MudText>
                                </MudText>
                            </MudField>
                        </MudStack>
                        <MudTextField Lines="2" Variant="Variant.Outlined"
                                      ReadOnly="@(!IsEdit)"
                                      Adornment="Adornment.None"
                                      Label="@L["Description"]"
                                      @bind-Value="@POHeader.PostingDescription">
                        </MudTextField>
                        <MudStack Row>
                            <MudDatePicker ReadOnly="@(!IsEdit)" Label="@L["DueDate"]" MinDate="DateTime.Today" Variant="Variant.Outlined"
                                           Date="@POHeader.DueDate" DateChanged="@OnDueDateChanged"/>
                            <MudDatePicker ReadOnly="true" Label="@L["OrderDate"]" Variant="Variant.Outlined" Date="@POHeader.OrderDate"/>
                        </MudStack>
                    </MudStack>
                </MudItem>
                <MudItem xs="3">
                    <MudPaper Elevation="0" Outlined Class="px-4 py-2">
                        <MudText Typo="Typo.subtitle1" Color="Color.Primary"><b>@L["Summarize"]</b></MudText>
                        <MudStack Spacing="3" Class="mt-1">
                            <MudStack Row Justify="Justify.SpaceBetween">
                                <MudText>@L["Amount(-VAT)"]</MudText>
                                <MudText Color="Color.Primary"><b>@POLines.Sum(x => x.Amount).ToString("N2") </b></MudText>
                            </MudStack>
                            <MudStack Row Justify="Justify.SpaceBetween">
                                <MudText>@L["VATAmount"]</MudText>
                                <MudText Color="Color.Error"><b>@POLines.Sum(x => x.VatAmount).ToString("N2")</b></MudText>
                            </MudStack>
                            <MudStack Row Justify="Justify.SpaceBetween">
                                <MudText>@L["DiscountAmount"]</MudText>
                                <MudText Color="Color.Tertiary"><b>@POLines.Sum(x => x.LineDiscountAmount).ToString("N2")</b></MudText>
                            </MudStack>
                            <MudStack Row Justify="Justify.SpaceBetween">
                                <MudText>@L["Amount(+Dis.)"]</MudText>
                                <MudText Color="Color.Primary"><b>@POLines.Sum(x => x.VatBaseAmount).ToString("N2")</b></MudText>
                            </MudStack>
                            <MudStack Row Justify="Justify.SpaceBetween">
                                <MudText>@L["TotalAmount(+VAT)"]</MudText>
                                <MudText Color="Color.Success"><b>@POLines.Sum(x => x.AmountIncludingVat).ToString("N2")</b></MudText>
                            </MudStack>
                        </MudStack>
                    </MudPaper>
                </MudItem>
                <MudItem xs="4">
                    <MudPaper Elevation="0" Outlined Class="px-4 py-2">
                        <MudText Typo="Typo.subtitle1" Color="Color.Primary"><b>@L["Modification"]</b></MudText>
                        <MudStack Spacing="3" Class="mt-1">
                            <MudStack Row Justify="Justify.SpaceBetween">
                                <MudStack Style="width: 50%;" Row Justify="Justify.SpaceBetween">
                                    <MudText>@L["CreatedBy"]</MudText>
                                    <MudText><b>@POHeader.PurchaserCode?.ToUpper()</b></MudText>
                                </MudStack>
                                <MudSpacer/>
                                <MudStack Row Justify="Justify.SpaceBetween">
                                    <MudText>@L["ModifiedBy"]</MudText>
                                    <MudText><b>@POHeader.ModifiedID?.ToUpper()</b></MudText>
                                </MudStack>
                            </MudStack>
                            <MudStack Row Justify="Justify.SpaceBetween">
                                <MudText>@L["LastModifiedTime"]</MudText>
                                <MudText><b>@POHeader.LastModifiedTime</b></MudText>
                            </MudStack>
                            <MudStack Row Justify="Justify.SpaceBetween">
                                <MudText>@L["CreatedAtTime"]</MudText>
                                <MudText><b>@POHeader.CreatedAtTime</b></MudText>
                            </MudStack>
                            <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                                <MudText>@L["PurchaserApprovalBy"]</MudText>
                                <MudText>
                                    @if (string.IsNullOrEmpty(POHeader.PurchaserApprovalBy))
                                    {
                                        <MudText Inline Color="Color.Warning">not approve yet</MudText>
                                    }
                                    else
                                    {
                                        <MudText Inline Color="Color.Success">@POHeader.PurchaserApprovalBy.ToUpper()</MudText>
                                    }
                                </MudText>
                            </MudStack>
                            <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                                <MudText>@L["VendorApprovalBy"]</MudText>
                                <MudText>
                                    @if (string.IsNullOrEmpty(POHeader.VendorApprovalBy))
                                    {
                                        <MudText Inline Color="Color.Warning">not approve yet</MudText>
                                    }
                                    else
                                    {
                                        <MudText Inline Color="Color.Success">@POHeader.VendorApprovalBy.ToUpper()</MudText>
                                    }
                                </MudText>
                            </MudStack>
                        </MudStack>
                    </MudPaper>
                </MudItem>
            </MudGrid>
        </MudForm>
        <MudTabs Class="mt-3" PanelClass="pt-3" Rounded=true ApplyEffectsToContainer MinimumTabWidth="20px" Elevation="1">
            <MudTabPanel Text="Danh sách">
                @if (IsEdit)
                {
                    <MudStack Class="ps-3">
                        <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.FlexStart">
                            @if (SelectedPOLine is { Count: > 0 })
                            {
                                <MudIconButton Icon="@Icons.Material.Filled.Delete" Size="Size.Small" OnClick="DeleteMultiLine"
                                               Variant="Variant.Outlined"
                                               Disabled="@(!IsEdit || HasPromotionItemSelected)"
                                               Color="Color.Error">
                                </MudIconButton>
                                @if (HasPromotionItemSelected)
                                {
                                    <MudTooltip Text="Không thể xóa sản phẩm khuyến mãi">
                                        <MudIcon Icon="@Icons.Material.Filled.Info" Color="Color.Warning" Size="Size.Small"/>
                                    </MudTooltip>
                                }
                            }
                            @if (IsEdit)
                            {
                                <MudButton Size="Size.Small" StartIcon="@Icons.Material.Filled.Add" OnClick="AddOrderLine" Disabled="@(!IsEdit)"
                                           Variant="Variant.Outlined"
                                           Color="Color.Success">
                                    Thêm
                                </MudButton>
                                <MudTooltip Placement="Placement.Bottom" Text="Import PO Detail From Excel">
                                    <MudFileUpload T="IBrowserFile" MaximumFileCount="1" FilesChanged="UploadPODetailFromFiles" Accept=".xlsx">
                                        <ActivatorContent>
                                            <MudIconButton Icon="@Icons.Custom.FileFormats.FileExcel" Color="Color.Success" Size="Size.Medium">
                                            </MudIconButton>
                                        </ActivatorContent>
                                    </MudFileUpload>
                                </MudTooltip>
                                <MudSpacer/>
                                <MudTooltip Placement="Placement.Bottom" Text="Download sample file for import PO Detail">
                                    <MudLink Underline="Underline.Always" Href="/files/Import_POLine_SampleData_10062025.xlsx">
                                        <MudIconButton Icon="@Icons.Material.Outlined.Download" Color="Color.Success" Size="Size.Medium">
                                        </MudIconButton>
                                    </MudLink>
                                </MudTooltip>
                            }
                        </MudStack>
                    </MudStack>
                }
                <MudTable Dense Items="@POLines" T="POLineGetDto"
                          @ref="Table" Height="500px"
                          FixedHeader FixedFooter
                          CustomFooter
                          SelectOnRowClick="false"
                          Hover
                          RowStyle="@(IsEdit ? "cursor: pointer;" : "cursor: default;")"
                          @bind-SelectedItems="SelectedPOLine"
                          ReadOnly="@(!IsEdit)" MultiSelection="@IsEdit"
                          OnRowClick="@(async row => await OnEditPOLine(row.Item))">
                    <HeaderContent>
                        <MudTh>@L["Item"]</MudTh>
                        <MudTh>@L["Type"]</MudTh>
                        <MudTh>@L["LotNo"] </MudTh>
                        <MudTh>@L["ExpirationDate"] </MudTh>
                        <MudTh>@L["Unit"]</MudTh>
                        <MudTh Style="text-align:right;">@L["Quantity"]</MudTh>
                        <MudTh Style="text-align:right;">@L["QuantityReceived"]</MudTh>
                        <MudTh Style="text-align:right;">@L["Price"]</MudTh>
                        <MudTh Style="text-align:right;">@L["Amount(-VAT)"]</MudTh>
                        <MudTh Style="text-align:right;">@L["Last Price"]</MudTh>
                        <MudTh Style="text-align:right;">@L["%VAT"]</MudTh>
                        <MudTh Style="text-align:right;">@L["VATAmount"]</MudTh>
                        <MudTh Style="text-align:right;">@L["%Discount"]</MudTh>
                        <MudTh Style="text-align:right;">@L["DiscountAmount"]</MudTh>
                        <MudTh Style="text-align:right;">@L["Amount(+Dis.)"]</MudTh>
                        <MudTh Style="text-align:right;">@L["TotalAmount(+VAT)"]</MudTh>
                        <MudTh Style="text-align:right;">@L["Description"]</MudTh>
                    </HeaderContent>
                    <RowTemplate Context="poLine">
                        <MudTd Style="min-width:250px">
                            <MudText Typo="Typo.body1">
                                @poLine.ItemName
                            </MudText>
                            <MudText Typo="Typo.caption">
                                <b>Number:</b>
                                @poLine.ItemNumber
                            </MudText>
                        </MudTd>
                        <MudTd Style="min-width:50px; text-align: center">
                            @if (poLine.DocumentType == 0)
                            {
                                <MudTooltip Text="Hàng mua">
                                    <MudIcon Icon="@Icons.Material.Filled.ShoppingCart" Color="Color.Primary"/>
                                </MudTooltip>
                            }
                            else
                            {
                                <MudTooltip Text="Hàng Khuyến mãi">
                                    <MudIcon Icon="@Icons.Material.Filled.CardGiftcard" Color="Color.Error"/>
                                </MudTooltip>
                            }
                        </MudTd>
                        <MudTd>@poLine.LotNo</MudTd>
                        <MudTd>
                            @if (poLine.ExpirationDate != null)
                            {
                                @(poLine.ExpirationDate.Value.GetDateStringNoTime())
                            }
                            else
                            {
                                <span>-</span>
                            }
                        </MudTd>
                        <MudTd>@poLine.UnitOfMeasure.ToUpper()</MudTd>
                        <MudTd Style="width:40px; text-align: right;">
                            <MudText Align="Align.End">@poLine.Quantity.ToString("N0")</MudText>
                        </MudTd>
                        <MudTd Style="width:40px; text-align: right;">
                            <MudText Align="Align.End">@poLine.QuantityReceived.ToString("N0")</MudText>
                        </MudTd>
                        <MudTd Style="text-align: right; min-width:100px;">@poLine.UnitCost.ToString("N2")</MudTd>
                        <MudTd Style="text-align: right; min-width:150px;">
                            <MudText Color="Color.Primary"><b>@poLine.Amount.ToString("N2")</b></MudText>
                        </MudTd>
                        <MudTd Style="text-align: right; min-width:100px;">@poLine.LastUnitCost.ToString("N2") </MudTd>
                        <MudTd Style="text-align: right; min-width:100px">@poLine.Vat.ToString("N2") %</MudTd>
                        <MudTd Style="text-align: right; min-width:150px;">
                            <MudText Color="Color.Error"><b>@poLine.VatAmount.ToString("N2")</b></MudText>
                        </MudTd>
                        <MudTd Style="text-align: right;">@poLine.LineDiscountPercent.ToString("N2") %</MudTd>
                        <MudTd Style="text-align: right;">
                            <MudText Color="Color.Tertiary"><b>@poLine.LineDiscountAmount.ToString("N2")</b></MudText>
                        </MudTd>
                        <MudTd Style="text-align: right; min-width:150px;">
                            <MudText Color="Color.Primary"><b>@poLine.VatBaseAmount.ToString("N2")</b></MudText>
                        </MudTd>
                        <MudTd Style="text-align: right; min-width:150px;">
                            <MudText Color="Color.Success"><b>@poLine.AmountIncludingVat.ToString("N2")</b></MudText>
                        </MudTd>
                        <MudTd Style="text-align: right; min-width:200px;">@poLine.Description</MudTd>
                    </RowTemplate>
                    <NoRecordsContent>
                        No Data
                    </NoRecordsContent>
                </MudTable>
            </MudTabPanel>
            <MudTabPanel Text="Phiếu xuất">
                <MudStack Style="width: 100%">
                    <UploadFiles OnReloadFileTable="@(async () => await CallReloadFileTable())" Disabled="@(!IsEdit)"
                                 Caption="Drag and drop files here or click for receive" PONumber="@PONumber" Prefix="RECEIVE"/>
                    <ListFilePage @ref="ListFilePageRef" Caption="Receive Files" DocumentNo="@PONumber" Prefix="RECEIVE"/>
                </MudStack>
            </MudTabPanel>
            <MudTabPanel Text="Hóa đơn">
                <MudStack Style="width: 100%">
                    <UploadFiles Caption="Drag and drop files here or click for bill" PONumber="@PONumber" Disabled="@(!IsEdit)"
                                 Prefix="BILL"/>
                    <ListFilePage Caption="Bill Files" DocumentNo="@PONumber" Prefix="BILL"/>
                </MudStack>
            </MudTabPanel>
        </MudTabs>
        <MudDialog @bind-Visible="@IsShowReport"
                   TitleClass="mud-secondary" ContentStyle="min-height:200px" ContentClass="pa-0"
                   Options="new DialogOptions { FullWidth = true, FullScreen = true, MaxWidth = MaxWidth.False, CloseButton = true }">
            <DialogContent>
                <MudExFileDisplay FileName="@PONumber" ColorizeIcons Url="@UrlFileReport" ContentType="application/pdf"></MudExFileDisplay>
            </DialogContent>
        </MudDialog>
        <MudDialog @bind-Visible="@IsShowAddNewVendorItem" TitleClass="mud-secondary" ContentStyle="min-height:200px" ContentClass="pa-0">
            <TitleContent>
                <MudText Inline Typo="Typo.h5" Align="Align.Start">@L["Add new promotion item "]</MudText>
            </TitleContent>
            <DialogContent>
                <PurchaseManager.Theme.Material.Demo.Pages.Items.AddItem.AddVendorItem OnVendorItemAdded="@OnVendorItemAddedCallback"
                                                                                       VendorNumber="@POHeader.BuyFromVendorNumber"/>
            </DialogContent>
        </MudDialog>
    </CascadingAuthenticationState>
}

<MudDialog Visible="@(IsShowEditPOLineDialog || IsShowAddFormLine)"
           Options="new DialogOptions { FullWidth = true, MaxWidth = MaxWidth.Medium, CloseOnEscapeKey= false, BackdropClick= false }"
           TitleClass="mud-secondary" ContentStyle="min-height:200px">
    <TitleContent>
        @if (IsShowEditPOLineDialog)
        {
            <div>
                @L["Edit line"] -
                <MudText Inline Typo="Typo.subtitle2" Color="Color.Primary">@CurrentLine.ItemNumber - @CurrentLine.ItemName</MudText>
                Unit:
                <MudText Inline Typo="Typo.subtitle2" Color="Color.Primary">@CurrentLine.UnitOfMeasure</MudText>
            </div>
        }
        else
        {
            @L["Add new line"]
        }
    </TitleContent>
    <DialogContent>
        <MudForm @ref="@EditPOLineFormRef" Model="@CurrentLine">
            <MudGrid>
                <MudItem sm="8" xs="12">
                    <MudStack Spacing="3">
                        <MudGrid Spacing="2">
                            <MudItem xs="12" sm="6" md="9">
                                @if (IsShowEditPOLineDialog)
                                {
                                    <MudTextField Variant="Variant.Outlined" T="String" Label="@L["ItemNumber"]"
                                                  Value="@($"{CurrentLine.ItemNumber} - {CurrentLine.ItemName}")" ReadOnly/>
                                }
                                else
                                {
                                    <MudAutocomplete Variant="Variant.Outlined" T="DetailItemDto" ShrinkLabel Label="@L["ItemNumber"]"
                                                     ShowProgressIndicator ValueChanged="@(dto => OnItemSelectedInAutoComplete(dto))"
                                                     ToStringFunc="@(dto => dto == null
                                                                       ? $"{CurrentLine.ItemNumber} - {CurrentLine.ItemName}"
                                                                       : dto.Number + " - " + dto.Name)"
                                                     Required
                                                     SearchFunc="@ItemSearch">
                                        <ProgressIndicatorInPopoverTemplate>
                                            <MudList T="String" ReadOnly>
                                                <MudListItem>
                                                    Loading...
                                                </MudListItem>
                                            </MudList>
                                        </ProgressIndicatorInPopoverTemplate>
                                        <ItemTemplate Context="e">
                                            <MudStack Row="false" StretchItems="StretchItems.All">
                                                <MudStack Spacing="0">
                                                    <MudText>@e.Name</MudText>
                                                    <MudStack Row Spacing="0">
                                                        <MudText Typo="Typo.caption">@e.Number</MudText>
                                                        <MudChip T="String" Size="Size.Small" Variant="Variant.Text"
                                                                 Color="@(e.Blocked == 1 ? Color.Warning : Color.Success)">@(e.Blocked == 1 ? "Blocked" : "Active")</MudChip>
                                                        <MudChip T="String" Size="Size.Small" Variant="Variant.Text"
                                                                 Color="@(e.Status == 2 ? Color.Success : Color.Warning)">
                                                            Status
                                                        </MudChip>
                                                    </MudStack>
                                                </MudStack>
                                            </MudStack>
                                        </ItemTemplate>
                                    </MudAutocomplete>
                                }
                            </MudItem>
                            <MudItem xs="12" sm="6" md="3">
                                @if (IsShowEditPOLineDialog)
                                {
                                    @if (CurrentLine.DocumentType == 0)
                                    {
                                        <MudField Variant="Variant.Outlined" Style="text-align: center">Sản phẩm</MudField>
                                    }
                                    else
                                    {
                                        <MudField Variant="Variant.Outlined" Style="text-align: center">Sản phẩm KM</MudField>
                                    }
                                }
                                else
                                {
                                    <MudSelect T="Int32" Variant="Variant.Outlined" Label="@L["Type"]" @bind-Value="CurrentLine.DocumentType"
                                               ReadOnly="@(POHeader.DocNoOccurrence == 2)">
                                        <MudSelectItem Value="0">Sản phẩm</MudSelectItem>
                                        <MudSelectItem Disabled Value="1">Sản phẩm K.M</MudSelectItem>
                                    </MudSelect>
                                }
                            </MudItem>
                        </MudGrid>
                        <MudGrid Spacing="2">
                            <MudItem xs="12" sm="3" md="6">
                                <MudSelect Variant="Variant.Outlined" T="String" Label="@L["Unit"]"
                                           ValueChanged="@(async value => await OnUOMInLineChanged(CurrentLine, value))"
                                           ToStringFunc="dto => dto ?? CurrentLine.UnitOfMeasure">
                                    @if (LsDetailItemUnitOfMeasureDtoEditing != null)
                                    {
                                        foreach (var unit in LsDetailItemUnitOfMeasureDtoEditing)
                                        {
                                            <MudSelectItem Value="@unit.Code" Disabled="@(unit.Block == 1)">@unit.Code.ToUpper()</MudSelectItem>
                                        }
                                    }
                                </MudSelect>
                            </MudItem>
                            <MudItem xs="12" sm="3" md="3">
                                <MudNumericField Variant="Variant.Outlined" T="Decimal" Label="@L["Quantity"]"
                                                 @bind-Value="CurrentLine.Quantity"
                                                 HideSpinButtons Format="N0" Immediate Required TextChanged="() => CurrentLine.UpdateAmount()"/>
                            </MudItem>
                            <MudItem xs="12" sm="3" md="3">
                                <MudNumericField Variant="Variant.Outlined" T="decimal" Label="@L["QtyPerUnit"]"
                                                 Value="CurrentLine.QtyPerUnitOfMeasure" ReadOnly
                                                 HideSpinButtons Format="N0"/>
                            </MudItem>
                            <MudItem xs="12" sm="6" md="6">
                                <MudTextField Variant="Variant.Outlined" T="string" Label="@L["LatestPriceByVendor - BaseUnit"]"
                                              Value="@CurrentLine.LastUnitCost.ToString("N2")"
                                              ReadOnly/>
                            </MudItem>
                            <MudItem xs="12" sm="6">
                                <MudSelect Variant="Variant.Outlined" T="Decimal" Label="@L["%VAT"]"
                                           @bind-Value="CurrentLine.Vat" Required
                                           TextChanged="()=> CurrentLine.UpdateAmount()">
                                    <MudSelectItem Value="0m">0</MudSelectItem>
                                    <MudSelectItem Value="5m">5</MudSelectItem>
                                    <MudSelectItem Value="8m">8</MudSelectItem>
                                    <MudSelectItem Value="10m">10</MudSelectItem>
                                </MudSelect>
                            </MudItem>
                        </MudGrid>
                        <MudGrid Spacing="2">
                            <MudItem xs="12" sm="12" md="12">
                                <MudNumericField Variant="Variant.Outlined" T="decimal" Label="Giá = Giá gốc * Số lượng"
                                                 @bind-Value="CurrentLine.UnitCost"
                                                 HideSpinButtons Format="N2" Min="0"
                                                 ReadOnly="@(CurrentLine.DocumentType == 1)"
                                                 Culture="@_cultureInfo"
                                                 Immediate
                                                 TextChanged="() => CurrentLine.UpdateAmount()"/>
                            </MudItem>
                        </MudGrid>
                        <MudGrid Spacing="2">
                            <MudItem xs="12" sm="6">
                                <MudTextField Variant="Variant.Outlined" T="String" Label="@L["LotNo"]" @bind-Value="CurrentLine.LotNo"/>
                            </MudItem>
                            <MudItem xs="12" sm="6">
                                <MudDatePicker Variant="Variant.Outlined" Label="@L["ExpirationDate"]" @bind-Date="ExpirationDateForPicker"/>
                            </MudItem>
                        </MudGrid>
                        <MudGrid>
                            <MudItem xs="12">
                                <MudTextField Variant="Variant.Outlined" T="String" Label="@L["Description"]" @bind-Value="CurrentLine.Description" AutoGrow/>
                            </MudItem>
                        </MudGrid>
                    </MudStack>
                </MudItem>
                <MudItem sm="4" xs="12">
                    <MudStack Spacing="7">
                        <MudStack Row Justify="Justify.SpaceBetween">
                            <MudText Typo="Typo.h6">Summary</MudText>
                        </MudStack>
                        <MudStack Row Justify="Justify.SpaceBetween">
                            <MudText>@L["VATAmount"] </MudText>
                            <MudText Color="Color.Error" Inline><b>@CurrentLine.VatAmount.ToString("N2")</b></MudText>
                        </MudStack>
                        <MudStack Row Justify="Justify.SpaceBetween">
                            <MudText>@L["DiscountAmount"]</MudText>
                            <MudText Color="Color.Tertiary" Inline><b>@CurrentLine.LineDiscountAmount.ToString("N2")</b></MudText>
                        </MudStack>
                        <MudStack Row Justify="Justify.SpaceBetween">
                            <MudText>@L["Amount(-VAT)"]</MudText>
                            <MudText Color="Color.Primary" Inline><b>@CurrentLine.Amount.ToString("N2")</b></MudText>
                        </MudStack>
                        <MudStack Row Justify="Justify.SpaceBetween">
                            <MudText>@L["Amount(+Dis.)"]</MudText>
                            <MudText Color="Color.Primary" Inline><b>@CurrentLine.VatBaseAmount.ToString("N2")</b></MudText>
                        </MudStack>
                        <MudStack Row Justify="Justify.SpaceBetween">
                            <MudText>@L["TotalAmount(+VAT)"]</MudText>
                            <MudText Color="Color.Success" Inline><b>@CurrentLine.AmountIncludingVat.ToString("N2")</b></MudText>
                        </MudStack>
                        <MudStack Justify="Justify.Center" AlignItems="AlignItems.Center">
                            <small>*@L["QtyPerUnit"]: <em> = 0: BaseUnit</em></small>
                        </MudStack>
                    </MudStack>
                </MudItem>
            </MudGrid>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton Variant="Variant.Text" Color="Color.Error" OnClick="@OnCloseEditPOLineDialog">@L["Cancel"]</MudButton>
        <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="@OnSavePOLineAsync">@L["Save"]</MudButton>
    </DialogActions>
</MudDialog>
