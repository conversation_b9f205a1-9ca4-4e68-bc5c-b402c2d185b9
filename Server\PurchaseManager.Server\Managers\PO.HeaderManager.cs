﻿using System.Data;
using System.Data.SqlClient;
using AutoMapper;
using Dapper;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using PurchaseManager.Constants;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Server.Services;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Dto.User;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.PO;
using PurchaseManager.Storage;
using static Microsoft.AspNetCore.Http.StatusCodes;
using static PurchaseManager.Shared.Helpers.POHandlingShare;

namespace PurchaseManager.Server.Managers;

public class POHeaderManager : IPOHeaderManager
{
    public static IWebHostEnvironment Environment { get; set; }
    private readonly IStringLocalizer<Global> _i18N;
    private readonly IAdminManager _adminManager;
    private readonly IMapper _mapper;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ApplicationDbContext _context;
    private readonly DapperContext _dapperContext;
    private readonly ILogger<POHeaderManager> _logger;
    
    public POHeaderManager(DapperContext dbContext, IHttpContextAccessor httpContextAccessor, ApplicationDbContext context,
        UserManager<ApplicationUser> userManager, IAdminManager adminManager, IMapper mapper, IStringLocalizer<Global> i18N,
        IWebHostEnvironment environment, ILogger<POHeaderManager> logger)
    {
        _dapperContext = dbContext;
        _httpContextAccessor = httpContextAccessor;
        _context = context;
        _userManager = userManager;
        _adminManager = adminManager;
        _mapper = mapper;
        _i18N = i18N;
        Environment = environment;
        _logger = logger;
    }

    public async Task<ApiResponse> HeaderNumberValidAsync(string code, string action)
    {
        if (string.IsNullOrEmpty(code))
        {
            return ApiResponse.S500();
        }
        var poHeader = await _context.PurchaseOrderHeaders.Where(x => x.Number == code).CountAsync();

        if (action == "insert")
        {
            if (poHeader > 0)
            {
                return ApiResponse.S404();
            }
        }
        else
        {
            if (poHeader == 0)
            {
                return ApiResponse.S404();
            }
        }
        return ApiResponse.S200();
    }

    public async Task<ApiResponse> ValidateVendorBeforePOCreationAsync(string code)
    {
        var vendor = await _context.Vendors
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Number == code);

        if (vendor == null)
        {
            return ApiResponse.S500("VendorNumber doesn't exist.");
        }

        return (vendor.Blocked, vendor.Status) switch
        {
            (VendorBlockedType.Blocked, _) => ApiResponse.S404("Vendor is blocked."),
            (_, VendorStatusType.Editing) or (_, VendorStatusType.Newed) => ApiResponse.S404("Vendor is Editing."),
            _ => ApiResponse.S200()
        };
    }

    public async Task<ApiResponse> CreateHeaderAsync(CreatePOHeaderDto createPOHeader)
    {
        var headerModel = _mapper.Map<PurchaseOrderHeader>(createPOHeader);
        headerModel.CreatedAtTime = DateTime.Now;
        headerModel.PromotionCalculated = 0;
        await _context.PurchaseOrderHeaders.AddAsync(headerModel);
        await _context.SaveChangesAsync();
        return ApiResponse.S200(result: createPOHeader);
    }

    public async Task<ApiResponse> GetHeaderAsync(string documentNumber)
    {
        var header = await _context.PurchaseOrderHeaders.FirstOrDefaultAsync(x => x.Number == documentNumber);
        if (header == null)
        {
            return ApiResponse.S404("Header does not exist");
        }
        var headerDto = _mapper.Map<POHeaderGetDto>(header);
        return ApiResponse.S200(result: headerDto);
    }
    public async Task<ApiResponse> GetPOHeadersByFilterAsync(PurchaseOrderFilter filter)
    {
        try
        {
            var take = filter.PageSize ?? 10;
            var skip = (filter.PageIndex ?? 0) * take;

            var baseQuery = _context.PurchaseOrderHeaders
                .AsNoTracking()
                .Include(x => x.PurchaseOrderLines)
                .Where(h =>
                    h.Blocked == false &&
                    (filter.FromDate == null || h.DocumentDate >= filter.FromDate) &&
                    (filter.IsMKT == null || h.IsMKT == filter.IsMKT) &&
                    (filter.ToDate == null || h.DocumentDate <= filter.ToDate) &&
                    (filter.Status == null || h.Status >= filter.Status) &&
                    (filter.StatusFilter == null || h.Status == filter.StatusFilter) &&
                    (filter.ContactNumber == null || h.BuyFromContact == filter.ContactNumber) &&
                    (filter.Description == null || h.PostingDescription.Contains(filter.Description)) &&
                    (filter.Number == null || h.Number.Trim().Contains(filter.Number.Trim())) &&
                    (filter.ItemNumber == null || h.PurchaseOrderLines.Any(line => line.ItemNumber == filter.ItemNumber.Trim())) &&
                    (string.IsNullOrEmpty(filter.Vendor) || h.BuyFromVendorNumber.Trim().Contains(filter.Vendor.Trim())));

            var totalRecords = await baseQuery.CountAsync();

            var result = await baseQuery
                .OrderByDescending(h => h.RowId)
                .Skip(skip)
                .Take(take)
                .GroupJoin(
                _context.PurchaseOrderLines,
                outerKeySelector: h => h.Number,
                innerKeySelector: l => l.DocumentNumber,
                resultSelector: (h, lines) => new
                {
                    Header = h,
                    Lines = lines
                }
                )
                .Select(g => new POHeaderGetDto
                {
                    Number = g.Header.Number,
                    BuyFromVendorNumber = g.Header.BuyFromVendorNumber,
                    PostingDescription = g.Header.PostingDescription,
                    DocumentDate = g.Header.DocumentDate,
                    OrderDate = g.Header.OrderDate,
                    Status = g.Header.Status,
                    TotalQuantity = g.Lines.Sum(l => (decimal?)l.Quantity) ?? 0,
                    TotalQuantityReceived = g.Lines.Sum(l => (decimal?)l.QuantityReceived) ?? 0,
                    TotalAmount = g.Lines.Sum(l => (decimal?)l.AmountIncludingVat) ?? 0,
                    DueDate = g.Header.DueDate,
                    PurchaserCode = g.Header.PurchaserCode,
                    Receive = g.Header.Receive,
                    TotalRecord = totalRecords,
                    PayToVendorNumber = g.Header.BuyFromVendorNumber,
                    PurchaserApprovalBy = g.Header.PurchaserApprovalBy,
                    YourReference = g.Header.YourReference,
                    UsingID = g.Header.UsingID,
                    LastModifiedTime = g.Header.LastModifiedTime,
                    DocNoOccurrence = g.Header.DocNoOccurrence,
                    VendorApprovalBy = g.Header.VendorApprovalBy,
                    ModifiedID = g.Header.ModifiedId,
                    BuyFromVendorName = g.Header.BuyFromVendorName,
                    VATRegistrationNumber = g.Header.VATRegistrationNumber,
                    VATBusinessPostingGroup = g.Header.VATBusinessPostingGroup,
                    BuyFromContact = g.Header.BuyFromContact == g.Header.BuyFromVendorNumber ? string.Empty : g.Header.BuyFromContact
                })
                .ToListAsync();

            var data = new PagedResult<POHeaderGetDto>
            {
                Data = result,
                CurrentPage = filter.PageIndex ?? 0,
                PageSize = filter.PageSize ?? 10,
                RowCount = totalRecords
            };
            return new ApiResponse(200, "Success", data);
        }
        catch (Exception ex)
        {
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }

    public async Task<ApiResponse> UpdateHeaderAsync(UpdatePOHeaderDto updatePOHeader)
    {
        var poHeader = await _context.PurchaseOrderHeaders.FirstOrDefaultAsync(x => x.Number == updatePOHeader.Number);
        if (poHeader == null)
        {
            return ApiResponse.S404("Document not found");
        }
        var vendor = await _context.Vendors.FirstOrDefaultAsync(x => x.Number == updatePOHeader.VendorNo);
        _mapper.Map(updatePOHeader, poHeader);
        poHeader.BuyFromVendorNumber = vendor.Number;
        poHeader.BuyFromVendorName = vendor.Name;
        poHeader.BuyFromAddress = vendor.Address;
        poHeader.PayToCode = vendor.Number;
        poHeader.PayToName = vendor.Name;
        poHeader.PayToAddress = vendor.Address;
        poHeader.ShipToCode = vendor.Number;
        poHeader.ShipToName = vendor.Name;
        poHeader.ShipToAddress = vendor.Address;
        poHeader.VATRegistrationNumber = vendor.VatregistrationNumber;

        await _context.SaveChangesAsync();
        return ApiResponse.S200();
    }

    public async Task<ApiResponse> UserIdValidAsync(string code)
    {
        if (string.IsNullOrEmpty(code))
        {
            return ApiResponse.S200();
        }
        var user = await _userManager.Users.AnyAsync(u => u.UserName == code);
        return user ? ApiResponse.S200() : ApiResponse.S500("User not found or blocked.");
    }

    public async Task<ApiResponse> IsDocumentLockedByAnotherUserAsync(string documentNumber)
    {
        var poHeader = await _context.PurchaseOrderHeaders.FirstOrDefaultAsync(x => x.Number == documentNumber);
        var usingId = poHeader.UsingID;
        var userLogin = _adminManager.GetUserLogin();
        if (string.IsNullOrEmpty(usingId)
            || string.Equals(userLogin, usingId, StringComparison.CurrentCultureIgnoreCase)
            || poHeader.BeginUsingTime == null)
        {
            return ApiResponse.S200();
        }
        var usingMinute = (DateTime.Now - poHeader.BeginUsingTime.Value).TotalMinutes;
        return usingMinute < 30
            ? ApiResponse.S500(
            $"The document is currently being accessed by {usingId}. Please try again in {Math.Ceiling(30 - usingMinute)} minutes.")
            : ApiResponse.S200();
    }

    public async Task<ApiResponse> IsLineLockedByAnotherUserAsync(string code)
    {
        var poHeader = await _context.PurchaseOrderHeaders.FirstOrDefaultAsync(x => x.Number == code);
        var usingId = poHeader.UsingID;
        if (usingId == null)
        {
            return ApiResponse.S500("Document have not been opened");
        }

        var userValid = await UserIdValidAsync(usingId);

        if (!userValid.IsSuccessStatusCode)
        {
            return ApiResponse.S500("UserId is not found in system");
        }

        var userLogin = _adminManager.GetUserLogin();
        if (string.Equals(userLogin, usingId, StringComparison.CurrentCultureIgnoreCase) || poHeader.BeginUsingTime == null)
        {
            return ApiResponse.S200();
        }
        var usingMinute = (DateTime.Now - poHeader.BeginUsingTime.Value).TotalMinutes;
        return usingMinute > 30
            ? ApiResponse.S500("Document have not been opened")
            : ApiResponse.S200();
    }

    public async Task<ApiResponse> OpenDocumentAsync(string documentNumber, bool isStockOrder)
    {
        var poHeader = await _context.PurchaseOrderHeaders.FirstOrDefaultAsync(x => x.Number == documentNumber);
        if (poHeader == null)
        {
            return ApiResponse.S404("Document not found");
        }
        if (poHeader.Status == (int)PurchaseOrderEnum.ERPStatus && isStockOrder == false)
        {
            return ApiResponse.S404("The documents have been sent to ERP.");
        }
        poHeader.UsingID = _adminManager.GetUserLogin();
        poHeader.BeginUsingTime = DateTime.Now;

        if (!isStockOrder)
        {
            poHeader.Status = (int)PurchaseOrderEnum.CancelConfirm;
        }
        else
        {
            // Khi mở PO cho Stock Order, cập nhật status thành ReceivingStock (51)
            poHeader.Status = (int)PurchaseOrderEnum.ReceivingStock;
        }

        await _context.SaveChangesAsync();

        return ApiResponse.S200();
    }

    public async Task<ApiResponse> CloseDocumentAsync(string documentNumber)
    {
        var poHeader = await _context.PurchaseOrderHeaders.FirstOrDefaultAsync(x => x.Number == documentNumber);
        if (poHeader == null)
        {
            return ApiResponse.S404("Document not found");
        }
        var userLogin = _adminManager.GetUserLogin();

        poHeader.UsingID = null;
        poHeader.ModifiedId = userLogin;
        poHeader.LastModifiedTime = DateTime.Now;

        await _context.SaveChangesAsync();
        return ApiResponse.S200();
    }

    public async Task<ApiResponse> ChangeDocumentStatusAsync(string documentNumber, PurchaseOrderEnum status)
    {
        var header = await _context.PurchaseOrderHeaders.FirstOrDefaultAsync(x => x.Number == documentNumber);
        if (header == null)
        {
            return ApiResponse.S404("PO Header not found");
        }

        var oldStatus = header.Status;
        header.Status = (int)status;
        header.ModifiedId = _adminManager.GetUserLogin();
        header.LastModifiedTime = DateTime.Now;
        await _context.SaveChangesAsync();

        // Track Front Margin usage when PO is approved
        if (status == PurchaseOrderEnum.Approve && oldStatus != (int)PurchaseOrderEnum.Approve)
        {
            await TrackFrontMarginUsageOnApprovalAsync(documentNumber);
        }

        return ApiResponse.S200("Update PO header successfully", header.Status);
    }

    /// <summary>
    /// Track Front Margin usage when PO is approved - Update Draft records to Active
    /// </summary>
    private async Task TrackFrontMarginUsageOnApprovalAsync(string poNumber)
    {
        try
        {
            // Find existing Draft usage records for this PO
            var draftUsageRecords = await _context.PromotionFrontMarginUsages
                .Where(u => u.PONumber == poNumber && u.Status == 1 && u.ModificationStatus == 1)// Status = 1 (Draft)
                .ToListAsync();

            if (draftUsageRecords.Count > 0)
            {
                // Update existing Draft records to Active
                foreach (var usage in draftUsageRecords)
                {
                    usage.Status = 2;// Active
                    usage.LastModifiedBy = _adminManager.GetUserLogin() ?? "SYSTEM";
                    usage.LastModifiedAt = DateTime.Now;
                    usage.Notes = $"{usage.Notes}\n[APPROVED] PO approved - status updated to Active";
                }

                await _context.SaveChangesAsync();
                _logger.LogInformation("Updated {Count} Draft Front Margin usage records to Active for PO {PONumber}",
                draftUsageRecords.Count, poNumber);
            }
            else
            {
                // Fallback: Create new records if no Draft records found (backward compatibility)
                await CreateFrontMarginUsageRecordsForApprovalAsync(poNumber);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error tracking Front Margin usage for PO {PONumber}", poNumber);
        }
    }

    /// <summary>
    /// Fallback method: Create Front Margin usage records for PO approval (backward compatibility)
    /// </summary>
    private async Task CreateFrontMarginUsageRecordsForApprovalAsync(string poNumber)
    {
        try
        {
            // Get PO header and lines with Front Margin information
            var poHeader = await _context.PurchaseOrderHeaders
                .FirstOrDefaultAsync(x => x.Number == poNumber);

            if (poHeader == null) return;

            var poLines = await _context.PurchaseOrderLines
                .Where(x => x.DocumentNumber == poNumber)
                .ToListAsync();

            // Filter lines that have Front Margin applied (check from Usage table instead)
            // Since we removed FrontMarginNumber and HasFrontMargin fields,
            // we rely on PromotionFrontMarginUsage table for tracking
            var frontMarginLines = poLines.Where(line =>
                    line.DocumentType == 2 // Only promotional items for fallback
                )
                .ToList();

            if (frontMarginLines.Count == 0) return;

            // Create usage tracking records
            foreach (var line in frontMarginLines)
            {
                await CreateFrontMarginUsageRecordAsync(poHeader, line);
            }

            _logger.LogInformation("Created {Count} new Front Margin usage records for PO {PONumber} (fallback)",
            frontMarginLines.Count, poNumber);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating Front Margin usage records for PO {PONumber}", poNumber);
        }
    }

    /// <summary>
    /// Create Front Margin usage record for a PO line
    /// </summary>
    private async Task CreateFrontMarginUsageRecordAsync(PurchaseOrderHeader poHeader, PurchaseOrderLine poLine)
    {
        try
        {
            // Determine discount type and calculate values
            var discountType = DetermineDiscountType(poLine);
            var (originalAmount, finalAmount, discountAmount) = CalculateDiscountAmounts(poLine);

            var usage = new PromotionFrontMarginUsage
            {
                // ProgramNumber will be fetched from PromotionFrontMargin on server side
                PromotionNumber = "FALLBACK_UNKNOWN", // This is fallback logic, should not be used
                PONumber = poHeader.Number,
                POLineNumber = poLine.LineNumber,
                ItemNumber = poLine.ItemNumber,
                ItemName = poLine.ItemName ?? "",
                VendorCode = poHeader.BuyFromVendorNumber,
                DiscountType = discountType,
                OriginalQuantity = poLine.Quantity,
                FinalQuantity = poLine.Quantity,
                OriginalUnitCost = poLine.LastUnitCost > 0 ? poLine.LastUnitCost : poLine.UnitCost,
                FinalUnitCost = poLine.UnitCost,
                DiscountPercentage = poLine.LineDiscountPercent,
                FixedDiscountAmount = poLine.LineDiscountAmount,
                TotalDiscountAmount = discountAmount,
                OriginalLineAmount = originalAmount,
                FinalLineAmount = finalAmount,
                GiftQuantity = 0,// Will be calculated if needed
                AppliedDate = DateTime.Now,
                AppliedBy = poHeader.ModifiedId ?? poHeader.UsingID ?? "SYSTEM",
                Notes = $"Auto-tracked on PO approval. DocumentType: {poLine.DocumentType}",
                Status = 2,// Active (fallback creation)
                ModificationStatus = 1,// Active
                CreatedAt = DateTime.Now,
                CreatedBy = "SYSTEM"
            };

            _context.PromotionFrontMarginUsages.Add(usage);
            await _context.SaveChangesAsync();

            _logger.LogDebug("Created Front Margin usage record for PO {PONumber} Line {LineNumber}",
            poHeader.Number, poLine.LineNumber);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating Front Margin usage record for PO {PONumber} Line {LineNumber}",
            poHeader.Number, poLine.LineNumber);
        }
    }

    /// <summary>
    /// Determine discount type based on PO line properties
    /// </summary>
    private static int DetermineDiscountType(PurchaseOrderLine poLine)
    {
        // If it's a promotional item (DocumentType = 2), likely a gift
        if (poLine.DocumentType == 2)
        {
            return poLine.UnitCost == 0 ? 4 : 3;// 4 = Different item gift, 3 = Same item gift
        }

        // If has percentage discount
        if (poLine.LineDiscountPercent > 0)
        {
            return 1;// Percentage discount
        }

        // If has fixed amount discount
        if (poLine.LineDiscountAmount > 0)
        {
            return 2;// Fixed amount discount
        }

        return 1;// Default to percentage
    }

    /// <summary>
    /// Calculate original amount, final amount, and discount amount
    /// </summary>
    private static (decimal originalAmount, decimal finalAmount, decimal discountAmount) CalculateDiscountAmounts(
        PurchaseOrderLine poLine)
    {
        var finalAmount = poLine.Amount;
        var discountAmount = poLine.LineDiscountAmount;

        // Calculate original amount before discount
        var originalAmount = finalAmount + discountAmount;

        // If percentage discount, recalculate
        if (poLine.LineDiscountPercent > 0 && discountAmount == 0)
        {
            originalAmount = poLine.UnitCost * poLine.Quantity;
            discountAmount = originalAmount * (poLine.LineDiscountPercent / 100);
        }

        return (originalAmount, finalAmount, discountAmount);
    }

    public async Task<ApiResponse> DeleteMultipleHeaderAsync(List<string> documentNumber)
    {
        try
        {
            var listPurchase = await _context.PurchaseOrderHeaders
                .Where(p => documentNumber.Contains(p.Number))
                .ToListAsync();

            if (listPurchase.Count == 0)
            {
                return ApiResponse.S404("Không tìm thấy đơn hàng cần xóa.");
            }

            listPurchase.ForEach(p => p.Blocked = true);

            await _context.SaveChangesAsync();

            return ApiResponse.S200();
        }
        catch (Exception ex)
        {
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }
    public async Task<ApiResponse> IsRecentOrderFromVendorAsync(List<string> listVendorNumber)
    {
        try
        {
            if (listVendorNumber.Count == 0)
            {
                return new ApiResponse(Status404NotFound, _i18N["Operation Failed"]);
            }
            var today = DateOnly.FromDateTime(DateTime.Now);
            var thirtyDaysAgo = today.AddDays(-30);
            var listVendorInvalid = new List<CheckPaymentDayResponse>();
            var existingPO = _context.PurchaseOrderHeaders
                .AsNoTracking()
                .Where(poh => listVendorNumber.Contains(poh.BuyFromVendorNumber)
                              && poh.ApprovalDate != null
                              && poh.ApprovalDate >= thirtyDaysAgo
                              && poh.ApprovalDate != null
                              && poh.ApprovalDate <= today)
                .ToList();
            if (existingPO.Count == 0)
            {
                return new ApiResponse(Status200OK, _i18N["Operation Successfully"], listVendorInvalid);
            }
            var vendors = await _context.Vendors
                .Where(v => listVendorNumber.Contains(v.Number))
                .ToListAsync();

            foreach (var poh in existingPO)
            {
                var vendor = vendors.Find(v => v.Number == poh.BuyFromVendorNumber.Trim());
                if (vendor == null)
                {
                    continue;
                }
                var now = DateOnly.FromDateTime(DateTime.Now);
                if (poh.ApprovalDate == null)
                {
                    continue;
                }
                // lấy ngày approve + ngày cho phép nhập hàng, nếu không có thì mặc định là 30 ngày
                var canOrderDate = poh.ApprovalDate.Value.AddDays(vendor.PaymentDays ?? 30);
                var daysDifference = canOrderDate.DayNumber - now.DayNumber;

                if (daysDifference > 0)
                {
                    listVendorInvalid.Add(new CheckPaymentDayResponse
                    {
                        PONumber = poh.Number,
                        VendorName = vendor.Name,
                        ValidDate = canOrderDate,
                        VendorNumber = vendor.Number,
                        RemainingDays = daysDifference
                    });
                }
            }
            return new ApiResponse(Status200OK, _i18N["Operation Successfully"], listVendorInvalid);
        }
        catch (Exception ex)
        {
            return new ApiResponse(Status500InternalServerError, ex.GetBaseException().Message);
        }
    }
    public async Task<ApiResponse> IsRecentOrderFromItemAsync(List<string> listItemNumber)
    {
        try
        {
            var existingVendor = _context.Items
                .AsNoTracking()
                .Where(poh => listItemNumber.Contains(poh.Number))
                .Select(i => i.VendorNumber)
                .ToHashSet();
            return await IsRecentOrderFromVendorAsync([.. existingVendor]);
        }
        catch (Exception ex)
        {
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }
    public async Task<ApiResponse> IsRecentOrderFromSuggestDocumentAsync(List<string> listSuggestDocumentNumber)
    {
        try
        {
            var existingVendor = _context.PurchaseSuggestedPaymentLines
                .AsNoTracking()
                .Where(poh => listSuggestDocumentNumber.Contains(poh.DocumentNumber))
                .Select(i => i.BuyFromVendorNumber)
                .Distinct();
            return await IsRecentOrderFromVendorAsync([.. existingVendor]);
        }
        catch (Exception ex)
        {
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }
    public async Task<ApiResponse<decimal>> GetLastPurchasePriceAsync(string itemCode, string unit, string vendorCode)
    {
        // Input validation
        if (string.IsNullOrEmpty(itemCode) || string.IsNullOrEmpty(vendorCode) || string.IsNullOrEmpty(unit))
        {
            return new ApiResponse<decimal>(Status400BadRequest, _i18N["Operation Failed"]);
        }

        try
        {
            var lastUnitCost = await _context.PurchaseOrderHeaders
                .Where(poh => poh.BuyFromVendorNumber == vendorCode)
                .Join(_context.PurchaseOrderLines.Where(pol => pol.ItemNumber == itemCode && pol.UnitOfMeasure == unit),
                outerKeySelector: poh => poh.Number,
                innerKeySelector: pol => pol.DocumentNumber,
                resultSelector: (poh, pol) => new
                {
                    poh.OrderDate,
                    pol.UnitCost
                })
                .OrderByDescending(x => x.OrderDate)
                .Select(x => x.UnitCost)
                .FirstOrDefaultAsync();

            return new ApiResponse<decimal>(Status200OK, _i18N["Operation Successful"], lastUnitCost);
        }
        catch (Exception ex)
        {
            return ApiResponse.S500(_i18N["Get last unit cost has error: {0}", ex.GetBaseException().Message]);
        }
    }

    public async Task<ApiResponse<GetLatestPriceDto>> GetLatestPriceBySku(string itemCode, string unit, string vendorCode)
    {
        try
        {
            var latestPriceData = new GetLatestPriceDto();

            var itemData = await _context.Items
                .Where(x => x.Number == itemCode)
                .Select(x => new
                {
                    x.UnitCost,
                    x.Number,
                    x.VatproductPostingGroup
                })
                .FirstOrDefaultAsync();

            if (itemData == null)
            {
                return new ApiResponse<GetLatestPriceDto>(404, _i18N["Could not find item with code {0}", itemCode]);
            }

            var itemUnit = await _context.ItemUnitOfMeasures
                .Where(x => x.ItemNumber == itemCode && x.Code == unit)
                .Select(x => new
                {
                    x.Code,
                    x.QuantityPerUnitOfMeasure
                })
                .FirstOrDefaultAsync();
            if (itemUnit == null)
            {
                return new ApiResponse<GetLatestPriceDto>(404,
                _i18N["Could not find unit of measure {0} for item {1}", unit, itemCode]);
            }

            latestPriceData.LastUnitCost = itemData.UnitCost ?? 0;
            latestPriceData.QtyPerUnitOfMeasure = itemUnit.QuantityPerUnitOfMeasure;
            latestPriceData.UnitCost = (itemData.UnitCost ?? 0) * itemUnit.QuantityPerUnitOfMeasure;
            latestPriceData.VAT = itemData.VatproductPostingGroup != null ? Decimal.Parse(itemData.VatproductPostingGroup) : 0;

            return ApiResponse.S200(result: latestPriceData);
        }
        catch (Exception ex)
        {
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }

    public async Task<ApiResponse> GetUserCreatedPOByPONumberAsync(string poNumber)
    {
        try
        {
            var exitingPO = await _context.PurchaseOrderHeaders.AsNoTracking()
                .FirstOrDefaultAsync(x => x.Number == poNumber);
            if (exitingPO is null)
            {
                return ApiResponse.S404($"PO: {poNumber} could not found");
            }
            var purchaserCode = exitingPO.PurchaserCode;

            var existingUser = _context.Users.AsNoTracking().FirstOrDefault(x => x.UserName == purchaserCode);
            if (existingUser is null)
            {
                return ApiResponse.S404($"User name: {purchaserCode} could not found");
            }
            var result = _mapper.Map<UserInfoDto>(existingUser);
            return ApiResponse.S200("Operation Successfully", result);
        }
        catch (Exception ex)
        {
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }

    public async Task<ApiResponse> PoAutoCreateAsync(string loginId)
    {
        try
        {
            var paras = new DynamicParameters();
            paras.Add("loginId", RemoveSpecialChar(loginId), DbType.String);

            const string query = "SET ARITHABORT ON EXEC TSERP_PurchaseOrder_AutoCreate @loginId";

            await using var connection = new SqlConnection(_dapperContext.connectionString);
            var responseData = await connection.QueryAsync<int>(query, paras).ConfigureAwait(false);
            return responseData.FirstOrDefault() == 0 ? ApiResponse.S404() : ApiResponse.S200();
        }
        catch (Exception e)
        {
            return ApiResponse.S500(e.GetBaseException().Message);
        }
    }

    public async Task<byte[]> ViewPDF(string code)
    {
        var poHeader = RemoveSpecialChar(code);
        const string getPoHeader = "SET ARITHABORT ON EXEC TSERP_PurchaseOrder_GetDocument @poHeader";
        await using var connection = new SqlConnection(_dapperContext.connectionString);
        var rsp = await connection.QueryAsync<POReportPDF>(getPoHeader, new
        {
            poHeader
        }).ConfigureAwait(false);
        {
            var pdf = POTemplate.GenerationPDF(rsp);
            return pdf;
        }
    }

    public async Task<ApiResponse> UploadFile(POFileData objFile)
    {
        try
        {
            const string folderName = "//Report//PO//";
            var filePath = folderName + objFile.FileName;
            await using (var filestream = File.Create(Environment.WebRootPath + filePath + ".pdf"))
            {
                await filestream.WriteAsync(objFile.ImageBytes);
            }
            if (_httpContextAccessor.HttpContext == null)
            {
                return ApiResponse.S404("HttpContext is null");
            }
            var scheme = new Uri(
            $"{_httpContextAccessor.HttpContext.Request.Scheme}://{_httpContextAccessor.HttpContext.Request.Host}");
            var pathFile = "Report/PO/" + objFile.FileName + ".pdf";
            var url = scheme.AbsoluteUri + pathFile;
            return ApiResponse.S200("Save file success", url);
        }
        catch (Exception ex)
        {
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }

    /// <summary>
    /// Get ProgramNumber for a promotion
    /// </summary>
    private async Task<string> GetProgramNumberForPromotionAsync(string? promotionNumber)
    {
        if (string.IsNullOrEmpty(promotionNumber))
            return "UNKNOWN";

        try
        {
            var promotion = await _context.PromotionFrontMargins
                .FirstOrDefaultAsync(p => p.Number == promotionNumber);

            return promotion?.ProgramNumber ?? "UNKNOWN";
        }
        catch
        {
            return "UNKNOWN";
        }
    }
}
