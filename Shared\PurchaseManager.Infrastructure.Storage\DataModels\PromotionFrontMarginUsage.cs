using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;

namespace PurchaseManager.Infrastructure.Storage.DataModels;

/// <summary>
/// Front Margin promotion usage tracking entity
/// Stores information about when and how Front Margin promotions are applied to PO lines
/// </summary>
[Table("PromotionFrontMarginUsages")]
public class PromotionFrontMarginUsage : FullTrackingEntity
{
    /// <summary>
    /// Reference to the Front Margin promotion that was applied (FK to PromotionFrontMargin.Number)
    /// </summary>
    [Required]
    [StringLength(50)]
    public string PromotionNumber { get; set; } = string.Empty;

    /// <summary>
    /// PO Number where the promotion was applied
    /// </summary>
    [Required]
    [StringLength(50)]
    public string PONumber { get; set; } = string.Empty;

    /// <summary>
    /// PO Line Number where the promotion was applied
    /// </summary>
    [Required]
    public int POLineNumber { get; set; }

    /// <summary>
    /// Item number that received the promotion
    /// </summary>
    [Required]
    [StringLength(50)]
    public string ItemNumber { get; set; } = string.Empty;

    /// <summary>
    /// Item name for display purposes
    /// </summary>
    [StringLength(250)]
    public string ItemName { get; set; } = string.Empty;

    /// <summary>
    /// Vendor code
    /// </summary>
    [Required]
    [StringLength(20)]
    public string VendorCode { get; set; } = string.Empty;

    /// <summary>
    /// Discount type applied: 1=Percentage, 2=FixedAmount, 3=SameItemGift, 4=DifferentItemGift
    /// </summary>
    [Required]
    public int DiscountType { get; set; }

    /// <summary>
    /// Original quantity before promotion
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal OriginalQuantity { get; set; }

    /// <summary>
    /// Final quantity after promotion (for same item gifts)
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal FinalQuantity { get; set; }

    /// <summary>
    /// Original unit cost before promotion
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal OriginalUnitCost { get; set; }

    /// <summary>
    /// Final unit cost after promotion
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal FinalUnitCost { get; set; }

    /// <summary>
    /// Discount percentage applied (for percentage discounts)
    /// </summary>
    [Column(TypeName = "decimal(5,2)")]
    public decimal DiscountPercentage { get; set; }

    /// <summary>
    /// Fixed discount amount applied (for fixed amount discounts)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal FixedDiscountAmount { get; set; }

    /// <summary>
    /// Total discount amount calculated
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,2)")]
    public decimal TotalDiscountAmount { get; set; }

    /// <summary>
    /// Original line amount before promotion
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,2)")]
    public decimal OriginalLineAmount { get; set; }

    /// <summary>
    /// Final line amount after promotion
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,2)")]
    public decimal FinalLineAmount { get; set; }

    /// <summary>
    /// Gift quantity for same item gifts
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal GiftQuantity { get; set; }

    /// <summary>
    /// Gift item number for different item gifts
    /// </summary>
    [StringLength(50)]
    public string? GiftItemNumber { get; set; }

    /// <summary>
    /// Gift item name for different item gifts
    /// </summary>
    [StringLength(250)]
    public string? GiftItemName { get; set; }

    /// <summary>
    /// Gift item quantity for different item gifts
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal GiftItemQuantity { get; set; }

    /// <summary>
    /// Gift line PO number (for different item gifts)
    /// </summary>
    [StringLength(50)]
    public string? GiftPOLineNumber { get; set; }

    /// <summary>
    /// When the promotion was applied
    /// </summary>
    [Required]
    public DateTime AppliedDate { get; set; } = DateTime.Now;

    /// <summary>
    /// User who applied the promotion
    /// </summary>
    [Required]
    [StringLength(100)]
    public string AppliedBy { get; set; } = string.Empty;

    /// <summary>
    /// Additional notes about the promotion application
    /// </summary>
    [StringLength(500)]
    public string? Notes { get; set; }

    /// <summary>
    /// Gift calculation type: 1=Progressive, 2=Milestone
    /// Only used for DiscountType = 3 (Same Item Gift) and 4 (Different Item Gift)
    /// </summary>
    public int? GiftCalculationType { get; set; } = 1; // Default to Progressive

    /// <summary>
    /// Tier quantity threshold for bonus calculation
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? TierQuantityThreshold { get; set; }

    /// <summary>
    /// Tier bonus percentage (if quantity meets threshold)
    /// </summary>
    [Column(TypeName = "decimal(5,2)")]
    public decimal? TierBonusPercentage { get; set; }

    /// <summary>
    /// Tier bonus amount (if quantity meets threshold)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? TierBonusAmount { get; set; }

    /// <summary>
    /// Status: 1=Draft, 2=Active
    /// </summary>
    [Required]
    public int Status { get; set; } = 1;

    // Navigation Properties
    [ForeignKey(nameof(PromotionNumber))]
    public virtual PromotionFrontMargin PromotionFrontMargin { get; set; } = null!;
}
