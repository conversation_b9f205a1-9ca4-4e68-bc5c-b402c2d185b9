namespace PurchaseManager.Shared.Dto.Promotions.FrontMargins;

/// <summary>
/// DTO for retrieving Front Margin promotion usage records
/// </summary>
public class GetPromotionFrontMarginUsageDto
{
    public long Id { get; set; }

    /// <summary>
    /// Reference to the Front Margin promotion that was applied
    /// </summary>
    public string PromotionNumber { get; set; } = string.Empty;

    /// <summary>
    /// Promotion program name for display
    /// </summary>
    public string PromotionProgramName { get; set; } = string.Empty;

    /// <summary>
    /// PO Number where the promotion was applied
    /// </summary>
    public string PONumber { get; set; } = string.Empty;

    /// <summary>
    /// PO Line Number where the promotion was applied
    /// </summary>
    public int POLineNumber { get; set; }

    /// <summary>
    /// Item number that received the promotion
    /// </summary>
    public string ItemNumber { get; set; } = string.Empty;

    /// <summary>
    /// Item name for display purposes
    /// </summary>
    public string ItemName { get; set; } = string.Empty;

    /// <summary>
    /// Vendor code
    /// </summary>
    public string VendorCode { get; set; } = string.Empty;

    /// <summary>
    /// Vendor name for display
    /// </summary>
    public string VendorName { get; set; } = string.Empty;

    /// <summary>
    /// Discount type applied: 1=Percentage, 2=FixedAmount, 3=SameItemGift, 4=DifferentItemGift
    /// </summary>
    public int DiscountType { get; set; }

    /// <summary>
    /// Discount type name for display
    /// </summary>
    public string DiscountTypeName { get; set; } = string.Empty;

    /// <summary>
    /// Original quantity before promotion
    /// </summary>
    public decimal OriginalQuantity { get; set; }

    /// <summary>
    /// Final quantity after promotion (for same item gifts)
    /// </summary>
    public decimal FinalQuantity { get; set; }

    /// <summary>
    /// Original unit cost before promotion
    /// </summary>
    public decimal OriginalUnitCost { get; set; }

    /// <summary>
    /// Final unit cost after promotion
    /// </summary>
    public decimal FinalUnitCost { get; set; }

    /// <summary>
    /// Discount percentage applied (for percentage discounts)
    /// </summary>
    public decimal DiscountPercentage { get; set; }

    /// <summary>
    /// Fixed discount amount applied (for fixed amount discounts)
    /// </summary>
    public decimal FixedDiscountAmount { get; set; }

    /// <summary>
    /// Total discount amount calculated
    /// </summary>
    public decimal TotalDiscountAmount { get; set; }

    /// <summary>
    /// Original line amount before promotion
    /// </summary>
    public decimal OriginalLineAmount { get; set; }

    /// <summary>
    /// Final line amount after promotion
    /// </summary>
    public decimal FinalLineAmount { get; set; }

    /// <summary>
    /// Savings amount (Original - Final)
    /// </summary>
    public decimal SavingsAmount => OriginalLineAmount - FinalLineAmount;

    /// <summary>
    /// Savings percentage
    /// </summary>
    public decimal SavingsPercentage => OriginalLineAmount > 0 ? (SavingsAmount / OriginalLineAmount) * 100 : 0;

    /// <summary>
    /// Gift quantity for same item gifts
    /// </summary>
    public decimal GiftQuantity { get; set; }

    /// <summary>
    /// Gift item number for different item gifts
    /// </summary>
    public string? GiftItemNumber { get; set; }

    /// <summary>
    /// Gift item name for different item gifts
    /// </summary>
    public string? GiftItemName { get; set; }

    /// <summary>
    /// Gift item quantity for different item gifts
    /// </summary>
    public decimal GiftItemQuantity { get; set; }

    /// <summary>
    /// Gift line PO number (for different item gifts)
    /// </summary>
    public string? GiftPOLineNumber { get; set; }

    /// <summary>
    /// When the promotion was applied
    /// </summary>
    public DateTime AppliedDate { get; set; }

    /// <summary>
    /// User who applied the promotion
    /// </summary>
    public string AppliedBy { get; set; } = string.Empty;

    /// <summary>
    /// Additional notes about the promotion application
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Status: 1=Active, 2=Cancelled, 3=Modified
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// Status name for display
    /// </summary>
    public string StatusName { get; set; } = string.Empty;

    /// <summary>
    /// Created date
    /// </summary>
    public DateTime CreatedDate { get; set; }

    /// <summary>
    /// Created by user
    /// </summary>
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// Last modified date
    /// </summary>
    public DateTime? ModifiedDate { get; set; }

    /// <summary>
    /// Last modified by user
    /// </summary>
    public string? ModifiedBy { get; set; }

    /// <summary>
    /// Gift calculation type: 1=Progressive, 2=Milestone
    /// Only used for DiscountType = 3 (Same Item Gift) and 4 (Different Item Gift)
    /// </summary>
    public int? GiftCalculationType { get; set; } = 1; // Default to Progressive

    /// <summary>
    /// Tier quantity threshold for bonus calculation
    /// </summary>
    public decimal? TierQuantityThreshold { get; set; }

    /// <summary>
    /// Tier bonus percentage (if quantity meets threshold)
    /// </summary>
    public decimal? TierBonusPercentage { get; set; }

    /// <summary>
    /// Tier bonus amount (if quantity meets threshold)
    /// </summary>
    public decimal? TierBonusAmount { get; set; }

    /// <summary>
    /// Display summary for UI
    /// </summary>
    public string DisplaySummary => GetDisplaySummary();

    private string GetDisplaySummary()
    {
        return DiscountType switch
        {
            1 => $"Giảm {DiscountPercentage}% - Tiết kiệm {SavingsAmount:N0} VND",
            2 => $"Giảm {FixedDiscountAmount:N0} VND - Tiết kiệm {SavingsAmount:N0} VND",
            3 => $"Mua {OriginalQuantity} tặng {GiftQuantity} - Tiết kiệm {SavingsAmount:N0} VND",
            4 => $"Tặng {GiftItemQuantity} {GiftItemName} - Tiết kiệm {SavingsAmount:N0} VND",
            _ => $"Tiết kiệm {SavingsAmount:N0} VND"
        };
    }
}
