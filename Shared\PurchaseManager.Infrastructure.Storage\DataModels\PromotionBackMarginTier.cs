using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;
namespace PurchaseManager.Infrastructure.Storage.DataModels;

/// <summary>
/// Back Margin tier configuration for progressive and stepwise calculations
/// Stores threshold values and corresponding discount rates
/// </summary>
[Table("PromotionBackMarginTiers")]
public class PromotionBackMarginTier : FullTrackingEntity
{
    /// <summary>
    /// Reference to PromotionBackMargin
    /// </summary>
    [Required]
    [StringLength(50)]
    public string BackMarginNumber { get; set; } = string.Empty;

    /// <summary>
    /// Tier sequence number (1, 2, 3...)
    /// </summary>
    [Required]
    public int TierLevel { get; set; }

    /// <summary>
    /// For Revenue types: Minimum revenue threshold
    /// For Quantity types: Minimum quantity threshold
    /// For Early Payment: Not used
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? MinimumThreshold { get; set; }

    /// <summary>
    /// For Revenue types: Maximum revenue threshold (null for unlimited)
    /// For Quantity types: Maximum quantity threshold (null for unlimited)
    /// For Early Payment: Not used
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? MaximumThreshold { get; set; }

    /// <summary>
    /// For Progressive types: Milestone value (doanh số mốc, số lượng mốc)
    /// For Stepwise types: Not used (use Min/Max instead)
    /// For Early Payment: Not used
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? MilestoneValue { get; set; }

    /// <summary>
    /// Discount percentage (for DiscountValueType = 1)
    /// </summary>
    [Column(TypeName = "decimal(5,2)")]
    public decimal? DiscountPercentage { get; set; }

    /// <summary>
    /// Fixed discount amount (for DiscountValueType = 2)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? FixedDiscountAmount { get; set; }

    /// <summary>
    /// Maximum discount amount limit
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? MaximumDiscountAmount { get; set; }

    /// <summary>
    /// Tier Type: PROGRESSIVE or TIERED
    /// </summary>
    [Required]
    [StringLength(20)]
    public string TierType { get; set; } = string.Empty;

    [Required]
    public bool IsActive { get; set; } = true;

    [Required]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Navigation Properties
    [ForeignKey(nameof(BackMarginNumber))]
    public virtual PromotionBackMargin BackMargin { get; set; } = null!;
}
