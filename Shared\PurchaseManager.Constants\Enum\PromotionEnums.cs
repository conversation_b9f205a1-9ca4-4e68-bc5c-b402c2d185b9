namespace PurchaseManager.Constants.Enum;

/// <summary>
/// Loại chương trình khuyến mãi
/// </summary>
public enum PromotionProgramTypeEnum
{
    FrontMargin = 1,
    BackMargin = 2
}

/// <summary>
/// Trạng thái chương trình khuyến mãi
/// </summary>
public enum PromotionStatusEnum
{
    Draft = 1,      // Nháp
    Active = 2,     // Đang áp dụng
    Inactive = 3,   // Hết hiệu lực
    Expired = 4     // Hết hạn
}

/// <summary>
/// Trạng thái phê duyệt
/// </summary>
public enum PromotionApprovalStatusEnum
{
    Pending = 1,            // Chờ phê duyệt
    VendorApproved = 2,     // Vendor đã phê duyệt
    PurchaserApproved = 3,  // Purchaser đã phê duyệt
    FullyApproved = 4,      // <PERSON><PERSON> phê duyệt hoàn toàn
    Rejected = 5            // <PERSON><PERSON> từ chối
}

/// <summary>
/// <PERSON><PERSON><PERSON> thức chi trả/áp dụng
/// </summary>
public enum PromotionPaymentMethodEnum
{
    Cash = 1,           // Tiền mặt
    Transfer = 2,       // Chuyển khoản
    DebtOffset = 3,     // Trừ công nợ
    Goods = 4,          // Hàng
    Discount = 5        // Giảm giá trực tiếp (FrontMargin)
}

/// <summary>
/// Thời điểm chi trả/áp dụng
/// </summary>
public enum PromotionPaymentTimingEnum
{
    Immediate = 1,      // Ngay lập tức (FrontMargin)
    NextOrder = 2,      // Đơn hàng tiếp theo
    AfterPayment = 3,   // Sau khi thanh toán đơn hàng
    Plus30Days = 4,     // n + 30 ngày
    Plus60Days = 5,     // n + 60 ngày
    Plus90Days = 6      // n + 90 ngày
}

/// <summary>
/// Chu kỳ tính toán/đánh giá
/// </summary>
public enum PromotionCalculationPeriodEnum
{
    PerOrder = 1,       // Theo từng đơn hàng (FrontMargin)
    Monthly = 2,        // Hàng tháng
    Quarterly = 3,      // Hàng quý
    Yearly = 4          // Hàng năm
}

/// <summary>
/// Loại chiết khấu Back Margin
/// </summary>
public enum BackMarginDiscountTypeEnum
{
    /// <summary>
    /// Chiết khấu theo doanh số lũy tiến
    /// Back Margin = Phần nguyên của (Doanh số mua / Doanh số mốc) * Mức chiết khấu
    /// </summary>
    RevenueProgressive = 1,

    /// <summary>
    /// Chiết khấu theo doanh số bậc thang
    /// Doanh số tối thiểu < Doanh số đạt < Doanh số tối đa => Back Margin = Doanh số đạt * Mức chiết khấu của bậc
    /// </summary>
    RevenueStepwise = 2,

    /// <summary>
    /// Chiết khấu theo số lượng lũy tiến
    /// Back Margin = Phần nguyên của (Số lượng mua / Số lượng mốc) * Mức chiết khấu
    /// </summary>
    QuantityProgressive = 3,

    /// <summary>
    /// Chiết khấu theo số lượng bậc thang
    /// Số lượng tối thiểu < Số lượng đạt < Số lượng tối đa => Back Margin = Số lượng đạt * Mức chiết khấu của bậc
    /// </summary>
    QuantityStepwise = 4,

    /// <summary>
    /// Chiết khấu thanh toán sớm
    /// Số ngày thanh toán từ khi nhận hàng
    /// </summary>
    EarlyPayment = 5
}

/// <summary>
/// Loại giá trị chiết khấu
/// </summary>
public enum DiscountValueTypeEnum
{
    Percentage = 1,     // Phần trăm doanh số
    FixedAmount = 2     // Số tiền cố định
}

/// <summary>
/// Loại điều kiện promotion
/// </summary>
public enum PromotionConditionTypeEnum
{
    FrontMargin = 1,                // Front Margin condition
    BackMarginRevenue = 2,          // Back Margin doanh số
    BackMarginQuantity = 3,         // Back Margin số lượng
    BackMarginEarlyPayment = 4      // Back Margin thanh toán sớm
}

/// <summary>
/// Loại phần thưởng promotion
/// </summary>
public enum PromotionRewardTypeEnum
{
    // Front Margin rewards
    ProductDiscount = 1,    // Giảm giá sản phẩm
    Gift = 2,              // Tặng phẩm
    Voucher = 3,           // Voucher

    // Back Margin rewards
    Cash = 4,              // Tiền mặt
    Transfer = 5,          // Chuyển khoản
    DebtOffset = 6,        // Trừ công nợ
    Goods = 7              // Hàng
}

/// <summary>
/// Loại tính toán Back Margin
/// </summary>
public enum BackMarginCalculationTypeEnum
{
    Progressive = 1,        // Lũy tiến
    Stepwise = 2           // Bậc thang
}

/// <summary>
/// Hình thức chi trả Back Margin
/// </summary>
public enum BackMarginPaymentMethodEnum
{
    Cash = 1,           // Tiền mặt
    Transfer = 2,       // Chuyển khoản
    DebtOffset = 3,     // Trừ công nợ
    Goods = 4           // Hàng
}

/// <summary>
/// Thời điểm chi trả Back Margin
/// </summary>
public enum BackMarginPaymentTimingEnum
{
    NextOrder = 1,      // Đơn hàng tiếp theo
    AfterPayment = 2,   // Sau khi thanh toán đơn hàng
    Plus30Days = 3,     // n + 30 ngày
    Plus60Days = 4,     // n + 60 ngày
    Plus90Days = 5      // n + 90 ngày
}

/// <summary>
/// Trạng thái xử lý Back Margin Tracking
/// </summary>
public enum BackMarginTrackingStatusEnum
{
    Pending = 1,        // Chưa xử lý
    Processed = 2,      // Đã tính Back Margin
    Paid = 3           // Đã trả thưởng
}

/// <summary>
/// Trạng thái Back Margin Earned
/// </summary>
public enum BackMarginEarnedStatusEnum
{
    Calculated = 1,     // Đã tính, chờ trả
    Approved = 2,       // Đã duyệt, sẵn sàng trả
    Paid = 3,          // Đã trả
    Cancelled = 4       // Hủy
}

/// <summary>
/// Trạng thái thanh toán Back Margin
/// </summary>
public enum BackMarginPaymentStatusEnum
{
    Pending = 1,        // Chờ xử lý
    Completed = 2,      // Đã hoàn thành
    Failed = 3,         // Thất bại
    Cancelled = 4       // Hủy
}

/// <summary>
/// Trạng thái phê duyệt thanh toán Back Margin
/// </summary>
public enum BackMarginPaymentApprovalStatusEnum
{
    Pending = 1,        // Chờ duyệt
    Approved = 2,       // Đã duyệt
    Rejected = 3        // Từ chối
}

/// <summary>
/// Conflict Resolution Mode cho multiple promotions
/// </summary>
public enum PromotionConflictResolutionEnum
{
    /// <summary>
    /// Tự động chọn CTKM có số tiền chiết khấu nhiều nhất
    /// </summary>
    AutoSelectHighestAmount = 1,

    /// <summary>
    /// Cho User chọn CTKM
    /// </summary>
    ManualSelection = 2,

    /// <summary>
    /// Theo Priority field trong PromotionHeaders
    /// </summary>
    ByPriority = 3
}
