﻿using System.Globalization;
using AutoMapper;
using Microsoft.JSInterop;
using PurchaseManager.Constants;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models.Report.PO;
namespace PurchaseManager.Theme.Material.Demo.Pages.PurchaseOrder.Services;

public class POServicesHandler
{
    private readonly IPurchaseOrderApiClient _apiClient;
    private readonly IMapper _mapper;
    private readonly IItemApiClient _itemApiClient;
    public POServicesHandler(IPurchaseOrderApiClient apiClient, IMapper mapper, IItemApiClient itemApiClient)
    {
        _apiClient = apiClient;
        _mapper = mapper;
        _itemApiClient = itemApiClient;
    }

    public async Task<(bool IsSuccess, string Message)> SaveHeaderAndProcessAsync(UpdatePOHeaderDto dto, POHeaderGetDto header)
    {
        var updateResult = await _apiClient.UpdateHeader(dto);
        if (!updateResult.IsSuccessStatusCode)
        {
            return (false, updateResult.Message);
        }

        if (!string.IsNullOrEmpty(header.PurchaserApprovalBy) && !string.IsNullOrEmpty(header.VendorApprovalBy))
        {
            var approveResult = await _apiClient.ApproveHeader(header.Number);
            if (!approveResult.IsSuccessStatusCode)
            {
                return (approveResult.IsSuccessStatusCode, approveResult.Message);
            }

            header.Status = approveResult.Result;
        }
        else
        {
            var rejectResult = await _apiClient.RejectHeader(header.Number);
            if (!rejectResult.IsSuccessStatusCode)
            {
                return (rejectResult.IsSuccessStatusCode, rejectResult.Message);
            }

            header.Status = rejectResult.Result;
        }

        if (header.Status < (int)PurchaseOrderEnum.Confirm)
        {
            var confirmResult = await _apiClient.ConfirmHeader(header.Number);
            if (!confirmResult.IsSuccessStatusCode)
            {
                return (false, confirmResult.Message);
            }

            header.Status = confirmResult.Result;
        }

        var closeResult = await _apiClient.CloseDocument(header.Number);
        return !closeResult.IsSuccessStatusCode ? (closeResult.IsSuccessStatusCode, closeResult.Message) : (true, "Success");
    }

    public async Task<(bool IsSuccess, string Message)> ExportDetailPo(List<POLineGetDto> poLineCalculated, POHeaderGetDto poHeader, IJSRuntime jsRuntime)
    {
        try
        {
            var headers = new List<string>
            {
                    "PO Number",
                    "Vendor No",
                    "Item No",
                    "Item Name",
                    "Unit",
                    "Quantity",
                    "Price",
                    "Amount",
                    "Discount",
                    "Vat Base Amount",
                    "Vat",
                    "Vat Amount",
                    "Amount Including Vat",
                    "Description"
                };

            foreach (var item in poLineCalculated)
            {
                item.UpdateAmount();
            }
            var dataExcel = poLineCalculated.Select(line => new PoDetailModel()
            {
                Vat = line.Vat.ToString(CultureInfo.InvariantCulture),
                ItemNo = line.ItemNumber,
                ItemName = line.ItemName,
                Unit = line.UnitOfMeasure,
                PONumber = line.DocumentNumber,
                Amount = line.Amount.ToString(CultureInfo.InvariantCulture),
                Description = line.Description,
                Price = line.UnitCost.ToString(CultureInfo.InvariantCulture),
                Quantity = line.Quantity.ToString(CultureInfo.InvariantCulture),
                VatAmount = line.VatAmount.ToString(CultureInfo.InvariantCulture),
                VendorNo = poHeader.BuyFromVendorNumber,
                VatBaseAmount = line.VatBaseAmount.ToString(CultureInfo.InvariantCulture),
                Discount = line.LineDiscountAmount.ToString(CultureInfo.InvariantCulture),
                VatIncludingAmount = line.AmountIncludingVat.ToString(CultureInfo.InvariantCulture)
            }
            ).ToList();
            var excelOpts = new ExcelExportOptions
            {
                SheetName = "PO Detail " + poHeader.OrderDate.ToString("dd-MM-yyyy"),
                AutoFilter = false
            };
            var fileData = ExcelExportExtension.ExportToExcel(headers, dataExcel, excelOpts);
            if (fileData == null)
            {
                return (false, "Cannot export file at the moment. Try again!");
            }
            var fileName = poHeader.Number + "_Detail_" + poHeader.OrderDate.ToString("dd-MM-yyyy") + "_" +
                           DateTime.Now.ToString("HH-mm-ss") + ".xlsx";
            await jsRuntime.InvokeVoidAsync("downloadFileFromBase64", fileName, fileData);
            return (true, "File exported successfully.");
        }
        catch (Exception ex)
        {
            return (false, ex.GetBaseException().Message);
        }
    }

    public async Task<bool> ApplySameItemGiftToPOLine(POLineGetDto poLine, GetPromotionFrontMarginDto promotion,
        List<POLineGetDto> poLines, string documentNumber)
    {
        try
        {
            // Case 2: Same Item Gift - "Mua X tặng Y cùng loại"
            // Logic: Tạo separate gift line với DocumentType = Promotional
            // Validate promotion data
            if (promotion.BuyQuantity <= 0 || promotion.GiftQuantity <= 0)
            {
                return false;
            }

            // Check if current quantity meets buy requirement
            if (poLine.Quantity < promotion.BuyQuantity)
            {
                return false;
            }

            // Additional validation for MinimumQuantity and MinimumAmount
            var lineAmount = poLine.Quantity * poLine.UnitCost;

            // Check minimum quantity requirement
            if (promotion.MinimumQuantity > 0 && poLine.Quantity < promotion.MinimumQuantity)
            {
                return false;
            }

            // Check minimum amount requirement
            if (promotion.MinimumAmount > 0 && lineAmount < promotion.MinimumAmount)
            {
                return false;
            }

            // Calculate how many gift sets can be applied with GiftCalculationType consideration
            var giftSetsEligible = Math.Floor(poLine.Quantity / promotion.BuyQuantity);

            // Apply GiftCalculationType logic for Case 3 (Same Item Gift)
            var totalGiftQuantity = promotion.GiftCalculationType switch
            {
                1 =>// Progressive (Lũy kế) - tặng theo từng set
                    giftSetsEligible * promotion.GiftQuantity,
                2 =>// Fixed (Cố định) - chỉ tặng 1 lần với quantity cố định
                    giftSetsEligible > 0 ? promotion.GiftQuantity : 0,
                _ => giftSetsEligible * promotion.GiftQuantity
            };

            // Check if gift line already exists for this promotion
            var existingGiftLine = poLines?.FirstOrDefault(line =>
                string.Equals(line.ItemNumber, poLine.ItemNumber, StringComparison.OrdinalIgnoreCase) &&
                line.DocumentType == (int)DocNoOccurrenceEnum.Promotional &&
                line.Description?.Contains($"Gift-{promotion.Number}") == true);

            if (existingGiftLine != null)
            {
                // Update existing gift line
                existingGiftLine.Quantity = totalGiftQuantity;
                existingGiftLine.UnitCost = 0;// Gifts are free
                existingGiftLine.Amount = 0;
                existingGiftLine.UpdateAmount();

                var updateGiftLine = _mapper.Map<POLineAddOrUpdate>(existingGiftLine);
                await _apiClient.UpdatePurchaseOrderLine(updateGiftLine);

            }
            else
            {
                // Create new gift line
                var giftLine = new POLineGetDto
                {
                    DocumentNumber = documentNumber,
                    DocumentType = (int)DocNoOccurrenceEnum.Promotional,// Gift line
                    ItemNumber = poLine.ItemNumber,// Same item
                    ItemName = poLine.ItemName,
                    Description = $"{poLine.Description} - Gift-{promotion.Number}",
                    Quantity = totalGiftQuantity,
                    UnitOfMeasure = poLine.UnitOfMeasure,
                    UnitCost = 0,// Gifts are free
                    UnitPrice = 0,
                    Amount = 0,
                    Vat = 0,// No VAT on gifts
                    LineNumber = (poLines?.Max(x => x.LineNumber) ?? 0) + 1,
                    LotNo = "KM",// Khuyến mãi
                    ExpirationDate = DateOnly.FromDateTime(DateTime.Today.AddDays(30)),
                    QtyPerUnitOfMeasure = poLine.QtyPerUnitOfMeasure
                };

                giftLine.UpdateAmount();
                var giftLineInsert = _mapper.Map<POLineAddOrUpdate>(giftLine);
                giftLineInsert.Desc = giftLine.Description;

                var addGiftLineResponse = await _apiClient.AddLine(giftLineInsert);

                if (addGiftLineResponse.IsSuccessStatusCode)
                {
                    poLines?.Add(giftLine);
                }
                else
                {
                    return false;
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error applying Same Item Gift: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> ApplyDifferentItemGiftToPOLine(POLineGetDto poLine, GetPromotionFrontMarginDto promotion,
        List<POLineGetDto> poLines, string documentNumber)
    {
        try
        {
            // Case 3: Different Item Gift - "Mua X tặng Y khác loại"
            // Logic: Tạo separate gift line với DocumentType = Promotional và ItemNumber khác

            // Validate promotion data
            if (string.IsNullOrEmpty(promotion.GiftItemNumber) || promotion.BuyQuantity <= 0 || promotion.GiftQuantity <= 0)
            {
                return false;
            }

            // Check if current quantity meets buy requirement
            if (poLine.Quantity < promotion.BuyQuantity)
            {
                return false;
            }

            // Calculate how many gift sets can be applied with GiftCalculationType consideration
            var giftSetsEligible = Math.Floor(poLine.Quantity / promotion.BuyQuantity);

            // Apply GiftCalculationType logic for Case 4 (Different Item Gift)
            var totalGiftQuantity = promotion.GiftCalculationType switch
            {
                1 =>// Progressive (Lũy kế) - tặng theo từng set
                    giftSetsEligible * promotion.GiftQuantity,
                2 =>// Fixed (Cố định) - chỉ tặng 1 lần với quantity cố định
                    giftSetsEligible > 0 ? promotion.GiftQuantity : 0,
                _ => giftSetsEligible * promotion.GiftQuantity
            };

            // Check if gift line already exists for this promotion
            var existingGiftLine = poLines?.FirstOrDefault(line =>
                string.Equals(line.ItemNumber, promotion.GiftItemNumber, StringComparison.OrdinalIgnoreCase) &&
                line.DocumentType == (int)DocNoOccurrenceEnum.Promotional &&
                line.Description?.Contains($"Gift-{promotion.Number}") == true);

            if (existingGiftLine != null)
            {
                // Update existing gift line
                existingGiftLine.Quantity = totalGiftQuantity;
                existingGiftLine.UnitCost = 0;// Gifts are free
                existingGiftLine.UnitPrice = 0;
                existingGiftLine.Amount = 0;
                existingGiftLine.UpdateAmount();

                var updateGiftLine = _mapper.Map<POLineAddOrUpdate>(existingGiftLine);
                await _apiClient.UpdatePurchaseOrderLine(updateGiftLine);
            }
            else
            {
                // Get gift item information
                var giftItemResponse = await _itemApiClient.GetItemUnitOfMeasure(promotion.GiftItemNumber);
                var giftItemUOM = giftItemResponse.IsSuccessStatusCode && giftItemResponse.Result?.Count > 0
                    ? giftItemResponse.Result.First().Code
                    : "EA";// Default unit

                // Create new gift line with different item
                var giftLine = new POLineGetDto
                {
                    DocumentNumber = documentNumber,
                    DocumentType = (int)DocNoOccurrenceEnum.Promotional,// Gift line
                    ItemNumber = promotion.GiftItemNumber,// Different item
                    ItemName = promotion.GiftItemName ?? promotion.GiftItemNumber,
                    Description = $"Gift {promotion.GiftItemName ?? promotion.GiftItemNumber} - Gift-{promotion.Number}",
                    Quantity = totalGiftQuantity,
                    UnitOfMeasure = giftItemUOM,
                    UnitCost = 0,// Gifts are free
                    UnitPrice = 0,
                    Amount = 0,
                    Vat = 0,// No VAT on gifts
                    LineNumber = (poLines?.Max(x => x.LineNumber) ?? 0) + 1,
                    LotNo = "KM",// Khuyến mãi
                    ExpirationDate = DateOnly.FromDateTime(DateTime.Today.AddDays(30)),
                    QtyPerUnitOfMeasure = 1// Default for gift items
                };

                giftLine.UpdateAmount();
                var giftLineInsert = _mapper.Map<POLineAddOrUpdate>(giftLine);
                giftLineInsert.Desc = giftLine.Description;

                var addGiftLineResponse = await _apiClient.AddLine(giftLineInsert);

                if (addGiftLineResponse.IsSuccessStatusCode)
                {
                    // Add to local collection for immediate UI update
                    poLines?.Add(giftLine);
                }
                else
                {
                    return false;
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error applying Different Item Gift: {ex.Message}");
            return false;
        }
    }

    public static bool ApplyPercentageDiscountToPOLine(POLineGetDto poLine, GetPromotionFrontMarginDto promotion)
    {
        try
        {
            // Calculate discount percentage (base + tier bonus if applicable)
            var totalDiscountPercentage = promotion.DiscountPercentage;

            // Check if tier bonus applies
            if (promotion.TierQuantityThreshold > 0 &&
                promotion.TierBonusPercentage > 0 &&
                poLine.Quantity >= promotion.TierQuantityThreshold)
            {
                totalDiscountPercentage += promotion.TierBonusPercentage.Value;
            }

            // Store discount information in Line fields (Case 1.1)
            poLine.LineDiscountPercent = totalDiscountPercentage;

            // Calculate discount amount
            var originalAmount = poLine.Quantity * poLine.UnitCost;
            var discountAmount = originalAmount * (totalDiscountPercentage / 100);
            poLine.LineDiscountAmount = discountAmount;

            // Recalculate amounts
            poLine.UpdateAmount();

            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error applying percentage discount: {ex.Message}");
            return false;
        }
    }

    public static bool ApplyFixedAmountDiscountToPOLine(POLineGetDto poLine, GetPromotionFrontMarginDto promotion)
    {
        try
        {
            // Calculate base fixed discount with GiftCalculationType consideration
            decimal totalFixedDiscount;

            switch (promotion.GiftCalculationType)
            {
                case 1:// Progressive (Lũy kế) - base discount nhân theo quantity
                    var baseQuantity = promotion.MinimumQuantity > 0 ? promotion.MinimumQuantity : 1;
                    var quantityRatio = poLine.Quantity / baseQuantity;
                    totalFixedDiscount = promotion.FixedDiscountAmount * quantityRatio;
                    break;

                default:// Milestone (Mốc) - base discount cố định
                    totalFixedDiscount = promotion.FixedDiscountAmount;
                    break;
            }

            // Check if tier bonus applies
            if (promotion.TierQuantityThreshold > 0 &&
                promotion.TierBonusAmount > 0 &&
                poLine.Quantity >= promotion.TierQuantityThreshold)
            {
                switch (promotion.GiftCalculationType)
                {
                    case 1:// Progressive (Lũy kế) - nhân tier bonus với số tier sets
                        var tierSets = (int)((poLine.Quantity - promotion.TierQuantityThreshold.Value) /
                                             promotion.TierQuantityThreshold.Value) + 1;
                        totalFixedDiscount += promotion.TierBonusAmount.Value * tierSets;
                        break;

                    default:// Default to Milestone (Mốc) for safety
                        totalFixedDiscount += promotion.TierBonusAmount.Value;
                        break;

                }
            }

            // Calculate discount percentage from fixed amount
            var originalAmount = poLine.Quantity * poLine.UnitCost;
            var discountPercentage = originalAmount > 0 ? totalFixedDiscount / originalAmount * 100 : 0;

            // Store discount information in Line fields (Case 1.2)
            poLine.LineDiscountAmount = Math.Min(totalFixedDiscount, originalAmount);
            poLine.LineDiscountPercent = discountPercentage;

            // Recalculate amounts
            poLine.UpdateAmount();

            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error applying fixed amount discount: {ex.Message}");
            return false;
        }
    }
}
