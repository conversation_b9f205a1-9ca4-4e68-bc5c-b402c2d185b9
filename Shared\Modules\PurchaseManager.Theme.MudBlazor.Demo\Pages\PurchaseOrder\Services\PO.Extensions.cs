﻿using System.Data;
using PurchaseManager.Constants.Enum;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Dto.PurchasePrices;
using PurchaseManager.Shared.Models.PurchasePrice;
namespace PurchaseManager.Theme.Material.Demo.Pages.PurchaseOrder.Services;

public static class POExtensions
{
    public static void UpdateAmount(this POLineGetDto row)
    {
        var vat = row.Vat / 100;// 0.08
        var discount = row.LineDiscountPercent;// 8

        var amountBeforeVat = row.UnitCost * row.Quantity;// 3 * 2  = 6
        var discountAmountBeforeVAT = discount / 100 * amountBeforeVat;

        if (discount == 0 && row.LineDiscountAmount != 0)
        {
            discountAmountBeforeVAT = row.LineDiscountAmount;
        }
        // cal discount

        // Số tiền trước VAT - Discount
        var amountBeforeVATDiscount = amountBeforeVat - discountAmountBeforeVAT;

        var vatAmount = amountBeforeVATDiscount * vat;

        // Số tiền sau thuế đã trừ discount
        var amountAfterVat = amountBeforeVATDiscount + vatAmount;

        row.Amount = amountAfterVat;// Use amount after VAT and discount
        row.VatAmount = vatAmount;
        row.LineDiscountAmount = discountAmountBeforeVAT;
        row.VatBaseAmount = amountBeforeVat - discountAmountBeforeVAT;
        row.AmountIncludingVat = amountAfterVat;
    }

    public static bool IsPromotionEligibleDynamic(PromotionFrontMarginSelected fm, List<POLineGetDto> poLines)
    {
        // Find the corresponding PO line for this promotion's item with DocumentType = 0 only
        var poLine = poLines?.FirstOrDefault(line =>
            string.Equals(line.ItemNumber, fm.ItemNumber, StringComparison.OrdinalIgnoreCase) &&
            line.DocumentType == 0);

        if (poLine == null)
        {
            return false;// No PO line found for this item with DocumentType = 0
        }

        var discountType = GetDiscountTypeFromFrontMargin(fm);
        var lineAmount = poLine.Quantity * poLine.UnitCost;

        // Check conditions based on discount type
        switch (discountType)
        {
            case (int)FrontMarginDiscountTypeTypeEnum.PercentageDiscount
                :// Percentage discount - condition 1.1: reach MinimumQuantity OR MinimumAmount
                if (fm.MinimumQuantity > 0 || fm.MinimumAmount > 0)
                {
                    var quantityMet = fm.MinimumQuantity > 0 && poLine.Quantity >= fm.MinimumQuantity;
                    var amountMet = fm.MinimumAmount > 0 && lineAmount >= fm.MinimumAmount;

                    // Need to meet at least one condition (OR logic)
                    if (!quantityMet && !amountMet)
                    {
                        return false;
                    }
                }

                break;

            case (int)FrontMarginDiscountTypeTypeEnum.FixedAmountDiscount:// Fixed amount discount - condition 1.2: only MinimumAmount
                if (fm.MinimumAmount > 0 && lineAmount < fm.MinimumAmount)
                {
                    return false;
                }

                break;

            case (int)FrontMarginDiscountTypeTypeEnum.SameItemGift:// Same item gift - condition 2: BuyQuantity and GiftQuantity
                if (fm.BuyQuantity > 0 && poLine.Quantity < fm.BuyQuantity)
                {
                    return false;
                }

                // Check unit of measure compatibility for same item gifts
                if (!string.IsNullOrEmpty(fm.UnitOfMeasure))
                {
                    if (!string.Equals(poLine.UnitOfMeasure, fm.UnitOfMeasure, StringComparison.OrdinalIgnoreCase))
                    {
                        return false;
                    }
                }
                break;

            case (int)FrontMarginDiscountTypeTypeEnum.DifferentItemGift
                :// Different item gift - condition 3: BuyQuantity and GiftQuantity
                if (fm.BuyQuantity > 0 && poLine.Quantity < fm.BuyQuantity)
                {
                    return false;
                }
                break;
        }

        return true;// All conditions met
    }

    public static bool IsTierBonusEligibleStatic(PromotionFrontMarginSelected fm, List<POLineGetDto> poLines)
    {
        // Find the corresponding PO line for this promotion's item with DocumentType = 0 only
        var poLine = poLines?.FirstOrDefault(line =>
            string.Equals(line.ItemNumber, fm.ItemNumber, StringComparison.OrdinalIgnoreCase) &&
            line.DocumentType == 0);

        if (poLine == null || fm.TierQuantityThreshold <= 0)
        {
            return false;
        }

        var discountType = GetDiscountTypeFromFrontMargin(fm);

        // Check if tier bonus is available and quantity threshold is met
        return discountType switch
        {
            (int)FrontMarginDiscountTypeTypeEnum.PercentageDiscount => fm.TierBonusPercentage > 0 &&
                                                                       poLine.Quantity >= fm.TierQuantityThreshold,
            (int)FrontMarginDiscountTypeTypeEnum.FixedAmountDiscount => fm.TierBonusAmount > 0 &&
                                                                        poLine.Quantity >= fm.TierQuantityThreshold,
            _ => false
        };
    }

    public static int GetDiscountTypeFromFrontMargin(PromotionFrontMarginSelected fm)
    {
        // Determine discount type based on available data - ưu tiên discount chính trước
        if (fm.DiscountPercentage > 0)
            return 1;// Percentage discount
        if (fm.FixedDiscountAmount > 0)
            return 2;// Fixed amount discount
        if (fm.GiftQuantity > 0 && string.IsNullOrEmpty(fm.GiftItemNumber))
            return 3;// Same item gift
        return !string.IsNullOrEmpty(fm.GiftItemNumber) ? 4 :// Different item gift
            1;// Default to percentage

    }

    public static List<DuplicatePurchasePriceItemDto> GetDuplicateDetails(this List<CreatePurchasePriceDto> dataList)
    {
        var validateDtos = dataList
            .GroupBy(x => new
            {
                x.VendorNumber,
                x.ItemNumber
            })
            .Where(g => g.Count() > 1)
            .Select(g => new DuplicatePurchasePriceItemDto
            {
                VendorNumber = g.Key.VendorNumber,
                ItemNumber = g.Key.ItemNumber,
                DuplicateCount = g.Count(),
                DuplicatedItems = g.ToList()
            }).ToList();
        return validateDtos;
    }
    public static int GetIntValue(DataRow row, int index)
    {
        return row.ItemArray.Length > index && int.TryParse(row.ItemArray[index]?.ToString(), out var value) ? value : 0;
    }

    public static string GetStringValue(DataRow row, int index)
    {
        return row.ItemArray.Length > index && row.ItemArray[index] != null
            ? row.ItemArray[index].ToString()?.Trim() : string.Empty;
    }
    public static decimal GetDecimalValue(DataRow row, int index)
    {
        return row.ItemArray.Length > index && decimal.TryParse(row.ItemArray[index]?.ToString(), out var value) ? value : 0;
    }

}
