﻿using System.ComponentModel.DataAnnotations.Schema;
using PurchaseManager.Shared.Dto.Item;
namespace PurchaseManager.Shared.Dto.PO;

public class POLineGetDto
{
    public int DocumentType { get; set; }
    public string DocumentNumber { get; set; }
    public int LineNumber { get; set; }
    public int Type { get; set; }
    public string ItemNumber { get; set; }
    public string LocationCode { get; set; }
    public string PostingGroup { get; set; }
    public DateTime ExpectedReceiptDate { get; set; }
    public string Description { get; set; }
    public string Description2 { get; set; }
    public string UnitOfMeasure { get; set; }
    /// <summary>
    ///     Số lượng đặt hàng
    /// </summary>
    public decimal Quantity { get; set; } = 1;
    public decimal OutstandingQuantity { get; set; }
    public decimal QuantityToInvoice { get; set; }
    /// <summary>
    ///     Số lượng hàng còn lại
    /// </summary>
    public decimal QuantityToReceive { get; set; }
    public decimal Vat { get; set; }// % vat
    /// <summary>
    ///     Chiếc khấu trước thuế theo SKus
    /// </summary>
    public decimal LineDiscountPercent { get; set; }
    /// <summary>
    ///     Thành tiền trước thuế theo SKus
    /// </summary>
    public decimal LineDiscountAmount { get; set; }
    /// <summary>
    ///     Số tiền = Đơn giá * Số lượng
    /// </summary>
    public decimal Amount { get; set; }
    /// <summary>
    /// Thành tiền cuối cùng, = Tiền VAT + Tiền sau TK
    /// </summary>
    public decimal AmountIncludingVat { get; set; }
    public int AllowInvoiceDiscount { get; set; }
    /// <summary>
    ///     Đã nhận hàng
    /// </summary>
    public decimal QuantityReceived { get; set; }
    public decimal QuantityInvoiced { get; set; }
    public string ReceiptNumber { get; set; }
    public int ReceiptLineNumber { get; set; }
    public decimal ProfitPercent { get; set; }
    public decimal VatAmount { get; set; }
    /// <summary>
    /// giá mua
    /// </summary>
    public decimal UnitCost { get; set; }
    public int Status { get; set; }
    public decimal LineAmount { get; set; }
    /// <summary>
    /// thành tiền sau chiếc kấu: (- CK) = Amount - Line discount amount
    /// </summary>
    public decimal VatBaseAmount { get; set; }
    public string UnitOfMeasureCode { get; set; }
    public string CrossReferenceNumber { get; set; }
    public string LotNo { get; set; } = string.Empty;
    /// <summary>
    /// Hạn dùng
    /// </summary>
    public DateOnly? ExpirationDate { get; set; }
    public string CategoryCode { get; set; } = string.Empty;
    public string ItemName { get; set; } = string.Empty;
    public decimal? UnitPrice { get; set; }
    public decimal QtyPerUnitOfMeasure { get; set; }
    /// <summary>
    /// Giá mua gần nhất trên ERP
    /// </summary>
    public decimal LastUnitCost { get; set; }
    [NotMapped]
    public DetailItemDto Item { get; set; } = new DetailItemDto();
    [NotMapped]
    public int TempReceive { get; set; }
    [NotMapped]
    public bool IsArrowDisabled { get; set; }
    [NotMapped]
    public string ConversionRateDisplay { get; set; } = string.Empty;
    public int RowId { get; set; }

    // Front Margin Integration Properties (computed from PromotionFrontMarginUsage table)
    [NotMapped]
    public decimal OriginalUnitCost { get; set; }
    [NotMapped]
    public decimal FrontMarginDiscountPercent { get; set; }
    [NotMapped]
    public decimal FrontMarginDiscountAmount { get; set; }
    [NotMapped]
    public bool CanEditDiscount { get; set; } = true;
    [NotMapped]
    public PromotionUsageInfo? PromotionUsageInfo { get; set; }
}
