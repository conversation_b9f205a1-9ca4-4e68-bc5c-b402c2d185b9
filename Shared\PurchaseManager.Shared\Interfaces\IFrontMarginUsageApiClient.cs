using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Filters;

namespace PurchaseManager.Shared.Interfaces;

/// <summary>
/// API Client interface for Front Margin Usage operations
/// </summary>
public interface IFrontMarginUsageApiClient
{
    /// <summary>
    /// Create Draft Front Margin usage when creating PO Line
    /// </summary>
    Task<ApiResponseDto> CreatePromotionFrontMarginUsage(CreatePromotionFrontMarginUsageDto dto);
    
    /// <summary>
    /// Cancel usage record
    /// </summary>
    Task<ApiResponseDto> CancelUsage(string number, string reason);
    
    /// <summary>
    ///  Get PO promotion tracking usage by poNumber
    /// </summary>
    Task<ApiResponseDto<List<AppliedPromotionMappingDto>>> GetPoPromotionTrackingUsageAsync(string poNumber);

}
