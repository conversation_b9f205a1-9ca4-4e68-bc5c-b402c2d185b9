using System.Net.Http.Json;
using Microsoft.Extensions.Logging;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Promotions;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models.Promotions;
using Microsoft.AspNetCore.Components.Forms;
using System.Net.Http.Headers;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
namespace PurchaseManager.Shared.Services;

public class PromotionApiClient : BaseApiClient, IPromotionApiClient
{
    private const string DefaultRootApiPath = "api/Promotion";
    public PromotionApiClient(HttpClient httpClient, ILogger<BaseApiClient> logger)
        : base(httpClient, logger, DefaultRootApiPath)
    {
    }

    #region Promotion Header
    public async Task<ApiResponseDto<PagedResult<GetPromotionHeaderDto>>> GetPromotionsAsync(PromotionFilter filter)
    {
        var query = filter.ToQuery();
        var responseApi =
            await httpClient.GetFromJsonAsync<ApiResponseDto<PagedResult<GetPromotionHeaderDto>>>(
            $"{DefaultRootApiPath}/filtered?" + query);
        return responseApi;
    }
    public async Task<ApiResponseDto<GetPromotionHeaderDto>> GetPromotionHeaderByNumberAsync(string number)
    {
        return await httpClient.GetFromJsonAsync<ApiResponseDto<GetPromotionHeaderDto>>($"{DefaultRootApiPath}/{number}");
    }
    public async Task<ApiResponseDto<CreatePromotionHeaderDto>> CreatePromotionHeaderAsync(CreatePromotionHeaderDto createPromotionDto)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto<CreatePromotionHeaderDto>>($"{DefaultRootApiPath}", createPromotionDto);
    }
    public async Task<ApiResponseDto<int>> OpenDocumentAsync(string documentNumber)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto<int>>(
        $"{DefaultRootApiPath}/open-document/{documentNumber}", null);
    }
    public async Task<ApiResponseDto<int>> CloseDocumentAsync(string documentNumber)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto<int>>(
        $"{DefaultRootApiPath}/close-document/{documentNumber}", null);
    }
    public async Task<ApiResponseDto<int>> UpdatePromotionHeaderAsync(string number, UpdatePromotionHeaderDto updateDto)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto<int>>($"{DefaultRootApiPath}/{number}", updateDto);
    }
    
    public async Task<ApiResponseDto> DeletePromotionHeaderAsync(List<string> numbers)
    {
        return await httpClient.SendJsonAsync<ApiResponseDto>(HttpMethod.Delete, $"{DefaultRootApiPath}/bulk", numbers);
    }
    #endregion

    #region Promotion Front Margin
    public async Task<ApiResponseDto<int>> UpdatePromotionFrontMarginAsync(string number, UpdatePromotionFrontMarginDto updateDto)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto<int>>($"{DefaultRootApiPath}/front-margin/{number}", updateDto);
    }
    public async Task<ApiResponseDto> CreatePromotionFrontMarginAsync(CreatePromotionFrontMarginDto createDto)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>($"{DefaultRootApiPath}/front-margin", createDto);
    }

    public async Task<ApiResponseDto> DeletePromotionFrontMarginAsync(List<string> numbers)
    {
        return await httpClient.SendJsonAsync<ApiResponseDto>(HttpMethod.Delete, $"{DefaultRootApiPath}/front-margin", numbers);
    }

    public async Task<ApiResponseDto?> ImportPromotionFrontMarginAsync(string promotionNumber, IBrowserFile file)
    {
        using var content = new MultipartFormDataContent();

        var streamContent = new StreamContent(file.OpenReadStream(maxAllowedSize: 10 * 1024 * 1024));// 10MB
        streamContent.Headers.ContentType = new MediaTypeHeaderValue(file.ContentType);
        content.Add(streamContent, "file", file.Name);

        // Gọi giống CreatePromotionFrontMarginAsync
        return await httpClient.PostMultipartAsync<ApiResponseDto>(
        $"{DefaultRootApiPath}/front-margin/{promotionNumber}/import",
        content,
        new JsonSerializerSettings
        {
            ContractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            },
            MissingMemberHandling = MissingMemberHandling.Ignore,
            NullValueHandling = NullValueHandling.Ignore
        });
    }
    
    #endregion
}
