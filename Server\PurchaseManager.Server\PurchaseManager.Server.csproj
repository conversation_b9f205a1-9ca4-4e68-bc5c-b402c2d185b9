<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Title>PurchaseManager Server</Title>
    <PublishWithAspNetCoreTargetManifest>false</PublishWithAspNetCoreTargetManifest>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>dccf98cd-91ea-4e92-b852-ad056fc2b6bf</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="logs\**"/>
    <Content Remove="logs\**"/>
    <EmbeddedResource Remove="logs\**"/>
    <None Remove="logs\**"/>
    <Content Remove="wwwroot\files\FILES000047_Table of right_FA_1.pdf"/>
  </ItemGroup>

    <ItemGroup>
        <PackageReference Include="AspectInjector" Version="2.8.2" />
        <PackageReference Include="AspNet.Security.OAuth.Apple" Version="8.0.0" />
        <PackageReference Include="Azure.Extensions.AspNetCore.DataProtection.Blobs" Version="1.3.4" />
        <PackageReference Include="Azure.Extensions.AspNetCore.DataProtection.Keys" Version="1.2.3" />
        <PackageReference Include="Azure.Identity" Version="1.11.4" />
        <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.6.0" />
        <PackageReference Include="Azure.Security.KeyVault.Certificates" Version="4.6.0" />
        <PackageReference Include="Breeze.AspNetCore.NetCore" Version="7.3.0" />
        <PackageReference Include="Breeze.Persistence.EFCore" Version="7.3.0"/>
        <PackageReference Include="ClosedXML" Version="0.102.3" />
        <PackageReference Include="Dapper" Version="2.1.35" />
      <PackageReference Include="EFCore.BulkExtensions" Version="8.1.2"/>
        <PackageReference Include="EntityFramework" Version="6.5.1" />
        <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
        <PackageReference Include="FormatWith" Version="3.0.1" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.Facebook" Version="8.0.6" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.Google" Version="8.0.6" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.6" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.MicrosoftAccount" Version="8.0.6" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.Twitter" Version="8.0.6" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.6" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.6">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.6">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="8.0.0" />
        <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.2" />
        <PackageReference Include="NSwag.AspNetCore" Version="14.0.3" />
      <PackageReference Include="Quartz" Version="3.14.0"/>
      <PackageReference Include="Quartz.Extensions.Hosting" Version="3.14.0"/>
      <PackageReference Include="Quartz.Plugins.RecentHistory" Version="1.0.3"/>
        <PackageReference Include="QuestPDF" Version="2024.6.2" />
        <PackageReference Include="Serilog" Version="4.0.0" />
        <PackageReference Include="Serilog.AspNetCore" Version="8.0.1" />
        <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.1" />
        <PackageReference Include="Serilog.Sinks.File" Version="5.0.1-dev-00947" />
        <PackageReference Include="MailKit" Version="4.6.0" />
        <PackageReference Include="Serilog.Sinks.MSSqlServer" Version="6.6.1" />
        <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="8.0.6" />
      <PackageReference Include="SilkierQuartz" Version="9.0.391"/>
      <PackageReference Include="SilkierQuartz.Plugins.RecentHistory" Version="9.0.391"/>
      <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.3.0"/>
      <PackageReference Include="System.Linq.Async" Version="6.0.1"/>
      <PackageReference Include="System.Linq.Dynamic.Core" Version="1.6.2"/>
        <PackageReference Include="System.Net.Http" Version="4.3.4" />
        <PackageReference Include="System.Net.Http.Json" Version="8.0.0" />
        <PackageReference Include="System.Security.Cryptography.Xml" Version="8.0.1" />
        <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
    </ItemGroup>

  <ItemGroup Condition="'$(OS)' == 'Windows_NT'">
    <PackageReference Include="Microsoft.ICU.ICU4C.Runtime" Version="[********]"/>
    <RuntimeHostConfigurationOption Include="System.Globalization.AppLocalIcu" Value="68.2"/>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Shared\PurchaseManager.Infrastructure\PurchaseManager.Infrastructure.csproj"/>
    <ProjectReference Include="..\..\Shared\PurchaseManager.UI.Base\PurchaseManager.UI.Base.csproj"/>
    <ProjectReference Include="..\..\Shared\Modules\PurchaseManager.Theme.MudBlazor.Admin\PurchaseManager.Theme.MudBlazor.Admin.csproj"/>
    <ProjectReference Include="..\..\Shared\Modules\PurchaseManager.Theme.MudBlazor.Demo\PurchaseManager.Theme.MudBlazor.Demo.csproj"/>
    <ProjectReference Include="..\..\Shared\Modules\PurchaseManager.Theme.MudBlazor\PurchaseManager.Theme.MudBlazor.csproj"/>
    <ProjectReference Include="..\PurchaseManager.Storage\PurchaseManager.Storage.csproj"/>
    <!--        <ProjectReference Include="..\..\Client\PurchaseManager.Client\PurchaseManager.Client.csproj" />-->
  </ItemGroup>

  <ItemGroup>
    <None Update="Controllers\AdminController.tt">
      <LastGenOutput>AdminController.cs</LastGenOutput>
      <Generator>TextTemplatingFileGenerator</Generator>
    </None>
    <None Update="Modules\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Themes\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Localization\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}"/>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Controllers\AdminController.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>AdminController.tt</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="obj\Debug\net8.0\"/>
    <Folder Include="Themes\"/>
    <Folder Include="wwwroot\images\QRcode\PO\"/>
    <Folder Include="wwwroot\Report\PO\"/>
  </ItemGroup>

  <Target Name="PostBuild" AfterTargets="PostBuildEvent" Condition="'$(BuildingInsideVisualStudio)' == 'true'">
    <Exec Command="if exist &quot;$(ProjectDir)Logs\*.*&quot; del &quot;$(ProjectDir)Logs\*.*&quot; /q"/>
  </Target>

</Project>
