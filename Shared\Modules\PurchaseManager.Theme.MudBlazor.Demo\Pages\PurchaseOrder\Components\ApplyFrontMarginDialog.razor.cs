using Microsoft.AspNetCore.Components;
using MudBlazor;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Interfaces;
using System.Globalization;
using PurchaseManager.Constants.Enum;
using static PurchaseManager.Theme.Material.Demo.Pages.PurchaseOrder.Services.POExtensions;
namespace PurchaseManager.Theme.Material.Demo.Pages.PurchaseOrder.Components;

public partial class ApplyFrontMarginDialog : ComponentBase
{
    [CascadingParameter]
    private MudDialogInstance MudDialog { get; set; } = null!;
    [Inject]
    private ISnackbar Snackbar { get; set; } = null!;
    [Parameter]
    public string VendorCode { get; set; } = string.Empty;
    [Parameter]
    public string ItemNumber { get; set; } = string.Empty;
    [Parameter]
    public List<POLineGetDto> POLines { get; set; } = [];
    [Parameter]
    public string PONumber { get; set; } = string.Empty;
    [Inject]
    private IFrontMarginApiClient FrontMarginApiClient { get; set; }

    [Inject]
    private IFrontMarginUsageApiClient FrontMarginUsageApiClient { get; set; }

    private List<PromotionSelectedDto> _promotionList = [];
    private Dictionary<string, List<GetPromotionFrontMarginDto>> _promotionsByProgram =
        new Dictionary<string, List<GetPromotionFrontMarginDto>>();
    private Dictionary<string, string>
        SelectedItems
    {
        get;
        set;
    } = new Dictionary<string, string>();// Key: "ProgramName_DiscountType", Value: ItemNumber
    private bool IsLoading { get; set; }
    private List<AppliedPromotionMappingDto> _existingUsageRecords = [];

    // Cached table data for better performance
    private List<PromotionTableItemDto> _tableData = [];

    // For MudTable with grouping and selection
    private readonly HashSet<PromotionTableItemDto> _selectedTableItems = [];
    private readonly TableGroupDefinition<PromotionTableItemDto> _groupDefinition = new TableGroupDefinition<PromotionTableItemDto>
    {
        GroupName = "Program",
        Indentation = false,
        Expandable = true,
        IsInitiallyExpanded = true,
        Selector = (e) => e.ProgramName// Use ProgramName property from the anonymous object
    };

    protected override async Task OnInitializedAsync()
    {
        await LoadExistingUsageRecords();
        await LoadPromotionHistory();

        // Note: Pre-checking for promotions is now done in the parent component
        // before opening this dialog, so we don't need to close it here
    }

    private async Task LoadExistingUsageRecords()
    {
        try
        {
            if (string.IsNullOrEmpty(PONumber))
                return;

            var response = await FrontMarginUsageApiClient.GetPoPromotionTrackingUsageAsync(PONumber);

            if (response.IsSuccessStatusCode && response.Result != null)
            {
                _existingUsageRecords = response.Result;
            }
            else
            {
                _existingUsageRecords = [];
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Lỗi khi tải dữ liệu usage records: {ex.Message}", Severity.Warning);
            _existingUsageRecords = [];
        }
    }

    private Task AutoSelectExistingPromotions(List<GetPromotionFrontMarginDto> flatPromotions)
    {
        try
        {
            Console.WriteLine(
            $"AutoSelectExistingPromotions: {_existingUsageRecords.Count} usage records, {flatPromotions.Count} promotions");

            var autoSelectedCount = 0;
            var skippedDueToUnitMismatchCount = 0;

            foreach (var usageRecord in _existingUsageRecords)
            {
                Console.WriteLine($"Looking for promotion with PromotionNumber: {usageRecord.PromotionNumber}");

                // Find matching promotion in the loaded list using PromotionNumber (exact match)
                var matchingPromotion = flatPromotions.FirstOrDefault(p =>
                    p.Number == usageRecord.PromotionNumber);

                if (matchingPromotion != null)
                {
                    Console.WriteLine($"Found matching promotion: {matchingPromotion.Number} - {matchingPromotion.ProgramName}");

                    // Find the exact table item from _tableData (for proper MudTable selection)
                    var tableItem = _tableData.FirstOrDefault(item =>
                        item.PromotionNumber == matchingPromotion.Number);

                    if (tableItem != null)
                    {
                        // Check if the promotion is still compatible with current PO unit of measure
                        if (IsUnitOfMeasureCompatible(tableItem))
                        {
                            // Auto-select this promotion only if unit of measure is still compatible
                            var key = $"{matchingPromotion.ProgramName}_{matchingPromotion.DiscountType}";
                            SelectedItems[key] = matchingPromotion.ItemNumber;

                            _selectedTableItems.Add(tableItem);
                            autoSelectedCount++;
                            Console.WriteLine(
                            $"Auto-selected compatible promotion: {tableItem.PromotionNumber} - {tableItem.ProgramName}");
                        }
                        else
                        {
                            skippedDueToUnitMismatchCount++;
                            Console.WriteLine(
                            $"Skipping promotion {matchingPromotion.Number} - unit of measure no longer compatible. PO UOM: {GetPOLineUnitOfMeasure(matchingPromotion.ItemNumber)}, Promotion UOM: {matchingPromotion.UnitOfMeasure}");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"Table item not found for promotion: {matchingPromotion.Number}");
                        Console.WriteLine($"Available table items: {string.Join(", ", _tableData.Select(t => t.PromotionNumber))}");
                    }
                }
                else
                {
                    // Log for debugging if no exact match found
                    Console.WriteLine($"No exact match found for PromotionNumber: {usageRecord.PromotionNumber}");
                    Console.WriteLine($"Available promotion numbers: {string.Join(", ", flatPromotions.Select(p => p.Number))}");
                }
            }

            Console.WriteLine($"Total selected table items: {_selectedTableItems.Count}");

            // Show notification if some promotions were skipped due to unit mismatch
            if (skippedDueToUnitMismatchCount > 0)
            {
                Snackbar.Add($"Đã hủy áp dụng {skippedDueToUnitMismatchCount} khuyến mãi do đơn vị tính không còn tương thích",
                Severity.Warning);
            }

            if (autoSelectedCount > 0)
            {
                Snackbar.Add($"Đã tự động chọn {autoSelectedCount} khuyến mãi tương thích", Severity.Success);
            }

            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in AutoSelectExistingPromotions: {ex}");
            Snackbar.Add($"Lỗi khi auto-select promotions: {ex.Message}", Severity.Warning);
        }

        return Task.CompletedTask;
    }

    private void BuildTableData()
    {
        if (_promotionsByProgram?.Any() == true)
        {
            _tableData = _promotionsByProgram.SelectMany(group =>
                group.Value.Select(promo =>
                {
                    // Create a PromotionFrontMarginSelected for eligibility check
                    var frontMargin = CreateFrontMarginFromPromotion(promo);

                    return new PromotionTableItemDto
                    {
                        ProgramName = group.Key,
                        PromotionNumber = promo.Number,
                        PromotionName = promo.ItemName,
                        DiscountType = promo.DiscountType,
                        MinOrderValue = promo.MinimumAmount,
                        TierQuantityThreshold = promo.TierQuantityThreshold,
                        TierBonusPercentage = promo.TierBonusPercentage,
                        TierBonusAmount = promo.TierBonusAmount,
                        DiscountPercentage = promo.DiscountPercentage,
                        FixedDiscountAmount = promo.FixedDiscountAmount,
                        DiscountValue = promo.DiscountType switch
                        {
                            1 => promo.DiscountPercentage > 0 ? $"{promo.DiscountPercentage:F2}%" : "0%",
                            2 => promo.FixedDiscountAmount > 0 ? $"{promo.FixedDiscountAmount:N0} VNĐ" : "0 VNĐ",
                            3 => $"Mua {promo.BuyQuantity:F0} tặng {promo.GiftQuantity:F0}",
                            4 => $"Tặng {promo.GiftItemQuantity:F0} {promo.GiftItemUOM ?? ""} {promo.GiftItemName ?? ""}",
                            _ => "-"
                        },
                        BonusInfo = promo.TierQuantityThreshold > 0
                            ? $"Từ {promo.TierQuantityThreshold}: " +
                              (promo.TierBonusPercentage > 0
                                  ? promo.TierBonusPercentage?.ToString("0.##") + "%"
                                  : promo.TierBonusAmount?.ToString("C", new CultureInfo("vi-VN")) ?? "-")
                            : "-",
                        IsSelected = _selectedTableItems.Any(s => s.PromotionNumber == promo.Number),
                        // Additional properties
                        ProgramNumber = promo.ProgramNumber,
                        ItemNumber = promo.ItemNumber,
                        ItemName = promo.ItemName,
                        UnitOfMeasure = promo.UnitOfMeasure,
                        BuyQuantity = promo.BuyQuantity,
                        GiftQuantity = promo.GiftQuantity,
                        GiftItemNumber = promo.GiftItemNumber,
                        GiftItemName = promo.GiftItemName,
                        MinimumQuantity = promo.MinimumQuantity,
                        MinimumAmount = promo.MinimumAmount,
                        GiftCalculationType = promo.GiftCalculationType,// Default to Progressive if not set
                        // Calculate eligibility properly
                        IsEligible = IsPromotionEligibleDynamic(frontMargin, POLines),
                        Note = GetPromotionNoteDynamic(frontMargin)
                    };
                })
            ).ToList();
        }
        else
        {
            _tableData = [];
        }
    }

    private PromotionFrontMarginSelected CreateFrontMarginFromPromotion(GetPromotionFrontMarginDto promo)
    {
        return new PromotionFrontMarginSelected
        {
            Number = promo.Number,
            ItemNumber = promo.ItemNumber,
            ItemName = promo.ItemName,
            DiscountType = promo.DiscountType,
            DiscountPercentage = promo.DiscountPercentage,
            FixedDiscountAmount = promo.FixedDiscountAmount,
            BuyQuantity = promo.BuyQuantity,
            GiftQuantity = promo.GiftQuantity,
            GiftItemNumber = promo.GiftItemNumber,
            GiftItemName = promo.GiftItemName,
            GiftItemUOM = promo.UnitOfMeasure,
            GiftItemQuantity = promo.GiftItemQuantity,
            MinimumQuantity = promo.MinimumQuantity,
            MinimumAmount = promo.MinimumAmount,
            TierQuantityThreshold = promo.TierQuantityThreshold ?? 0,
            TierBonusPercentage = promo.TierBonusPercentage ?? 0,
            TierBonusAmount = promo.TierBonusAmount ?? 0,
            UnitOfMeasure = promo.UnitOfMeasure
        };
    }

    private async Task RefreshTableData()
    {
        await LoadPromotionHistory();
        StateHasChanged();
    }

    private async Task LoadPromotionHistory()
    {
        try
        {
            IsLoading = true;
            StateHasChanged();

            // Get item numbers from POLines with DocumentType = 0 only
            var itemNumbers = POLines?
                .Where(line => line.DocumentType == 0)
                .Select(line => line.ItemNumber)
                .Distinct()
                .Where(item => !string.IsNullOrEmpty(item))
                .ToList() ?? [];

            if (itemNumbers.Count == 0)
            {
                Snackbar.Add("Không có mã hàng nào với DocumentType = 0 để tìm khuyến mãi", Severity.Warning);
                _promotionList = [];
                _promotionsByProgram = new Dictionary<string, List<GetPromotionFrontMarginDto>>();
                return;
            }

            // Create request for new API
            var request = new PromotionSelectedRequestDto
            {
                VendorNumber = VendorCode,
                ItemNumbers = itemNumbers
            };

            var response = await FrontMarginApiClient.GetPromotionsByVendorItemsAsync(request);

            if (response.IsSuccessStatusCode && response.Result != null)
            {
                _promotionList = response.Result;

                // Convert PromotionSelectedDto to GetPromotionFrontMarginDto for UI compatibility
                var flatPromotions = new List<GetPromotionFrontMarginDto>();

                foreach (var program in _promotionList)
                {
                    foreach (var frontMargin in program.FrontMargins)
                    {
                        var promotionDto = new GetPromotionFrontMarginDto
                        {
                            Number = frontMargin.Number,
                            ProgramName = program.PromotionName,
                            ProgramNumber = program.PromotionNumber,
                            ItemNumber = frontMargin.ItemNumber,
                            ItemName = frontMargin.ItemName,
                            BuyQuantity = frontMargin.BuyQuantity,
                            GiftQuantity = frontMargin.GiftQuantity,
                            GiftItemNumber = frontMargin.GiftItemNumber,
                            GiftItemName = frontMargin.GiftItemName,
                            GiftItemUOM = frontMargin.GiftItemUOM,
                            GiftItemQuantity = frontMargin.GiftItemQuantity,
                            GiftCalculationType = frontMargin.GiftCalculationType,
                            MinimumQuantity = frontMargin.MinimumQuantity,
                            MinimumAmount = frontMargin.MinimumAmount,
                            TierQuantityThreshold = frontMargin.TierQuantityThreshold,
                            TierBonusPercentage = frontMargin.TierBonusPercentage,
                            TierBonusAmount = frontMargin.TierBonusAmount,
                            // Determine discount type and values - Sử dụng giá trị từ API
                            DiscountType = frontMargin.DiscountType,
                            DiscountPercentage = frontMargin.DiscountPercentage,// Giá trị discount chính từ API
                            FixedDiscountAmount = frontMargin.FixedDiscountAmount,// Giá trị discount chính từ API
                            UnitOfMeasure = frontMargin.UnitOfMeasure,// Use the main item's unit of measure, not gift item
                            Status = (int)PromotionFrontMarginUsageEnum.Active,// Active
                            StartDate = DateTime.Now.AddDays(-30),// Default values for UI
                            EndDate = DateTime.Now.AddDays(30)
                        };

                        flatPromotions.Add(promotionDto);
                    }
                }

                // Group by program name for UI
                _promotionsByProgram = flatPromotions
                    .GroupBy(p => p.ProgramName)
                    .ToDictionary(keySelector: g => g.Key, elementSelector: g => g.ToList());

                // Build table data for better performance
                BuildTableData();

                // Auto-select promotions that exist in usage records
                await AutoSelectExistingPromotions(flatPromotions);
            }
            else
            {
                Snackbar.Add("Không thể tải danh sách khuyến mãi", Severity.Error);
                _promotionList = [];
                _promotionsByProgram = new Dictionary<string, List<GetPromotionFrontMarginDto>>();
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Lỗi khi tải dữ liệu: {ex.Message}", Severity.Error);
            _promotionList = [];
            _promotionsByProgram = new Dictionary<string, List<GetPromotionFrontMarginDto>>();
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }

    private void CloseDialog()
    {
        MudDialog.Close();
    }

    /// <summary>
    /// Static method to check if there are any promotions available before opening the dialog
    /// </summary>
    public static async Task<bool> HasPromotionsAsync(IFrontMarginApiClient frontMarginApiClient, string vendorCode,
        List<POLineGetDto> poLines)
    {
        try
        {
            var itemNumbers = poLines?
                .Where(line => line.DocumentType == 0)
                .Select(line => line.ItemNumber)
                .Distinct()
                .Where(item => !string.IsNullOrEmpty(item))
                .ToList() ?? [];

            if (itemNumbers.Count == 0)
                return false;

            var request = new PromotionSelectedRequestDto
            {
                VendorNumber = vendorCode,
                ItemNumbers = itemNumbers
            };

            var response = await frontMarginApiClient.GetPromotionsByVendorItemsAsync(request);
            return response.IsSuccessStatusCode && response.Result?.Count > 0;
        }
        catch
        {
            return false;
        }
    }

    public List<GetPromotionFrontMarginDto> GetSelectedPromotions()
    {
        var selectedPromotions = new List<GetPromotionFrontMarginDto>();

        foreach (var (_, itemNumber) in SelectedItems)
        {
            // Find the promotion in the flattened list from _promotionsByProgram
            var promotion = _promotionsByProgram.Values
                .SelectMany(list => list)
                .FirstOrDefault(p => p.Number == itemNumber);

            if (promotion != null)
            {
                selectedPromotions.Add(promotion);
            }
        }

        return selectedPromotions;
    }

    private void ApplySelections()
    {
        // Use table selection if available, otherwise fallback to radio selection
        var selectedPromotions = _selectedTableItems.Count != 0
            ? GetSelectedPromotionsFromTable()
            : GetSelectedPromotions();

        // Just return the selected promotions, let PoLinesPage handle the discount application
        MudDialog.Close(DialogResult.Ok(selectedPromotions));
    }

    private string GetItemNumbersDisplay()
    {
        // Get item numbers from POLines with DocumentType = 0 only
        var documentType0Items = POLines?
            .Where(line => line.DocumentType == 0)
            .Select(line => line.ItemNumber)
            .Distinct()
            .ToList() ?? [];

        return documentType0Items.Count switch
        {
            0 => "Không có items với DocumentType = 0",
            <= 3 => string.Join(", ", documentType0Items),
            _ => $"{string.Join(", ", documentType0Items.Take(3))} và {documentType0Items.Count - 3} mã khác"
        };

    }

    public List<GetPromotionFrontMarginDto> GetSelectedPromotionsFromTable()
    {
        var selectedPromotions = new List<GetPromotionFrontMarginDto>();

        foreach (var item in _selectedTableItems)
        {
            // Convert PromotionTableItemDto back to GetPromotionFrontMarginDto
            var promotion = new GetPromotionFrontMarginDto
            {
                Number = item.PromotionNumber,
                ProgramName = item.ProgramName,
                ProgramNumber = item.ProgramNumber,
                ItemNumber = item.ItemNumber,
                ItemName = item.ItemName,
                BuyQuantity = item.BuyQuantity,
                GiftCalculationType = item.GiftCalculationType,
                GiftQuantity = item.GiftQuantity,
                GiftItemNumber = item.GiftItemNumber,
                GiftItemName = item.GiftItemName,
                GiftItemUOM = item.UnitOfMeasure,
                GiftItemQuantity = 0,
                MinimumQuantity = item.MinimumQuantity,
                MinimumAmount = item.MinimumAmount,
                TierQuantityThreshold = item.TierQuantityThreshold,
                TierBonusPercentage = item.TierBonusPercentage,
                TierBonusAmount = item.TierBonusAmount,
                DiscountType = item.DiscountType,
                DiscountPercentage = item.DiscountPercentage,// Sử dụng discount chính
                FixedDiscountAmount = item.FixedDiscountAmount,// Sử dụng discount chính
                UnitOfMeasure = item.UnitOfMeasure,
                Status = 2,
                StartDate = DateTime.Now.AddDays(-30),
                EndDate = DateTime.Now.AddDays(30)
            };
            selectedPromotions.Add(promotion);
        }

        return selectedPromotions;
    }

    private string GetPromotionNoteDynamic(PromotionFrontMarginSelected fm)
    {
        if (IsPromotionEligibleDynamic(fm, POLines))
        {
            // Check if tier bonus is available
            var currentPoLine = POLines?.FirstOrDefault(line =>
                string.Equals(line.ItemNumber, fm.ItemNumber, StringComparison.OrdinalIgnoreCase));

            if (currentPoLine == null || fm.TierQuantityThreshold <= 0) return string.Empty;
            var currentDiscountType = GetDiscountTypeFromFrontMargin(fm);
            var hasTierBonus = currentDiscountType == 1 && fm.TierBonusPercentage > 0 ||
                               currentDiscountType == 2 && fm.TierBonusAmount > 0;

            if (!hasTierBonus || currentPoLine.Quantity < fm.TierQuantityThreshold) return string.Empty;
            var tierBonusText = currentDiscountType == 1
                ? $"+{fm.TierBonusPercentage:F1}% tier bonus"
                : $"+{fm.TierBonusAmount:N0} VNĐ tier bonus";
            return $"✓ Đủ điều kiện ({tierBonusText})";
        }

        var notes = new List<string>();

        // Find the corresponding PO line for this promotion's item
        var poLine = POLines?.FirstOrDefault(line =>
            string.Equals(line.ItemNumber, fm.ItemNumber, StringComparison.OrdinalIgnoreCase));

        if (poLine == null)
        {
            return "Không tìm thấy sản phẩm trong PO";
        }

        var discountType = GetDiscountTypeFromFrontMargin(fm);
        var lineAmount = poLine.Quantity * poLine.UnitCost;

        // Check conditions based on discount type
        switch (discountType)
        {
            case 1:// Percentage discount - condition 1.1: reach MinimumQuantity OR MinimumAmount
                if (fm.MinimumQuantity > 0 || fm.MinimumAmount > 0)
                {
                    var quantityMet = fm.MinimumQuantity > 0 && poLine.Quantity >= fm.MinimumQuantity;
                    var amountMet = fm.MinimumAmount > 0 && lineAmount >= fm.MinimumAmount;

                    if (!quantityMet && !amountMet)
                    {
                        var conditions = new List<string>();
                        if (fm.MinimumQuantity > 0)
                            conditions.Add($"≥{fm.MinimumQuantity:N0} SP");
                        if (fm.MinimumAmount > 0)
                            conditions.Add($"≥{fm.MinimumAmount:N0} VNĐ");

                        notes.Add(
                        $"Cần đạt: {string.Join(" HOẶC ", conditions)} (hiện tại: {poLine.Quantity:N0} SP, {lineAmount:N0} VNĐ)");
                    }
                }
                break;

            case 2:// Fixed amount discount - condition 1.2: only MinimumAmount
                if (fm.MinimumAmount > 0 && lineAmount < fm.MinimumAmount)
                {
                    notes.Add($"Đơn hàng tối thiểu {fm.MinimumAmount:N0} VNĐ (hiện tại: {lineAmount:N0} VNĐ)");
                }
                break;

            case 3:// Same item gift - condition 2: BuyQuantity and GiftQuantity
                if (fm.BuyQuantity > 0 && poLine.Quantity < fm.BuyQuantity)
                {
                    notes.Add($"Cần mua {fm.BuyQuantity:N0} để được tặng {fm.GiftQuantity:N0} (hiện tại: {poLine.Quantity:N0})");
                }

                // Check unit of measure compatibility
                if (!string.IsNullOrEmpty(fm.UnitOfMeasure))
                {
                    if (!string.Equals(poLine.UnitOfMeasure, fm.UnitOfMeasure, StringComparison.OrdinalIgnoreCase))
                    {
                        notes.Add($"Đơn vị tính không khớp (yêu cầu: {fm.UnitOfMeasure}, hiện tại: {poLine.UnitOfMeasure})");
                    }
                }
                break;

            case 4:// Different item gift - condition 3: BuyQuantity and GiftQuantity
                if (fm.BuyQuantity > 0 && poLine.Quantity < fm.BuyQuantity)
                {
                    notes.Add($"Cần mua {fm.BuyQuantity:N0} để được tặng SP khác (hiện tại: {poLine.Quantity:N0})");
                }
                break;
        }

        return notes.Count > 0 ? string.Join(", ", notes) : "Chưa đủ điều kiện";
    }

    private static string GetColumnValue2Dynamic(PromotionTableItemDto item)
    {
        return item.DiscountType switch
        {
            1 => GetPercentageDiscountConditions(item),// For percentage discount - show conditions + tier bonus
            2 => GetFixedDiscountConditionWithTierBonus(item),// For fixed amount discount - show conditions + tier bonus
            3 => GetSameItemGiftDisplay(item),// For same item gift - show "Mua x tặng y"
            4 => !string.IsNullOrEmpty(item.GiftItemName) ? item.GiftItemName :
                !string.IsNullOrEmpty(item.GiftItemNumber) ? item.GiftItemNumber : "-",
            _ => "-"
        };
    }

    private static string GetPercentageDiscountConditions(PromotionTableItemDto item)
    {
        // Only show bonus value for percentage discount
        if (item.TierQuantityThreshold > 0 && item.TierBonusPercentage > 0)
        {
            return $"+{item.TierBonusPercentage:F1}%";
        }
        return "-";
    }

    private static string GetFixedDiscountConditionWithTierBonus(PromotionTableItemDto item)
    {
        // Only show bonus value for fixed amount discount
        if (item.TierQuantityThreshold > 0 && item.TierBonusAmount > 0)
        {
            return $"+{item.TierBonusAmount:N0} VNĐ";
        }
        return "-";
    }

    private static string GetSameItemGiftDisplay(PromotionTableItemDto item)
    {
        // For same item gift (discount type 3) - show "Mua x tặng y"
        if (item.BuyQuantity > 0 && item.GiftQuantity > 0)
        {
            return $"Mua {item.BuyQuantity:N0} tặng {item.GiftQuantity:N0}";
        }
        return "-";
    }

    public static string GetBonusValueWithIcon(PromotionTableItemDto item)
    {
        var bonusValue = GetColumnValue2Dynamic(item);
        return bonusValue == "-" ? "-" :
            bonusValue;
    }

    public static string GetBonusTooltip(PromotionTableItemDto item, int discountType)
    {
        var conditions = new List<string>();

        switch (discountType)
        {
            case 1 when item.TierBonusPercentage > 0 && item.TierQuantityThreshold > 0:
                conditions.Add($"Bonus +{item.TierBonusPercentage:F1}% khi đạt ≥{item.TierQuantityThreshold:N0} SP");
                break;
            case 2 when item.TierBonusAmount > 0 && item.TierQuantityThreshold > 0:
                conditions.Add($"Bonus +{item.TierBonusAmount:N0} VNĐ khi đạt ≥{item.TierQuantityThreshold:N0} SP");
                break;
            case 3 when item.BuyQuantity > 0 && item.GiftQuantity > 0:
                conditions.Add($"Mua {item.BuyQuantity:N0} sản phẩm sẽ được tặng {item.GiftQuantity:N0} sản phẩm cùng loại");
                break;
            case 4 when item.BuyQuantity > 0:
                var giftInfo = !string.IsNullOrEmpty(item.GiftItemName) ? item.GiftItemName :
                    !string.IsNullOrEmpty(item.GiftItemNumber) ? item.GiftItemNumber : "sản phẩm khác";
                conditions.Add($"Mua {item.BuyQuantity:N0} sản phẩm sẽ được tặng {giftInfo}");
                break;
        }

        return conditions.Count > 0 ? string.Join("\n", conditions) : "Không có điều kiện bonus";
    }

    public bool IsTierBonusEligible(PromotionTableItemDto item)
    {
        // Create PromotionFrontMarginSelected from table item
        var frontMargin = new PromotionFrontMarginSelected
        {
            ItemNumber = item.ItemNumber,
            TierQuantityThreshold = item.TierQuantityThreshold ?? 0,
            TierBonusPercentage = item.TierBonusPercentage ?? 0,
            TierBonusAmount = item.TierBonusAmount ?? 0
        };

        // Use the extension method from PO.Extensions
        return IsTierBonusEligibleStatic(frontMargin, POLines);
    }

    public string GetBonusColor(PromotionTableItemDto item)
    {
        // If no bonus available, show normal color
        if (GetColumnValue2Dynamic(item) == "-")
        {
            return "color: #999;";
        }

        // If row should be muted (not eligible or unit mismatch), show muted color
        if (IsRowMuted(item))
        {
            return "color: #ccc; opacity: 0.5;";
        }

        // Return appropriate color based on tier bonus eligibility
        return "color: inherit;";
    }

    /// <summary>
    /// Check if bonus column should be muted (when unit of measure doesn't match)
    /// </summary>
    public bool IsBonusColumnMuted(PromotionTableItemDto item)
    {
        return !IsUnitOfMeasureCompatible(item);
    }

    /// <summary>
    /// Check if entire row should be muted (when not eligible or unit of measure doesn't match)
    /// </summary>
    public bool IsRowMuted(PromotionTableItemDto item)
    {
        return !item.IsEligible || !IsUnitOfMeasureCompatible(item);
    }

    /// <summary>
    /// Get row style for muted/enabled state
    /// </summary>
    public string GetRowStyle(PromotionTableItemDto item)
    {
        return IsRowMuted(item) ? "color: #999;" : "color: inherit;";
    }

    /// <summary>
    /// Get discount group for a discount type to prevent conflicts
    /// Group 1: Discount types (1-Percentage, 2-FixedAmount)
    /// Group 2: Same item gift (3)
    /// Group 3: Different item gift (4)
    /// </summary>
    private static int GetDiscountGroup(int discountType)
    {
        return discountType switch
        {
            1 or 2 => 1,// Discount group (percentage and fixed amount)
            3 => 2,// Same item gift group
            4 => 3,// Different item gift group
            _ => 0// Unknown
        };
    }

    /// <summary>
    /// Check if two discount types are in the same group (conflicting)
    /// </summary>
    private bool AreDiscountTypesConflicting(int discountType1, int discountType2)
    {
        var group1 = GetDiscountGroup(discountType1);
        var group2 = GetDiscountGroup(discountType2);
        return group1 == group2 && group1 != 0;
    }

    /// <summary>
    /// Check if the unit of measure in PO line matches with promotion's unit of measure
    /// </summary>
    private bool IsUnitOfMeasureCompatible(PromotionTableItemDto item)
    {
        var poLine = POLines?.FirstOrDefault(line =>
            string.Equals(line.ItemNumber, item.ItemNumber, StringComparison.OrdinalIgnoreCase));

        if (poLine == null) return false;

        // If PO line doesn't have unit of measure, return false
        if (string.IsNullOrEmpty(poLine.UnitOfMeasure))
            return false;

        // If promotion doesn't specify unit of measure, we need to check if this is intentional
        // Some promotions might not require specific unit checking
        if (string.IsNullOrEmpty(item.UnitOfMeasure))
        {
            // For discount types 1 & 2 (percentage and fixed amount), unit checking might be optional
            // For gift types 3 & 4, unit checking should be more flexible
            // Allow gift types to proceed if UnitOfMeasure is not specified in promotion
            return item.DiscountType is 1 or 2 or 3 or 4;
        }

        // Check if units match exactly (case insensitive)
        return string.Equals(poLine.UnitOfMeasure.Trim(), item.UnitOfMeasure.Trim(), StringComparison.OrdinalIgnoreCase);
    }
    /// <summary>
    /// Get unit of measure from PO line for a specific item
    /// </summary>
    private string GetPOLineUnitOfMeasure(string itemNumber)
    {
        var poLine = POLines?.FirstOrDefault(line =>
            string.Equals(line.ItemNumber, itemNumber, StringComparison.OrdinalIgnoreCase));

        return poLine?.UnitOfMeasure ?? "N/A";
    }

    /// <summary>
    /// Check if promotion can be selected (eligible and unit of measure compatible)
    /// </summary>
    private bool CanSelectPromotion(PromotionTableItemDto item)
    {
        return item.IsEligible && IsUnitOfMeasureCompatible(item);
    }

    /// <summary>
    /// Get reason why promotion cannot be selected
    /// </summary>
    private string GetPromotionBlockReason(PromotionTableItemDto item)
    {
        if (!item.IsEligible)
        {
            return item.Note;
        }

        if (!IsUnitOfMeasureCompatible(item))
        {
            var poUOM = GetPOLineUnitOfMeasure(item.ItemNumber);
            var promotionUOM = item.UnitOfMeasure ?? "Không có";

            if (string.IsNullOrEmpty(poUOM) || poUOM == "N/A")
            {
                return "Không tìm thấy đơn vị tính trong PO";
            }

            if (string.IsNullOrEmpty(item.UnitOfMeasure))
            {
                // For gift types, unit of measure is mandatory
                if (item.DiscountType is 3 or 4)
                {
                    return "Promotion quà tặng không có đơn vị tính";
                }
                // This shouldn't happen due to logic in IsUnitOfMeasureCompatible
                return "Promotion không có đơn vị tính";
            }

            return $"Đơn vị tính không khớp: PO ({poUOM}) ≠ KM ({promotionUOM})";
        }

        return "Không thể chọn";
    }

    private Task OnTableItemSelected(PromotionTableItemDto item, bool isSelected)
    {
        if (isSelected)
        {
            // Check unit of measure compatibility first
            if (!IsUnitOfMeasureCompatible(item))
            {
                var poUOM = GetPOLineUnitOfMeasure(item.ItemNumber);
                var promotionUOM = item.UnitOfMeasure ?? "Không có";

                string message;
                if (string.IsNullOrEmpty(item.UnitOfMeasure) && item.DiscountType is 3 or 4)
                {
                    message = "Không thể chọn: Promotion quà tặng không có đơn vị tính";
                }
                else if (string.IsNullOrEmpty(item.UnitOfMeasure))
                {
                    message = "Không thể chọn: Promotion không có đơn vị tính";
                }
                else
                {
                    message = $"Không thể chọn: Đơn vị tính không khớp. PO ({poUOM}) ≠ Promotion ({promotionUOM})";
                }

                Snackbar.Add(message, Severity.Warning);
                return Task.CompletedTask;
            }

            // Check for conflicts with already selected items for the same item number
            var conflictingItems = _selectedTableItems
                .Where(selected =>
                    selected.ItemNumber == item.ItemNumber &&
                    AreDiscountTypesConflicting(selected.DiscountType, item.DiscountType))
                .ToList();

            // Remove conflicting items
            foreach (var conflictingItem in conflictingItems)
            {
                _selectedTableItems.Remove(conflictingItem);
            }

            // Add the new item
            _selectedTableItems.Add(item);
        }
        else
        {
            _selectedTableItems.Remove(item);
        }

        StateHasChanged();
        return Task.CompletedTask;
    }// Helper methods for gift promotion table
    private static string GetGiftItemNumber(PromotionTableItemDto item)
    {
        return item.DiscountType switch
        {
            3 => item.ItemNumber,// Same item gift - show same item number
            4 => item.GiftItemNumber ?? "-",// Different item gift - show gift item number
            _ => "-"
        };
    }

    private static string GetGiftItemName(PromotionTableItemDto item)
    {
        return item.DiscountType switch
        {
            3 => item.ItemName,// Same item gift - show same item name
            4 => item.GiftItemName ?? "-",// Different item gift - show gift item name
            _ => "-"
        };
    }

    private static string GetGiftItemUOM(PromotionTableItemDto item)
    {
        return item.DiscountType switch
        {
            3 => item.UnitOfMeasure,// Same item gift - use same UOM
            4 => item.GiftItemUOM ?? "-",// Different item gift - use gift item UOM
            _ => "-"
        };
    }

    private static string GetCalculatedGiftQuantity(PromotionTableItemDto item)
    {
        if (item.GiftQuantity <= 0) return "-";

        // Calculate based on buy quantity conditions
        var baseGiftQty = item.GiftQuantity;

        // If there are tier conditions, show the calculation
        if (item.BuyQuantity > 0)
        {
            return item.GiftCalculationType == 2
                ? $"Tặng {baseGiftQty:F0}"
                : $"{baseGiftQty:F0} (mỗi {item.BuyQuantity:F0}) Tổng: {baseGiftQty * (item.BuyQuantity > 0 ? 1 : 0):F0}";

        }

        return $"{baseGiftQty:F0}";
    }

    private static string GetGiftConditionText(PromotionTableItemDto item)
    {
        var conditions = new List<string>();

        // Add buy quantity condition
        if (item.BuyQuantity > 0)
        {
            conditions.Add($"Mua ≥ {item.BuyQuantity:F0}");
        }

        // Add minimum quantity condition
        if (item.MinimumQuantity > 0)
        {
            conditions.Add($"SL tối thiểu: {item.MinimumQuantity:F0}");
        }

        // Add minimum amount condition
        if (item.MinimumAmount > 0)
        {
            conditions.Add($"GT tối thiểu: {item.MinimumAmount:C0}");
        }

        // Add tier bonus conditions
        if (item.TierQuantityThreshold is not > 0)
            return conditions.Count > 0 ? string.Join("; ", conditions) : "Không có điều kiện đặc biệt";
        var tierBonus = "";
        if (item.TierBonusPercentage.HasValue)
        {
            tierBonus = $"{item.TierBonusPercentage:F1}%";
        }
        else if (item.TierBonusAmount.HasValue)
        {
            tierBonus = $"{item.TierBonusAmount:C0}";
        }

        if (!string.IsNullOrEmpty(tierBonus))
        {
            conditions.Add($"Bonus khi ≥ {item.TierQuantityThreshold:F0}: +{tierBonus}");
        }

        return conditions.Count > 0 ? string.Join("; ", conditions) : "Không có điều kiện đặc biệt";
    }

    private static string GetBonusDisplayText(PromotionTableItemDto item)
    {
        // Handle percentage bonus
        if (item.TierBonusPercentage is > 0)
        {
            return item.TierQuantityThreshold is > 0
                ? $"Mua > {(int)item.TierQuantityThreshold.Value} + {item.TierBonusPercentage.Value:F1}%"
                : $"+{item.TierBonusPercentage.Value:F1}%";
        }

        // Handle amount bonus
        if (item.TierBonusAmount is > 0)
        {
            return item.TierQuantityThreshold is > 0
                ? $"Mua > {(int)item.TierQuantityThreshold.Value} + {item.TierBonusAmount.Value:N0} VND"
                : $"+{item.TierBonusAmount.Value:N0} VND";
        }

        return "-";
    }

}
