using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;
namespace PurchaseManager.Infrastructure.Storage.DataModels;

/// <summary>
/// Back Margin earned entity - stores Back Margin rewards that have been calculated and are ready for payment
/// This is the "CTKM đã đạt điều kiện chờ trả thưởng" table mentioned in requirements
/// </summary>
[Table("PromotionBackMarginEarneds")]
public class PromotionBackMarginEarned : FullTrackingEntity
{
    /// <summary>
    /// Reference to PromotionBackMargin program
    /// </summary>
    [Required]
    [StringLength(50)]
    public string BackMarginNumber { get; set; } = string.Empty;

    /// <summary>
    /// Vendor code
    /// </summary>
    [Required]
    [StringLength(20)]
    public string VendorCode { get; set; } = string.Empty;

    /// <summary>
    /// Item number (null for program-level rewards)
    /// </summary>
    [StringLength(50)]
    public string? ItemNumber { get; set; }

    /// <summary>
    /// Calculation period start date
    /// </summary>
    [Required]
    public DateTime PeriodStartDate { get; set; }

    /// <summary>
    /// Calculation period end date
    /// </summary>
    [Required]
    public DateTime PeriodEndDate { get; set; }

    /// <summary>
    /// Total quantity achieved in the period
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal TotalQuantity { get; set; }

    /// <summary>
    /// Total amount achieved in the period
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// Tier level achieved (which tier was qualified)
    /// </summary>
    public int? TierLevelAchieved { get; set; }

    /// <summary>
    /// Calculated Back Margin amount
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,2)")]
    public decimal EarnedAmount { get; set; }

    /// <summary>
    /// Discount percentage applied
    /// </summary>
    [Column(TypeName = "decimal(5,2)")]
    public decimal? DiscountPercentage { get; set; }

    /// <summary>
    /// Calculation formula used
    /// </summary>
    [StringLength(500)]
    public string? CalculationFormula { get; set; }

    /// <summary>
    /// Calculation details (JSON or text)
    /// </summary>
    [StringLength(2000)]
    public string? CalculationDetails { get; set; }

    /// <summary>
    /// Date when Back Margin was calculated
    /// </summary>
    [Required]
    public DateTime CalculatedDate { get; set; }

    /// <summary>
    /// User who calculated the Back Margin
    /// </summary>
    [StringLength(100)]
    public string? CalculatedBy { get; set; }

    /// <summary>
    /// Status:
    /// 1 = Calculated (đã tính, chờ trả)
    /// 2 = Approved (đã duyệt, sẵn sàng trả)
    /// 3 = Paid (đã trả)
    /// 4 = Cancelled (hủy)
    /// </summary>
    [Required]
    public int Status { get; set; } = 1;

    /// <summary>
    /// Payment method (from BackMargin.PaymentMethod)
    /// 1 = Cash, 2 = Transfer, 3 = DebtOffset, 4 = Goods
    /// </summary>
    [Required]
    public int PaymentMethod { get; set; }

    /// <summary>
    /// Payment timing (from BackMargin.PaymentTiming)
    /// 1 = NextOrder, 2 = AfterPayment, 3 = Plus30Days, etc.
    /// </summary>
    [Required]
    public int PaymentTiming { get; set; }

    /// <summary>
    /// Expected payment date (calculated based on PaymentTiming)
    /// </summary>
    public DateTime? ExpectedPaymentDate { get; set; }

    /// <summary>
    /// Notes
    /// </summary>
    [StringLength(500)]
    public string? Notes { get; set; }

    // Navigation Properties
    [ForeignKey(nameof(BackMarginNumber))]
    public virtual PromotionBackMargin BackMargin { get; set; } = null!;

    [ForeignKey(nameof(VendorCode))]
    public virtual Vendor Vendor { get; set; } = null!;

    /// <summary>
    /// Payment records for this earned Back Margin
    /// </summary>
    public virtual ICollection<PromotionBackMarginPayment> Payments { get; set; } = new List<PromotionBackMarginPayment>();
}
