using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.MarginDto.FrontMargin.Integrations;
using PurchaseManager.Shared.Localizer;
using static Microsoft.AspNetCore.Http.StatusCodes;

namespace PurchaseManager.Server.Controllers;

/// <summary>
///     Controller for PO integration with Front Margin system
///     Provides endpoints for seamless integration between PO and Front Margin
/// </summary>
[ApiController]
[Route("api/po-integration")]
public class POIntegrationController : ControllerBase
{
    private readonly IPOFrontMarginIntegrationManager _integrationManager;
    private readonly ApiResponse _invalidData;

    public POIntegrationController(
        IPOFrontMarginIntegrationManager integrationManager,
        IStringLocalizer<Global> i18N)
    {
        _integrationManager = integrationManager;
        _invalidData = new ApiResponse(Status400BadRequest, i18N["InvalidData"]);
    }

    /// <summary>
    ///     Apply Front Margin to PO during creation or modification
    /// </summary>
    [HttpPost("apply-front-margin")]
    public async Task<ApiResponse> ApplyFrontMarginToPO([FromBody] POFrontMarginRequest request)
    {
        if (!ModelState.IsValid)
        {
            return _invalidData;
        }

        return await _integrationManager.ApplyFrontMarginToPOAsync(request.POHeader, request.POLines);
    }

    /// <summary>
    ///     Preview Front Margin impact before applying to PO
    /// </summary>
    [HttpPost("preview-front-margin")]
    public async Task<ApiResponse> PreviewFrontMarginImpact([FromBody] POFrontMarginRequest request)
    {
        if (!ModelState.IsValid)
        {
            return _invalidData;
        }

        return await _integrationManager.PreviewFrontMarginImpactAsync(request.POHeader, request.POLines);
    }

    /// <summary>
    ///     Validate PO for Front Margin eligibility
    /// </summary>
    [HttpPost("validate-front-margin")]
    public async Task<ApiResponse> ValidatePOForFrontMargin([FromBody] POFrontMarginRequest request)
    {
        if (!ModelState.IsValid)
        {
            return _invalidData;
        }

        return await _integrationManager.ValidatePOForFrontMarginAsync(request.POHeader, request.POLines);
    }

    /// <summary>
    ///     Remove Front Margin from existing PO
    /// </summary>
    [HttpDelete("remove-front-margin/{poNumber}")]
    public async Task<ApiResponse> RemoveFrontMarginFromPO(string poNumber)
    {
        if (string.IsNullOrEmpty(poNumber))
        {
            return ApiResponse.S400("PO Number is required");
        }

        return await _integrationManager.RemoveFrontMarginFromPOAsync(poNumber);
    }

    /// <summary>
    ///     Get Front Margin summary for specific PO
    /// </summary>
    [HttpGet("front-margin-summary/{poNumber}")]
    public async Task<ApiResponse> GetPOFrontMarginSummary(string poNumber)
    {
        if (string.IsNullOrEmpty(poNumber))
        {
            return ApiResponse.S400("PO Number is required");
        }

        return await _integrationManager.GetPOFrontMarginSummaryAsync(poNumber);
    }

    /// <summary>
    ///     Recalculate Front Margin for existing PO
    /// </summary>
    [HttpPost("recalculate-front-margin/{poNumber}")]
    public async Task<ApiResponse> RecalculateFrontMargin(string poNumber)
    {
        if (string.IsNullOrEmpty(poNumber))
        {
            return ApiResponse.S400("PO Number is required");
        }

        return await _integrationManager.RecalculateFrontMarginAsync(poNumber);
    }

    /// <summary>
    ///     Get Front Margin usage statistics for vendor
    /// </summary>
    [HttpGet("vendor-stats/{vendorCode}")]
    public async Task<ApiResponse> GetVendorFrontMarginStats(string vendorCode, [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        if (string.IsNullOrEmpty(vendorCode))
        {
            return ApiResponse.S400("Vendor Code is required");
        }

        return await _integrationManager.GetVendorFrontMarginStatsAsync(vendorCode, fromDate, toDate);
    }

    /// <summary>
    ///     Auto-apply Front Margin to PO based on vendor settings
    /// </summary>
    [HttpPost("auto-apply-front-margin")]
    public async Task<ApiResponse> AutoApplyFrontMargin([FromBody] POFrontMarginRequest request)
    {
        if (!ModelState.IsValid)
        {
            return _invalidData;
        }

        return await _integrationManager.AutoApplyFrontMarginAsync(request.POHeader, request.POLines);
    }

    /// <summary>
    ///     Get integration dashboard data
    /// </summary>
    [HttpGet("dashboard")]
    public async Task<ApiResponse> GetIntegrationDashboard()
    {
        try
        {
            // This would aggregate integration statistics
            var dashboard = new
            {
                Summary = new
                {
                    TotalPOsWithFrontMargin = 0, TotalSavingsThisMonth = 0m, AverageDiscountPercentage = 0m, ActivePromotions = 0
                },
                RecentActivity = new List<object>(),
                TopVendorsByUsage = new List<object>(),
                MonthlyTrends = new List<object>()
            };

            return ApiResponse.S200("Integration dashboard data retrieved", dashboard);
        }
        catch (Exception ex)
        {
            return ApiResponse.S500($"Error getting dashboard data: {ex.Message}");
        }
    }

    /// <summary>
    ///     Get integration health status
    /// </summary>
    [HttpGet("health")]
    public async Task<ApiResponse> GetIntegrationHealth()
    {
        try
        {
            var health = new
            {
                Status = "Healthy",
                LastChecked = DateTime.UtcNow,
                Services = new
                {
                    FrontMarginService = "Online", CalculationEngine = "Online", CacheService = "Online", Database = "Online"
                },
                Metrics = new
                {
                    AverageResponseTime = "45ms", CacheHitRatio = "87%", ErrorRate = "0.1%"
                }
            };

            return ApiResponse.S200("Integration health status", health);
        }
        catch (Exception ex)
        {
            return ApiResponse.S500($"Error checking health: {ex.Message}");
        }
    }

    /// <summary>
    ///     Bulk apply Front Margin to multiple POs
    /// </summary>
    [HttpPost("bulk-apply-front-margin")]
    public async Task<ApiResponse> BulkApplyFrontMargin([FromBody] List<POFrontMarginRequest> requests)
    {
        if (!ModelState.IsValid || requests.Count == 0)
        {
            return ApiResponse.S400("Invalid request data or empty request list");
        }

        try
        {
            var results = new List<object>();
            var successCount = 0;
            var errorCount = 0;

            foreach (var request in requests)
            {
                var result = await _integrationManager.ApplyFrontMarginToPOAsync(request.POHeader, request.POLines);

                if (result.IsSuccessStatusCode)
                {
                    successCount++;
                    results.Add(new
                    {
                        PONumber = request.POHeader.Number, Status = "Success", Data = result.Result
                    });
                }
                else
                {
                    errorCount++;
                    results.Add(new
                    {
                        PONumber = request.POHeader.Number, Status = "Error", result.Message
                    });
                }
            }

            return ApiResponse.S200($"Bulk apply completed: {successCount} success, {errorCount} errors", new
            {
                TotalRequests = requests.Count, SuccessCount = successCount, ErrorCount = errorCount, Results = results
            });
        }
        catch (Exception ex)
        {
            return ApiResponse.S500($"Error in bulk apply: {ex.Message}");
        }
    }

    /// <summary>
    ///     Get Front Margin integration settings for vendor
    /// </summary>
    [HttpGet("settings/{vendorCode}")]
    public async Task<ApiResponse> GetVendorIntegrationSettings(string vendorCode)
    {
        if (string.IsNullOrEmpty(vendorCode))
        {
            return ApiResponse.S400("Vendor Code is required");
        }

        try
        {
            // This would get vendor-specific integration settings
            var settings = new VendorIntegrationSettings
            {
                VendorCode = vendorCode,
                AutoApplyEnabled = true,
                RequireApproval = false,
                MaxDiscountPercentage = 50m,
                AllowGiftItems = true,
                NotificationSettings = new NotificationSettings
                {
                    EmailOnApply = false, EmailOnLargeDiscount = true, LargeDiscountThreshold = 20m
                }
            };

            return ApiResponse.S200($"Integration settings for vendor {vendorCode}", settings);
        }
        catch (Exception ex)
        {
            return ApiResponse.S500($"Error getting settings: {ex.Message}");
        }
    }

    /// <summary>
    ///     Update Front Margin integration settings for vendor
    /// </summary>
    [HttpPut("settings/{vendorCode}")]
    public async Task<ApiResponse> UpdateVendorIntegrationSettings(string vendorCode, [FromBody] VendorIntegrationSettings settings)
    {
        if (string.IsNullOrEmpty(vendorCode) || !ModelState.IsValid)
        {
            return _invalidData;
        }

        try
        {
            // This would update vendor-specific integration settings
            return ApiResponse.S200($"Integration settings updated for vendor {vendorCode}", settings);
        }
        catch (Exception ex)
        {
            return ApiResponse.S500($"Error updating settings: {ex.Message}");
        }
    }
}
