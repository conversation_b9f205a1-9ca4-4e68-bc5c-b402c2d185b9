using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PurchaseManager.Infrastructure.Storage.DataModels;

/// <summary>
/// Back Margin promotion configuration entity
/// Supports 5 discount types: Sales Progressive, Sales Tiered, Quantity Progressive, Quantity Tiered, Early Payment
/// </summary>
[Table("PromotionBackMargins")]
public class PromotionBackMargin
{
    [Key]
    [StringLength(50)]
    public string Number { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string ProgramNumber { get; set; } = string.Empty;

    [Required]
    public int LineNumber { get; set; }

    [StringLength(50)]
    public string? ItemNumber { get; set; }

    [StringLength(250)]
    public string? ItemName { get; set; }

    [StringLength(50)]
    public string? UnitOfMeasure { get; set; }

    /// <summary>
    /// Discount Type: 1=Sales Progressive, 2=Sales Tiered, 3=Qty Progressive, 4=Qty Tiered, 5=Early Payment
    /// </summary>
    [Required]
    public int DiscountType { get; set; }

    /// <summary>
    /// Calculation Period: MONTHLY, QUARTERLY, YEARLY
    /// </summary>
    [Required]
    [StringLength(20)]
    public string CalculationPeriod { get; set; } = string.Empty;

    public DateTime? PeriodStartDate { get; set; }

    public DateTime? PeriodEndDate { get; set; }

    /// <summary>
    /// Standard payment days (for Early Payment Discount)
    /// </summary>
    public int? StandardPaymentDays { get; set; }

    /// <summary>
    /// Early payment days (for Early Payment Discount)
    /// </summary>
    public int? EarlyPaymentDays { get; set; }

    /// <summary>
    /// Early payment discount percentage (for Early Payment Discount)
    /// </summary>
    [Column(TypeName = "decimal(5,2)")]
    public decimal? EarlyPaymentDiscountPercentage { get; set; }

    /// <summary>
    /// Supported payment methods as JSON array (for Early Payment Discount)
    /// </summary>
    [StringLength(500)]
    public string? SupportedPaymentMethods { get; set; }

    /// <summary>
    /// Status: 1=Draft, 2=Active, 3=Inactive, 4=Expired
    /// </summary>
    [Required]
    public int Status { get; set; } = 1;

    public string? Notes { get; set; }

    [StringLength(100)]
    public string? CreatedBy { get; set; }

    public DateTime? CreatedAt { get; set; }

    [StringLength(100)]
    public string? LastModifiedBy { get; set; }

    public DateTime? LastModifiedAt { get; set; }

    /// <summary>
    /// Modification Status: 1=Active, 2=Deleted
    /// </summary>
    [Required]
    public int ModificationStatus { get; set; } = 1;

    // Navigation Properties
    [ForeignKey(nameof(ProgramNumber))]
    public virtual PromotionHeader PromotionHeader { get; set; } = null!;

    public virtual ICollection<PromotionBackMarginTier> Tiers { get; set; } = new List<PromotionBackMarginTier>();

    public virtual ICollection<PromotionBackMarginCalculation> Calculations { get; set; } = new List<PromotionBackMarginCalculation>();
}

/// <summary>
/// Back Margin tier configuration entity
/// Defines discount tiers for progressive and tiered discount types
/// </summary>
[Table("PromotionBackMarginTiers")]
public class PromotionBackMarginTier
{
    [Key]
    public long Id { get; set; }

    [Required]
    [StringLength(50)]
    public string BackMarginNumber { get; set; } = string.Empty;

    [Required]
    public int TierLevel { get; set; }

    /// <summary>
    /// Minimum amount threshold (for sales-based discounts)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? MinimumAmount { get; set; }

    /// <summary>
    /// Maximum amount threshold (for sales-based discounts)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? MaximumAmount { get; set; }

    /// <summary>
    /// Minimum quantity threshold (for quantity-based discounts)
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? MinimumQuantity { get; set; }

    /// <summary>
    /// Maximum quantity threshold (for quantity-based discounts)
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? MaximumQuantity { get; set; }

    /// <summary>
    /// Discount percentage for this tier
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(5,2)")]
    public decimal DiscountPercentage { get; set; }

    /// <summary>
    /// Fixed discount amount (alternative to percentage)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? FixedDiscountAmount { get; set; }

    /// <summary>
    /// Maximum discount amount limit
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? MaximumDiscountAmount { get; set; }

    /// <summary>
    /// Tier Type: PROGRESSIVE or TIERED
    /// </summary>
    [Required]
    [StringLength(20)]
    public string TierType { get; set; } = string.Empty;

    [Required]
    public bool IsActive { get; set; } = true;

    [Required]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Navigation Properties
    [ForeignKey(nameof(BackMarginNumber))]
    public virtual PromotionBackMargin BackMargin { get; set; } = null!;
}

/// <summary>
/// Back Margin calculation history entity
/// Stores historical calculation results for audit and payment tracking
/// </summary>
[Table("PromotionBackMarginCalculations")]
public class PromotionBackMarginCalculation
{
    [Key]
    public long Id { get; set; }

    [Required]
    [StringLength(50)]
    public string BackMarginNumber { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string VendorCode { get; set; } = string.Empty;

    [Required]
    [StringLength(20)]
    public string CalculationPeriod { get; set; } = string.Empty;

    [Required]
    public DateTime PeriodStartDate { get; set; }

    [Required]
    public DateTime PeriodEndDate { get; set; }

    /// <summary>
    /// Total purchase amount in the period
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,2)")]
    public decimal TotalPurchaseAmount { get; set; }

    /// <summary>
    /// Total purchase quantity in the period
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal TotalPurchaseQuantity { get; set; }

    /// <summary>
    /// Total number of invoices in the period
    /// </summary>
    [Required]
    public int TotalInvoiceCount { get; set; }

    /// <summary>
    /// Average payment days (for Early Payment Discount)
    /// </summary>
    [Column(TypeName = "decimal(5,2)")]
    public decimal? AveragePaymentDays { get; set; }

    /// <summary>
    /// Number of early payments (for Early Payment Discount)
    /// </summary>
    public int? EarlyPaymentCount { get; set; }

    /// <summary>
    /// Total number of payments (for Early Payment Discount)
    /// </summary>
    public int? TotalPaymentCount { get; set; }

    /// <summary>
    /// Applied tier level (for tiered discounts)
    /// </summary>
    public int? AppliedTierLevel { get; set; }

    /// <summary>
    /// Calculated discount percentage
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(5,2)")]
    public decimal DiscountPercentage { get; set; }

    /// <summary>
    /// Calculated discount amount before limits
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,2)")]
    public decimal CalculatedDiscountAmount { get; set; }

    /// <summary>
    /// Final discount amount after applying maximum limits
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,2)")]
    public decimal FinalDiscountAmount { get; set; }

    /// <summary>
    /// Detailed calculation information as JSON
    /// </summary>
    public string? CalculationDetails { get; set; }

    /// <summary>
    /// Calculation Status: CALCULATED, APPROVED, PAID, CANCELLED
    /// </summary>
    [Required]
    [StringLength(20)]
    public string CalculationStatus { get; set; } = "CALCULATED";

    [Required]
    [StringLength(100)]
    public string CalculatedBy { get; set; } = string.Empty;

    [Required]
    public DateTime CalculatedAt { get; set; } = DateTime.UtcNow;

    [StringLength(100)]
    public string? ApprovedBy { get; set; }

    public DateTime? ApprovedAt { get; set; }

    // Navigation Properties
    [ForeignKey(nameof(BackMarginNumber))]
    public virtual PromotionBackMargin BackMargin { get; set; } = null!;

    public virtual ICollection<PromotionBackMarginPayment> Payments { get; set; } = new List<PromotionBackMarginPayment>();
}

/// <summary>
/// Back Margin payment tracking entity
/// Tracks payment execution for calculated back margins
/// </summary>
[Table("PromotionBackMarginPayments")]
public class PromotionBackMarginPayment
{
    [Key]
    public long Id { get; set; }

    [Required]
    public long CalculationId { get; set; }

    [Required]
    [StringLength(50)]
    public string PaymentNumber { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string VendorCode { get; set; } = string.Empty;

    /// <summary>
    /// Payment amount
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,2)")]
    public decimal PaymentAmount { get; set; }

    /// <summary>
    /// Payment Method: CASH, TRANSFER, CHECK, CREDIT_NOTE
    /// </summary>
    [Required]
    [StringLength(50)]
    public string PaymentMethod { get; set; } = string.Empty;

    [Required]
    public DateTime PaymentDate { get; set; }

    [StringLength(100)]
    public string? PaymentReference { get; set; }

    /// <summary>
    /// Bank account (for transfers)
    /// </summary>
    [StringLength(50)]
    public string? BankAccount { get; set; }

    /// <summary>
    /// Bank name (for transfers)
    /// </summary>
    [StringLength(100)]
    public string? BankName { get; set; }

    /// <summary>
    /// Transaction reference (for transfers)
    /// </summary>
    [StringLength(100)]
    public string? TransactionReference { get; set; }

    /// <summary>
    /// Payment Status: PENDING, COMPLETED, FAILED, CANCELLED
    /// </summary>
    [Required]
    [StringLength(20)]
    public string PaymentStatus { get; set; } = "PENDING";

    [Required]
    [StringLength(100)]
    public string ProcessedBy { get; set; } = string.Empty;

    [Required]
    public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;

    // Navigation Properties
    [ForeignKey(nameof(CalculationId))]
    public virtual PromotionBackMarginCalculation Calculation { get; set; } = null!;
}
