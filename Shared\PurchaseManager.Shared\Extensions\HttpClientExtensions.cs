using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
namespace PurchaseManager.Shared.Extensions;

public static class HttpClientMultipartNewtonsoftExtensions
{
    public static async Task<T?> PostMultipartAsync<T>(
        this HttpClient httpClient,
        string requestUri,
        MultipartFormDataContent content,
        JsonSerializerSettings? settings = null)
    {
        using var response = await httpClient.PostAsync(requestUri, content);
        var raw = await response.Content.ReadAsStringAsync();

        // Dùng Newtonsoft để deserialize
        var opts = settings ?? new JsonSerializerSettings
        {
            // match tên field không phân biệt hoa/thường
            // (Json.NET vốn đã không phân biệt, nhưng để rõ ràng)
            MissingMemberHandling = MissingMemberHandling.Ignore,
            NullValueHandling = NullValueHandling.Ignore,
            // Nếu API trả camelCase thì để CamelCase; nếu snake_case thì dùng SnakeCaseNamingStrategy
            ContractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
                // NamingStrategy = new SnakeCaseNamingStrategy() // nếu backend trả snake_case
            }
        };

        // Parse JSON bất kể status code (giống thói quen PostJsonAsync trả DTO)
        T? payload = default;
        try
        {
            if (!string.IsNullOrWhiteSpace(raw))
                payload = JsonConvert.DeserializeObject<T>(raw, opts);
        }
        catch (JsonException)
        {
            // Nếu body không phải JSON, vẫn trả về default để caller tự xử
        }

        return payload;
    }
}
