﻿using Microsoft.AspNetCore.Mvc;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
namespace PurchaseManager.Server.Controllers;

/// <summary>
///     CJL Controller - Xử lý flow GR to PO direct (logic mới: quét từ GR để tối ưu performance)
/// </summary>
[SecurityHeaders]
[Route("api/[controller]/po")]
[ApiController]
public class CJLController : ControllerBase
{
    private readonly ICJLGRManager _cjlGRManager;

    public CJLController(ICJLGRManager cjlGRManager)
    {
        _cjlGRManager = cjlGRManager;
    }

    [HttpPost("update-status")]
    public async Task<ApiResponse> UpdatePOStatusByGRDataAsync([FromQuery] string poNumber = null)
    {
        return await _cjlGRManager.UpdatePOStatusByGRDataAsync(poNumber);
    }

    [HttpGet("{poNumber}/gr-status")]
    public async Task<ApiResponse> CheckGRStatusForPOAsync(string poNumber)
    {
        return await _cjlGRManager.CheckGRStatusForPOAsync(poNumber);
    }
}
