using System.ComponentModel.DataAnnotations;

namespace PurchaseManager.Shared.Dto.Promotions.FrontMargins;

/// <summary>
/// DTO for updating existing Front Margin usage record
/// </summary>
public class UpdatePromotionFrontMarginUsageDto
{
    /// <summary>
    /// New promotion number to apply
    /// </summary>
    [Required]
    [StringLength(50)]
    public string PromotionNumber { get; set; } = string.Empty;

    /// <summary>
    /// Updated quantity
    /// </summary>
    public decimal OriginalQuantity { get; set; }

    /// <summary>
    /// Updated final quantity after promotion
    /// </summary>
    public decimal FinalQuantity { get; set; }

    /// <summary>
    /// Updated original unit cost
    /// </summary>
    public decimal OriginalUnitCost { get; set; }

    /// <summary>
    /// Updated final unit cost after promotion
    /// </summary>
    public decimal FinalUnitCost { get; set; }

    /// <summary>
    /// Updated original line amount
    /// </summary>
    public decimal OriginalLineAmount { get; set; }

    /// <summary>
    /// Updated final line amount after promotion
    /// </summary>
    public decimal FinalLineAmount { get; set; }

    /// <summary>
    /// Updated discount percentage (if applicable)
    /// </summary>
    public decimal DiscountPercentage { get; set; }

    /// <summary>
    /// Updated fixed discount amount (if applicable)
    /// </summary>
    public decimal FixedDiscountAmount { get; set; }

    /// <summary>
    /// Quantity threshold for tier bonus (e.g., 100 units)
    /// Used with DiscountType = 1 (Percentage) and 2 (Fixed Amount) for tier bonuses
    /// </summary>
    public decimal? TierQuantityThreshold { get; set; }

    /// <summary>
    /// Additional percentage discount when tier threshold is met (e.g., 5%)
    /// Used with DiscountType = 1 (Percentage)
    /// </summary>
    public decimal? TierBonusPercentage { get; set; }

    /// <summary>
    /// Additional fixed amount discount when tier threshold is met (e.g., 2,000,000)
    /// Used with DiscountType = 2 (Fixed Amount)
    /// </summary>
    public decimal? TierBonusAmount { get; set; }

    /// <summary>
    /// Reason for the update
    /// </summary>
    [StringLength(500)]
    public string UpdateReason { get; set; } = string.Empty;

    /// <summary>
    /// User who made the update
    /// </summary>
    [StringLength(100)]
    public string UpdatedBy { get; set; } = string.Empty;
}
