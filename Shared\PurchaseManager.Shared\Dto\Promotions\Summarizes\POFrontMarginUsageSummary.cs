using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
namespace PurchaseManager.Shared.Dto.Promotions.Summarizes;

/// <summary>
/// PO Front Margin usage summary
/// </summary>
public class POFrontMarginUsageSummary
{
    public string PONumber { get; set; } = string.Empty;
    public int TotalLines { get; set; }
    public decimal TotalSavingsAmount { get; set; }
    public decimal TotalOriginalAmount { get; set; }
    public decimal TotalFinalAmount { get; set; }
    public decimal SavingsPercentage { get; set; }
    public List<GetPromotionFrontMarginUsageDto> LineDetails { get; set; } = [];
}
