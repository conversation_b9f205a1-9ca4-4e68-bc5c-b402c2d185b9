namespace PurchaseManager.Shared.Dto.Promotions.FrontMargins;

/// <summary>
/// DTO for Front Margin conflicts during PO approval
/// </summary>
public class FrontMarginConflictDto
{
    /// <summary>
    /// PO Line information
    /// </summary>
    public string PONumber { get; set; } = string.Empty;
    public int POLineNumber { get; set; }
    public string ItemNumber { get; set; } = string.Empty;
    public string ItemName { get; set; } = string.Empty;
    public string VendorCode { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public decimal UnitCost { get; set; }

    /// <summary>
    /// Conflict type
    /// </summary>
    public ConflictType ConflictType { get; set; }
    public string ConflictDescription { get; set; } = string.Empty;

    /// <summary>
    /// Available Front Margin options for this line
    /// </summary>
    public List<GetPromotionFrontMarginDto> AvailablePromotions { get; set; } = [];

    /// <summary>
    /// Currently selected promotion (if any)
    /// </summary>
    public string? SelectedPromotionNumber { get; set; }

    /// <summary>
    /// Requires user selection
    /// </summary>
    public bool RequiresUserSelection { get; set; }
}

/// <summary>
/// Types of Front Margin conflicts
/// </summary>
public enum ConflictType
{
    /// <summary>
    /// Multiple promotions available for same item
    /// </summary>
    MultiplePromotions = 1,

    /// <summary>
    /// No promotion selected but promotions available
    /// </summary>
    NoPromotionSelected = 2,

    /// <summary>
    /// Promotion expired or inactive
    /// </summary>
    PromotionInactive = 3,

    /// <summary>
    /// Promotion conditions not met
    /// </summary>
    ConditionsNotMet = 4
}
