using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Dto.Promotions.Summarizes;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Filters;
using PurchaseManager.Shared.Interfaces;
namespace PurchaseManager.Shared.Services.Promotions;

/// <summary>
/// API Client for Front Margin Usage operations
/// </summary>
public class FrontMarginUsageApiClient : IFrontMarginUsageApiClient
{
    private readonly HttpClient _httpClient;
    private const string BaseUrl = "api/FrontMarginUsage";

    public FrontMarginUsageApiClient(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    /// <summary>
    /// Create Draft Front Margin usage when creating PO Line
    /// </summary>
    public async Task<ApiResponseDto> CreatePromotionFrontMarginUsage(CreatePromotionFrontMarginUsageDto dto)
    {
        return await _httpClient.PostJsonAsync<ApiResponseDto>($"{BaseUrl}", dto);
    }

    /// <summary>
    /// Cancel usage record
    /// </summary>
    public async Task<ApiResponseDto> CancelUsage(string number, string reason)
    {
        var request = new CancelUsageRequest
        {
            Reason = reason
        };
        return await _httpClient.PutJsonAsync<ApiResponseDto>($"{BaseUrl}/{number}/cancel/", request);
    }

    /// <summary>
    /// Get PO promotion mapping - Compare applied vs available promotions
    /// </summary>
    public async Task<ApiResponseDto<List<AppliedPromotionMappingDto>>> GetPoPromotionTrackingUsageAsync(string poNumber)
    {
        return await _httpClient.GetJsonAsync<ApiResponseDto<List<AppliedPromotionMappingDto>>>(
        $"{BaseUrl}/front-margin/usage/{Uri.EscapeDataString(poNumber)}");
    }
}
