using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Dto.Promotions.Summarizes;
using PurchaseManager.Storage;
namespace PurchaseManager.Server.Managers;

public class FrontMarginUsageTrackingManager : IFrontMarginUsageTrackingManager
{
    private readonly IMapper _mapper;
    private readonly ApplicationDbContext _context;
    private readonly IAdminManager _adminManager;
    private readonly ILogger<FrontMarginUsageTrackingManager> _logger;

    public FrontMarginUsageTrackingManager(IMapper mapper, ApplicationDbContext context,
        ILogger<FrontMarginUsageTrackingManager> logger, IAdminManager adminManager)
    {
        _mapper = mapper;
        _context = context;
        _logger = logger;
        _adminManager = adminManager;
    }
    public async Task<ApiResponse> TrackPromotionUsageAsync(CreatePromotionFrontMarginUsageDto usageDto)
    {
        try
        {
            // Fetch ProgramNumber from PromotionFrontMargin
            var promotion = await _context.PromotionFrontMargins
                .FirstOrDefaultAsync(p => p.Number == usageDto.PromotionNumber);

            if (promotion == null)
            {
                return ApiResponse.S404($"Promotion {usageDto.PromotionNumber} not found");
            }

            var usage = _mapper.Map<PromotionFrontMarginUsage>(usageDto);
            usage.Number = await _adminManager.CreateNumberSeries("FRONTMARINUSAGE", "AL");
            usage.ModificationStatus = 1;
            usage.CreatedAt = DateTime.Now;
            usage.CreatedBy = _adminManager.GetUserLogin() ?? "SYSTEM";

            _context.PromotionFrontMarginUsages.Add(usage);
            await _context.SaveChangesAsync();

            _logger.LogInformation(
            "Tracked Front Margin usage: {PromotionNumber} (Program: {ProgramNumber}) for PO {PONumber} Line {POLineNumber} - Status: {Status}",
            usageDto.PromotionNumber, promotion.ProgramNumber, usageDto.PONumber, usageDto.POLineNumber, usageDto.Status);

            return ApiResponse.S200("Usage tracked successfully", usage.RowId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error tracking Front Margin usage for promotion {PromotionNumber}", usageDto.PromotionNumber);
            return ApiResponse.S500($"Error tracking usage: {ex.Message}");
        }
    }
    /// <summary>
    /// Update an existing Front Margin usage record
    /// </summary>
    public async Task<ApiResponse> UpdateUsageAsync(string number, UpdatePromotionFrontMarginUsageDto dto)
    {
        try
        {
            var usage = await _context.PromotionFrontMarginUsages
                .Include(u => u.PromotionFrontMargin)
                .FirstOrDefaultAsync(u => u.Number == number
                                          && u.ModificationStatus == (int)ModificationStatusEnum.ACTIVE);

            if (usage == null)
            {
                return ApiResponse.S404($"Usage record {number} not found");
            }

            // Check if the new promotion exists
            var newPromotion = await _context.PromotionFrontMargins
                .FirstOrDefaultAsync(p => p.Number == dto.PromotionNumber);

            if (newPromotion == null)
            {
                return ApiResponse.S404($"Promotion {dto.PromotionNumber} not found");
            }

            // Update usage record with new promotion and values
            usage.PromotionNumber = dto.PromotionNumber;
            usage.OriginalQuantity = dto.OriginalQuantity;
            usage.FinalQuantity = dto.FinalQuantity;
            usage.OriginalUnitCost = dto.OriginalUnitCost;
            usage.FinalUnitCost = dto.FinalUnitCost;
            usage.OriginalLineAmount = dto.OriginalLineAmount;
            usage.FinalLineAmount = dto.FinalLineAmount;
            usage.DiscountPercentage = dto.DiscountPercentage;
            usage.FixedDiscountAmount = dto.FixedDiscountAmount;
            usage.TierQuantityThreshold = dto.TierQuantityThreshold;
            usage.TierBonusPercentage = dto.TierBonusPercentage;
            usage.TierBonusAmount = dto.TierBonusAmount;
            usage.Notes = $"{usage.Notes}\n[UPDATED] {dto.UpdateReason}";
            usage.LastModifiedAt = DateTime.Now;
            usage.LastModifiedBy = dto.UpdatedBy;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Updated Front Margin usage {Number} with promotion {PromotionNumber} by {UpdatedBy}: {Reason}",
            number, dto.PromotionNumber, dto.UpdatedBy, dto.UpdateReason);

            return ApiResponse.S200($"Usage record {number} updated successfully with promotion {dto.PromotionNumber}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating usage {Number}", number);
            return ApiResponse.S500(ex.Message);
        }
    }

    public async Task<ApiResponse> CancelUsageAsync(string number, CancelUsageRequest cancelUsageRequest)
    {
        try
        {
            var usage = await _context.PromotionFrontMarginUsages
                .FirstOrDefaultAsync(u => u.Number == number
                                          && u.ModificationStatus == (int)ModificationStatusEnum.ACTIVE);

            if (usage == null)
                return ApiResponse.S404("Usage record not found");

            // Cancelled
            usage.ModificationStatus = (int)ModificationStatusEnum.DELETED;
            usage.LastModifiedAt = DateTime.Now;
            usage.LastModifiedBy = _adminManager.GetUserLogin();
            usage.Notes = $"{usage.Notes}\n[CANCELLED] {cancelUsageRequest.Reason}";

            await _context.SaveChangesAsync();

            _logger.LogInformation("Cancelled Front Margin usage {Id} by {CancelledBy}: {Reason}",
            number, _adminManager.GetUserLogin(), cancelUsageRequest.Reason);

            return ApiResponse.S200("Usage cancelled successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling usage {Id}", number);
            return ApiResponse.S500(ex.Message);
        }
    }
    
    public async Task<ApiResponse> GetPromotionFrontMarginUsage(string poNumber)
    {
        try
        {
            var usages = await _context.PromotionFrontMarginUsages
                .Include(u => u.PromotionFrontMargin)
                .ThenInclude(p => p.PromotionHeader)
                .Where(u => u.PONumber == poNumber && u.ModificationStatus == 1)
                .ToListAsync();

            // Use AutoMapper to map PromotionFrontMarginUsage to AppliedPromotionMappingDto
            var appliedMappings = _mapper.Map<List<AppliedPromotionMappingDto>>(usages);

            return ApiResponse.S200("Promotion usage retrieved successfully", appliedMappings);
        }
        catch (Exception ex)
        {
            return ApiResponse.S500(ex.Message);
        }
    }
}
