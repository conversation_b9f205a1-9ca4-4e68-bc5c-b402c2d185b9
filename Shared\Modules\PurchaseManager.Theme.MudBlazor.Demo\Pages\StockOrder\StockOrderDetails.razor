@page "/stock/{DocumentNo}/{DocumentStatus}/detail"
@page "/stock/{DocumentNo}/detail"
@using PurchaseManager.Shared.Dto.StockOrder
@using PurchaseManager.Theme.Material.Demo.Shared.Components.PO.Services
@using PurchaseManager.Theme.Material.Demo.Pages.PurchaseOrder
@attribute [Authorize]
<PageTitle>Purchase Order</PageTitle>
@if (IsProcessing)
{
    <MudProgressLinear Indeterminate="@IsProcessing" />
}
else
{
    <MudTabs Class="mt-3"
             PanelClass="pt-3"
             Rounded=true
             ApplyEffectsToContainer
             MinimumTabWidth="20px"
             Elevation="1">
        <MudTabPanel Text="@L["List Stock Order"]">
            <MudGrid Spacing="0" Justify="Justify.SpaceBetween">
                <MudItem xs="4" sm="6" md="6">
                    <MudStack Row AlignItems="AlignItems.Center">
                        <MudIconButton Color="Color.Default" Icon="@Icons.Material.Filled.ArrowBack"
                                       OnClick="@(() => { NavigationManager.NavigateTo("/stock-order"); })"
                                       Variant="Variant.Text">
                        </MudIconButton>
                        <MudText Class="d-none d-sm-block" Typo="Typo.button">
                            @L["PurchaseOrderDetail"]
                        </MudText>
                        <MudText Class="" Typo="Typo.button">
                            @DocumentNo
                        </MudText>
                    </MudStack>
                    <MudIconButton Icon="@Icons.Material.Filled.Refresh" Size="Size.Medium"
                                   OnClick="@(async _ => await LoadDataAsync())"
                                   Variant="Variant.Text" />
                    <div class="d-inline">
                        <MudIconButton Icon="@Icons.Material.Outlined.Info" Size="Size.Medium"
                                       Color="@(IsShowBuyerInfo ? Color.Primary : Color.Default)"
                                       OnClick="@(() => IsShowBuyerInfo = !IsShowBuyerInfo)"
                                       Variant="Variant.Text" />
                        <MudPopover Open="@IsShowBuyerInfo" Class="px-4 pt-4" AnchorOrigin="Origin.BottomCenter"
                                    OverflowBehavior="OverflowBehavior.FlipNever" TransformOrigin="Origin.TopLeft">
                            <MudStack AlignItems="AlignItems.Center" Row Justify="Justify.SpaceBetween">
                                <MudText><strong>Thông tin người mua hàng</strong></MudText>
                                <MudIconButton Size="Size.Small" Icon="@Icons.Material.Outlined.Close"
                                               Color="@Color.Error"
                                               OnClick="@(() => IsShowBuyerInfo = !IsShowBuyerInfo)"
                                               Variant="Variant.Text" />
                            </MudStack>
                            <MudStack Class="pa-4" Style="width: 100%;" Justify="Justify.SpaceBetween">
                                @if (!string.IsNullOrEmpty(UserCreatedPO.UserName))
                                {
                                    <MudStack Row Justify="Justify.SpaceBetween">
                                        <MudText>
                                            @L["CreatedBy"]
                                        </MudText>
                                        <MudStack>
                                            <MudText Typo="Typo.body2">
                                                <b>@UserCreatedPO.FullName</b>
                                            </MudText>
                                        </MudStack>
                                    </MudStack>
                                    <MudStack Row Justify="Justify.SpaceBetween">
                                        <MudText>
                                            @L["Code"]
                                        </MudText>
                                        <MudStack>
                                            <MudText Typo="Typo.body2">
                                                <b>@UserCreatedPO.UserName</b>
                                            </MudText>
                                        </MudStack>
                                    </MudStack>
                                }
                                <MudStack Row Justify="Justify.SpaceBetween">
                                    <MudText>
                                        @L["OrderDate"]:
                                    </MudText>
                                    <b>@PoHeader.OrderDate.ToString("dd-MM-yyyy")</b>
                                </MudStack>
                                <MudStack Row Justify="Justify.SpaceBetween">
                                    <MudText>
                                        @L["DueDate"]:
                                    </MudText>
                                    <b>@PoHeader.DueDate.ToString("dd-MM-yyyy")</b>
                                </MudStack>
                            </MudStack>
                        </MudPopover>
                    </div>
                </MudItem>
                <MudItem xs="8" sm="6" md="6">
                    <MudStack Class="pa-2" Spacing="6" Justify="Justify.FlexEnd" AlignItems="AlignItems.End">
                        <MudChip Size="Size.Small" T="String" Color="Color.Primary">
                            @POComponentExtension.GetStatusText(PoHeader.Status)
                        </MudChip>
                        <MudText Align="Align.Center" Typo="Typo.button">
                            @VendorInfo.Name
                        </MudText>
                    </MudStack>
                </MudItem>
            </MudGrid>
            <MudDivider />
            <MudStack Row Justify="Justify.FlexEnd" Class="pa-2" Spacing="0">
                <MudTextField Value="@ItemFilterInPOLineTable" T="String" Placeholder="Type to search..."
                              Clearable ValueChanged="@OnFilterItem" />
                <div>
                    @if (CanOpen())
                    {
                        <MudStack Row Justify="Justify.FlexEnd" Class="ms-3">
                            @if (IsEdit)
                            {
                                <MudButton OnClick="@(async _ => await SaveChangeHeaderPoOrOpenPo(true))"
                                           Variant="Variant.Outlined" Color="Color.Primary">
                                    @L["Save"]
                                </MudButton>
                            }
                            else
                            {
                                <MudButton OnClick="@(async _ => await OpenedPo())"
                                           Variant="Variant.Outlined" Color="Color.Primary">
                                    @L["Open"]
                                </MudButton>
                            }
                        </MudStack>
                    }
                </div>
            </MudStack>
            <MudGrid Spacing="2" Justify="Justify.Center" Class="px-2 pb-2">
                <MudItem xs="12">
                    <MudTable Striped="false" Outlined Dense Items="@PoLines" T="SOLineGetDto"
                              CanCancelEdit
                              Height="500px" FixedHeader
                              FixedFooter
                              ReadOnly="@IsEdit"
                              SelectedItemChanged="@OnPOLineSelectedChanged"
                              SelectedItem="@SOLineSelected"
                              OnRowClick="@RowClickEvent"
                              RowClassFunc="@SelectedRowClassFunc"
                              RowClass="cursor-pointer">
                        <HeaderContent>
                            <MudTh>@L["ItemName"]</MudTh>
                            <MudTh>@L["Unit"]</MudTh>
                            <MudTh>@L["Conversion Rate"]</MudTh>
                            <MudTh>@L["Quantity"]</MudTh>
                            <MudTh>@L["Received"]</MudTh>
                            <MudTh>@L["Remaining"]</MudTh>
                            @* <MudTh></MudTh> *@
                            @* <MudTh Style="text-align: center">@L["To received"]</MudTh> *@
                            @* <MudTh Style="text-align: center">@L["LotNo"]</MudTh>
                                        <MudTh Style="text-align: center">@L["ExpirationDate"]</MudTh> *@
                        </HeaderContent>
                        <RowTemplate>
                            <MudTd DataLabel="Item">
                                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                                    <div class="">
                                        <MudText Typo="Typo.body1">
                                            @context.ItemName
                                        </MudText>
                                        <MudText Typo="Typo.subtitle2"> Number: @context.ItemNumber </MudText>
                                    </div>
                                    @if (context.QuantityReceived - context.Quantity > 0)
                                    {
                                        <div class="">
                                            <MudTooltip
                                                        Text="@($"Số lượng thực nhận vượt quá số lượng đặt hàng: {(int)context.QuantityReceived - (int)context.Quantity} item")">
                                                <MudIconButton Size="Size.Small"
                                                               Icon="@Icons.Material.Filled.QuestionMark" />
                                            </MudTooltip>
                                        </div>
                                    }
                                </MudStack>
                            </MudTd>
                            <MudTd DataLabel="Unit">
                                @context.UnitOfMeasure
                            </MudTd>
                            <MudTd DataLabel="HSQD">
                                @if (!string.IsNullOrEmpty(context.ConversionRateDisplay))
                                {
                                    <MudText>
                                        @context.ConversionRateDisplay
                                    </MudText>
                                }
                            </MudTd>
                            <MudTd DataLabel="SL">
                                @context.Quantity.ToString("N0")
                            </MudTd>
                            <MudTd DataLabel="SL.Đã nhận">
                                @context.QuantityReceived.ToString("N0")
                            </MudTd>
                            <MudTd DataLabel="SL.Cần nhận">
                                @context.QuantityToReceive.ToString("N0")
                            </MudTd>
                            @* <MudTd Style="width: 20px">
                                            <MudIconButton Icon="@Icons.Material.Filled.ArrowForward" Disabled="@(IsArrowDisabled(context))"
                                                        OnClick="() => OnArrowClick(context)" />
                                        </MudTd> *@
                            @* <MudTd>
                                            <MudNumericField ReadOnly="@(!IsEdit)" T="Int32" Immediate
                                                            Value="context.TempReceive"
                                                            ValueChanged="receive => CheckToReceived(receive, context)" />
                                        </MudTd> *@
                            @* <MudTd>
                                            <MudTextField ReadOnly="@(!IsEdit)" T="String" Immediate
                                                        @bind-Value="context.LotNo" />
                                        </MudTd> *@
                            @* <MudTd>
                                            <MudDatePicker
                                                        T="DateTime?"
                                                        @bind-Value="@context.ExpirationDate"
                                                        DateChanged="date => DateChanged(date, context)"
                                                        DateFormat="dd/MM/yyyy"
                                                        Editable
                                                        Mask="@(new DateMask("dd/MM/yyyy"))"
                                                        ReadOnly="@(!IsEdit)" />
                                        </MudTd> *@
                        </RowTemplate>
                        <PagerContent>
                            <MudTablePager />
                        </PagerContent>
                    </MudTable>
                </MudItem>
            </MudGrid>
        </MudTabPanel>
        <MudTabPanel Text="PO">
            <div class="px-4">
                <PODetails PONumber="@DocumentNo" />
            </div>
        </MudTabPanel>
    </MudTabs>
    <MudDialog Options="new DialogOptions() { FullWidth = true, MaxWidth = MaxWidth.Medium, CloseButton = true, BackdropClick = false }"
               @bind-Visible="@IsShowAddOrEditDialog">
        <TitleContent>
            <strong>@SOLineSelected.ItemName</strong>
            <br />
            <MudStack Row Justify="Justify.SpaceBetween" Class="pe-10">
                <small>
                    <MudText Color="Color.Success" Typo="Typo.caption">
                        Đã nhập:
                        @ListStockOrderEditing.Sum(x => x.QuantityReceived).ToString("N0")
                    </MudText>
                </small>
                <small>
                    <MudText Color="Color.Warning" Typo="Typo.caption">
                        Còn lại:
                        @SOLineSelected.QuantityToReceive.ToString("N0")
                    </MudText>
                </small>
            </MudStack>
        </TitleContent>
        <DialogContent>
            <div class="d-none d-sm-block">
                <MudStack Style="width: 100%;" Class="mb-2" StretchItems="StretchItems.Middle">
                    @if (SOLineSelected is null)
                    {
                        <MudAlert ContentAlignment="HorizontalAlignment.Center" Severity="Severity.Info">
                            @L["AllStockOrder"]:
                            <strong>@ListStockOrderEditing.Count()</strong>
                        </MudAlert>
                    }
                    else
                    {
                        <MudButton FullWidth Disabled="@(!IsEdit)"
                                   Color="Color.Success"
                                   Variant="Variant.Outlined"
                                   OnClick="@OnAddStockOrderClicked"
                                   StartIcon="@Icons.Material.Filled.Add">
                            @L["Add"]
                        </MudButton>
                    }
                </MudStack>
            </div>
            <MudTable T="GetStockOrderDto"
                      Elevation="0"
                      Context="context"
                      FixedHeader
                      Outlined
                      Dense
                      Height="500px"
                      ApplyButtonPosition="@TableApplyButtonPosition.End"
                      EditTrigger="TableEditTrigger.RowClick"
                      CanCancelEdit
                      OnPreviewEditClick="@BackupItem"
                      RowEditableFunc="@CanEditStockOrderRow"
                      RowEditPreview="@BackupItem"
                      RowEditCancel="@ResetItemToOriginalValues"
                      RowEditCommit="@(async value => await ItemHasBeenCommittedAsync(value))"
                      Items="@ListStockOrderEditing">
                <HeaderContent>
                    <MudTh>@L["#"]</MudTh>
                    <MudTh>@L["QuantityReceived"]</MudTh>
                    <MudTh>@L["LotNo"]</MudTh>
                    <MudTh>@L["ExpirationDate"]</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="Index">
                        <MudText>
                            @context.Index
                        </MudText>
                    </MudTd>
                    <MudTd DataLabel="SL.Nhận">
                        <MudStack Row Justify="Justify.SpaceBetween">
                            <MudText>@context.QuantityReceived</MudText>
                            @if (context.IsERPSynced)
                            {
                                <MudTooltip Text="ERP Synced">
                                    <MudIcon Color="Color.Success" Icon="@Icons.Material.Filled.CloudSync" />
                                </MudTooltip>
                            }
                        </MudStack>
                    </MudTd>
                    <MudTd DataLabel="LotNo">
                        <MudText>@context.LotNo</MudText>
                    </MudTd>
                    <MudTd DataLabel="Exp">
                        <MudText>@context.ExpirationDate.ToString("dd/MM/yyyy")</MudText>
                    </MudTd>
                    @if (context.IsAdding)
                    {
                        <MudTd>
                            <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                           Color="Color.Error"
                                           OnClick="@(_ => OnRemoveStockOrderClicked(context.Number))"
                                           Variant="Variant.Outlined" />
                        </MudTd>
                    }
                </RowTemplate>
                <RowEditingTemplate>
                    <MudTd DataLabel="Index">
                        <MudText>@context.Index</MudText>
                    </MudTd>
                    <MudTd DataLabel="SL.Đã nhận">
                    <MudNumericField Required AutoFocus
                                     Margin="Margin.Dense" Variant="Variant.Outlined"
                                     Max="@MaxAllowedQuantity"
                                     @bind-Value="@context.QuantityReceived"
                                     Error="@context.HasError"
                                     Immediate
                                     Min="1"
                                     ErrorText="@context.ErrorText"
                                         HelperText="@($"Tối đa: {MaxAllowedQuantity}")" />
                </MudTd>
                <MudTd DataLabel="LotNo">
                    <MudTextField Required Variant="Variant.Outlined" Margin="Margin.Dense"
                                  @bind-Value="@context.LotNo" />
                </MudTd>
                <MudTd DataLabel="Exp">
                    <MudTextField @bind-Value="context.ExpirationDateText" @bind-Value:event="oninput"
                                  Variant="Variant.Outlined"
                                  Margin="Margin.Dense"
                                  Immediate="true"
                                  T="String"
                                  Error="@context.HasError"
                                  ErrorText="@context.ErrorText"
                                  ValueChanged="@(v => OnDateChanged(v, context))" />
                </MudTd>
            </RowEditingTemplate>
        </MudTable>
        <div class="d-block d-sm-none">
            <MudStack Style="width: 100%;" Class="mt-2" StretchItems="StretchItems.Middle">
                @if (SOLineSelected is null)
                    {
                        <MudAlert ContentAlignment="HorizontalAlignment.Center" Severity="Severity.Info">
                            @L["AllStockOrder"]:
                            <strong>@ListStockOrderEditing.Count()</strong>
                        </MudAlert>
                    }
                    else
                    {
                        <MudButton FullWidth Disabled="@(!IsEdit)"
                                   Color="Color.Success"
                                   Variant="Variant.Outlined"
                                   OnClick="@OnAddStockOrderClicked"
                                   StartIcon="@Icons.Material.Filled.Add">
                            @L["Add"]
                        </MudButton>
                    }
                </MudStack>
            </div>
        </DialogContent>
    </MudDialog>
}
<style>
    .selected {
        background-color: rgba(0, 110, 255, 0.1) !important;
    }

    .received-over {
        background-color: rgba(243, 71, 71, 0.1);
    }

    .received-full {
        background-color: rgba(0, 255, 34, 0.1);
    }

    .selected>td {
        color: #333;
    }

    .selected>td .mud-input {
        color: #333 !important;
    }
</style>
