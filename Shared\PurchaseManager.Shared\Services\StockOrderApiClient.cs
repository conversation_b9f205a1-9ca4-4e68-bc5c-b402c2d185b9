﻿using System.Net.Http.Json;
using Microsoft.Extensions.Logging;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.StockOrder;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models.StockOrder;
namespace PurchaseManager.Shared.Services;

public class StockOrderApiClient : BaseApiClient, IStockOrderApiClient
{

    public StockOrderApiClient(HttpClient httpClient, ILogger<BaseApiClient> logger, string rootApiPath = "api/data/") : base(
    httpClient, logger, rootApiPath)
    {
    }
    public async Task<ApiResponseDto> CreateMultipleStockOrder(CreateStockOrderDto stockOrders)
        => await httpClient.PostJsonAsync<ApiResponseDto>("api/StockOrder/create", stockOrders);

    public async Task<ApiResponseDto<List<GetStockOrderDto>>> GetStockOrderByPoHeader(string poHeader)
        => await httpClient.GetJsonAsync<ApiResponseDto<List<GetStockOrderDto>>>($"api/StockOrder/by-po-header/{poHeader}");

    public async Task<ApiResponseDto<PagedResultDto<GetStockOrderDto>>> GetStockOrderByPoHeaderAsync(StockOrderFilter filter)
        => await httpClient.GetFromJsonAsync<ApiResponseDto<PagedResultDto<GetStockOrderDto>>>("/api/StockOrder/Gets?" +
        filter.ToQuery());

    public async Task<ApiResponseDto> UpdateStockOrderAsync(string number, UpdateStockOrderDto dto)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto>($"api/StockOrder/update/{number}", dto);
    }

    public async Task<ApiResponseDto> SaveDraftStockOrdersAsync(string headerNumber)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto>($"api/StockOrder/save-drafts/{headerNumber}", new { });
    }

    public async Task<ApiResponseDto<List<SOLineGetDto>>> GetLinesForStockOrder(string purchaseOrderNumber)
    {
        return await httpClient.GetJsonAsync<ApiResponseDto<List<SOLineGetDto>>>(
        $"api/StockOrder/stock-order-lines/{purchaseOrderNumber}");
    }

}
