using PurchaseManager.Shared.Dto.PO;
namespace PurchaseManager.Shared.Dto.MarginDto.FrontMargin.ResultServices;

public class DiscountCalculationResult
{
    public decimal NewUnitCost { get; set; }
    public decimal DiscountAmount { get; set; }
    public string CalculationMethod { get; set; } = string.Empty;
    public string Details { get; set; } = string.Empty;
    public List<POLineGetDto>? GiftLines { get; set; }
}
