﻿using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.StockOrder;
using PurchaseManager.Shared.Models.StockOrder;
namespace PurchaseManager.Infrastructure.Server;

public interface IStockOrderManager
{
    Task<ApiResponse> GetReceiveLotsByPoHeaderAsync(StockOrderFilter poHeader);
    Task<ApiResponse> CreateReceiveLotsAsync(CreateStockOrderDto stockOrders);
    Task<ApiResponse> UpdateReceiveLotsAsync(string number, UpdateStockOrderDto stockOrders);
    Task<ApiResponse> GetLinesStockOrderAsync(string code);
    Task<ApiResponse> SaveDraftStockOrdersAsync(string headerNumber);
}
