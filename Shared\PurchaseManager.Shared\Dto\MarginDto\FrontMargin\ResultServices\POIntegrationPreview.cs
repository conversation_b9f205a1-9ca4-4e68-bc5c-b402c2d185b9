namespace PurchaseManager.Shared.Dto.MarginDto.FrontMargin.ResultServices;

public class POIntegrationPreview
{
    public string PONumber { get; set; } = string.Empty;
    public string VendorCode { get; set; } = string.Empty;
    public List<LineImpactPreview> LineImpacts { get; set; } = [];
    public decimal TotalOriginalAmount { get; set; }
    public decimal TotalProjectedSavings { get; set; }
    public decimal ProjectedDiscountPercentage { get; set; }
    public int LinesWithPromotions { get; set; }
    public int TotalApplicablePromotions { get; set; }
    public string? ErrorMessage { get; set; }
}
