-- =============================================
-- Back Margin Database Migration Script
-- Created: 2025-01-14
-- Description: Creates tables for Back Margin system
-- =============================================

-- 1. Update PromotionHeaders table (if needed)
-- Add any missing columns for Back Margin support
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('PromotionHeaders') AND name = 'AccumulateRevenue')
BEGIN
    ALTER TABLE PromotionHeaders ADD AccumulateRevenue BIT NOT NULL DEFAULT 0;
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('PromotionHeaders') AND name = 'SupportTypeNumber')
BEGIN
    ALTER TABLE PromotionHeaders ADD SupportTypeNumber NVARCHAR(50) NULL;
END

-- 2. Create PromotionBackMarginTiers table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'PromotionBackMarginTiers')
BEGIN
    CREATE TABLE PromotionBackMarginTiers (
        Number NVARCHAR(50) NOT NULL PRIMARY KEY,
        BackMarginNumber NVARCHAR(50) NOT NULL,
        TierLevel INT NOT NULL,
        MinimumThreshold DECIMAL(18,2) NULL,
        MaximumThreshold DECIMAL(18,2) NULL,
        MilestoneValue DECIMAL(18,2) NULL,
        DiscountPercentage DECIMAL(5,2) NULL,
        FixedDiscountAmount DECIMAL(18,2) NULL,
        MaximumDiscountAmount DECIMAL(18,2) NULL,
        TierType NVARCHAR(20) NOT NULL,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),

        -- Audit fields (FullTrackingEntity)
        CreatedBy NVARCHAR(100) NULL,
        CreatedOn DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        LastModifiedBy NVARCHAR(100) NULL,
        LastModifiedOn DATETIME2 NULL,
        DeletedBy NVARCHAR(100) NULL,
        DeletedOn DATETIME2 NULL,
        ModificationStatus INT NOT NULL DEFAULT 1,

        -- Foreign Key
        CONSTRAINT FK_PromotionBackMarginTiers_BackMargin
            FOREIGN KEY (BackMarginNumber) REFERENCES PromotionBackMargins(Number)
    );

    -- Indexes
    CREATE INDEX IX_PromotionBackMarginTiers_BackMarginNumber ON PromotionBackMarginTiers(BackMarginNumber);
    CREATE INDEX IX_PromotionBackMarginTiers_TierLevel ON PromotionBackMarginTiers(TierLevel);
END

-- 3. Create PromotionBackMarginTrackings table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'PromotionBackMarginTrackings')
BEGIN
    CREATE TABLE PromotionBackMarginTrackings (
        Number NVARCHAR(50) NOT NULL PRIMARY KEY,
        BackMarginNumber NVARCHAR(50) NOT NULL,
        VendorCode NVARCHAR(20) NOT NULL,
        ItemNumber NVARCHAR(50) NULL,
        PONumber NVARCHAR(50) NOT NULL,
        POLineNumber INT NOT NULL,
        TransactionDate DATETIME2 NOT NULL,
        Quantity DECIMAL(18,4) NOT NULL,
        UnitCost DECIMAL(18,4) NOT NULL,
        TotalAmount DECIMAL(18,2) NOT NULL,
        AccumulatedQuantity DECIMAL(18,4) NOT NULL DEFAULT 0,
        AccumulatedAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
        PaymentDate DATETIME2 NULL,
        PaymentDays INT NULL,
        QualifiesForEarlyPayment BIT NOT NULL DEFAULT 0,
        ProcessingStatus INT NOT NULL DEFAULT 1,
        Notes NVARCHAR(500) NULL,

        -- Audit fields (FullTrackingEntity)
        CreatedBy NVARCHAR(100) NULL,
        CreatedOn DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        LastModifiedBy NVARCHAR(100) NULL,
        LastModifiedOn DATETIME2 NULL,
        DeletedBy NVARCHAR(100) NULL,
        DeletedOn DATETIME2 NULL,
        ModificationStatus INT NOT NULL DEFAULT 1,

        -- Foreign Keys
        CONSTRAINT FK_PromotionBackMarginTrackings_BackMargin
            FOREIGN KEY (BackMarginNumber) REFERENCES PromotionBackMargins(Number),
        CONSTRAINT FK_PromotionBackMarginTrackings_Vendor
            FOREIGN KEY (VendorCode) REFERENCES Vendors(Code)
    );

    -- Indexes
    CREATE INDEX IX_PromotionBackMarginTrackings_BackMarginNumber ON PromotionBackMarginTrackings(BackMarginNumber);
    CREATE INDEX IX_PromotionBackMarginTrackings_VendorCode ON PromotionBackMarginTrackings(VendorCode);
    CREATE INDEX IX_PromotionBackMarginTrackings_PONumber ON PromotionBackMarginTrackings(PONumber);
    CREATE INDEX IX_PromotionBackMarginTrackings_TransactionDate ON PromotionBackMarginTrackings(TransactionDate);
    CREATE INDEX IX_PromotionBackMarginTrackings_ProcessingStatus ON PromotionBackMarginTrackings(ProcessingStatus);
END

-- 4. Create PromotionBackMarginEarneds table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'PromotionBackMarginEarneds')
BEGIN
    CREATE TABLE PromotionBackMarginEarneds (
        Number NVARCHAR(50) NOT NULL PRIMARY KEY,
        BackMarginNumber NVARCHAR(50) NOT NULL,
        VendorCode NVARCHAR(20) NOT NULL,
        ItemNumber NVARCHAR(50) NULL,
        PeriodStartDate DATETIME2 NOT NULL,
        PeriodEndDate DATETIME2 NOT NULL,
        TotalQuantity DECIMAL(18,4) NOT NULL DEFAULT 0,
        TotalAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
        TierLevelAchieved INT NULL,
        EarnedAmount DECIMAL(18,2) NOT NULL,
        DiscountPercentage DECIMAL(5,2) NULL,
        CalculationFormula NVARCHAR(500) NULL,
        CalculationDetails NVARCHAR(2000) NULL,
        CalculatedDate DATETIME2 NOT NULL,
        CalculatedBy NVARCHAR(100) NULL,
        Status INT NOT NULL DEFAULT 1,
        PaymentMethod INT NOT NULL,
        PaymentTiming INT NOT NULL,
        ExpectedPaymentDate DATETIME2 NULL,
        Notes NVARCHAR(500) NULL,

        -- Audit fields (FullTrackingEntity)
        CreatedBy NVARCHAR(100) NULL,
        CreatedOn DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        LastModifiedBy NVARCHAR(100) NULL,
        LastModifiedOn DATETIME2 NULL,
        DeletedBy NVARCHAR(100) NULL,
        DeletedOn DATETIME2 NULL,
        ModificationStatus INT NOT NULL DEFAULT 1,

        -- Foreign Keys
        CONSTRAINT FK_PromotionBackMarginEarneds_BackMargin
            FOREIGN KEY (BackMarginNumber) REFERENCES PromotionBackMargins(Number),
        CONSTRAINT FK_PromotionBackMarginEarneds_Vendor
            FOREIGN KEY (VendorCode) REFERENCES Vendors(Code)
    );

    -- Indexes
    CREATE INDEX IX_PromotionBackMarginEarneds_BackMarginNumber ON PromotionBackMarginEarneds(BackMarginNumber);
    CREATE INDEX IX_PromotionBackMarginEarneds_VendorCode ON PromotionBackMarginEarneds(VendorCode);
    CREATE INDEX IX_PromotionBackMarginEarneds_Status ON PromotionBackMarginEarneds(Status);
    CREATE INDEX IX_PromotionBackMarginEarneds_PeriodDates ON PromotionBackMarginEarneds(PeriodStartDate, PeriodEndDate);
    CREATE INDEX IX_PromotionBackMarginEarneds_CalculatedDate ON PromotionBackMarginEarneds(CalculatedDate);
END

-- 5. Create PromotionBackMarginPayments table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'PromotionBackMarginPayments')
BEGIN
    CREATE TABLE PromotionBackMarginPayments (
        Number NVARCHAR(50) NOT NULL PRIMARY KEY,
        EarnedBackMarginNumber NVARCHAR(50) NOT NULL,
        PONumber NVARCHAR(50) NULL,
        PaymentDate DATETIME2 NOT NULL,
        PaymentAmount DECIMAL(18,2) NOT NULL,
        PaymentMethod INT NOT NULL,
        PaymentReference NVARCHAR(100) NULL,
        PaymentDetails NVARCHAR(200) NULL,
        PaymentStatus INT NOT NULL DEFAULT 1,
        ProcessedBy NVARCHAR(100) NULL,
        ProcessedDate DATETIME2 NULL,
        ApprovalStatus INT NOT NULL DEFAULT 1,
        ApprovedBy NVARCHAR(100) NULL,
        ApprovedDate DATETIME2 NULL,
        Notes NVARCHAR(500) NULL,

        -- Audit fields (FullTrackingEntity)
        CreatedBy NVARCHAR(100) NULL,
        CreatedOn DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        LastModifiedBy NVARCHAR(100) NULL,
        LastModifiedOn DATETIME2 NULL,
        DeletedBy NVARCHAR(100) NULL,
        DeletedOn DATETIME2 NULL,
        ModificationStatus INT NOT NULL DEFAULT 1,

        -- Foreign Key
        CONSTRAINT FK_PromotionBackMarginPayments_EarnedBackMargin
            FOREIGN KEY (EarnedBackMarginNumber) REFERENCES PromotionBackMarginEarneds(Number)
    );

    -- Indexes
    CREATE INDEX IX_PromotionBackMarginPayments_EarnedBackMarginNumber ON PromotionBackMarginPayments(EarnedBackMarginNumber);
    CREATE INDEX IX_PromotionBackMarginPayments_PaymentDate ON PromotionBackMarginPayments(PaymentDate);
    CREATE INDEX IX_PromotionBackMarginPayments_PaymentStatus ON PromotionBackMarginPayments(PaymentStatus);
    CREATE INDEX IX_PromotionBackMarginPayments_ApprovalStatus ON PromotionBackMarginPayments(ApprovalStatus);
END

-- 6. Update PromotionBackMargins table (if exists and needs modification)
-- Add any missing columns that might be needed
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'PromotionBackMargins')
BEGIN
    -- Add PaymentMethod column if not exists
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('PromotionBackMargins') AND name = 'PaymentMethod')
    BEGIN
        ALTER TABLE PromotionBackMargins ADD PaymentMethod INT NOT NULL DEFAULT 1;
    END

    -- Add PaymentTiming column if not exists
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('PromotionBackMargins') AND name = 'PaymentTiming')
    BEGIN
        ALTER TABLE PromotionBackMargins ADD PaymentTiming INT NOT NULL DEFAULT 1;
    END

    -- Add DiscountValueType column if not exists
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('PromotionBackMargins') AND name = 'DiscountValueType')
    BEGIN
        ALTER TABLE PromotionBackMargins ADD DiscountValueType INT NOT NULL DEFAULT 1;
    END

    -- Add EarlyPaymentDays column if not exists
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('PromotionBackMargins') AND name = 'EarlyPaymentDays')
    BEGIN
        ALTER TABLE PromotionBackMargins ADD EarlyPaymentDays INT NULL;
    END

    -- Add EarlyPaymentDiscountPercentage column if not exists
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('PromotionBackMargins') AND name = 'EarlyPaymentDiscountPercentage')
    BEGIN
        ALTER TABLE PromotionBackMargins ADD EarlyPaymentDiscountPercentage DECIMAL(5,2) NULL;
    END

    -- Add SupportTypeNumber column if not exists
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('PromotionBackMargins') AND name = 'SupportTypeNumber')
    BEGIN
        ALTER TABLE PromotionBackMargins ADD SupportTypeNumber NVARCHAR(50) NULL;
    END
END

-- 7. Create sample data for testing (optional)
-- Uncomment the following section if you want to insert sample data

/*
-- Sample Support Types
IF NOT EXISTS (SELECT * FROM PromotionBackMarginSupportTypes WHERE Number = 'THUONG_DS')
BEGIN
    INSERT INTO PromotionBackMarginSupportTypes (Number, Name, Description, CreatedOn, ModificationStatus)
    VALUES ('THUONG_DS', N'Thưởng doanh số', N'Thưởng theo doanh số đạt được', GETUTCDATE(), 1);
END

IF NOT EXISTS (SELECT * FROM PromotionBackMarginSupportTypes WHERE Number = 'HT_GIA')
BEGIN
    INSERT INTO PromotionBackMarginSupportTypes (Number, Name, Description, CreatedOn, ModificationStatus)
    VALUES ('HT_GIA', N'Hỗ trợ giá', N'Hỗ trợ giá bán cho khách hàng', GETUTCDATE(), 1);
END
*/

-- 8. Print completion message
PRINT 'Back Margin database migration completed successfully!';
PRINT 'Created tables:';
PRINT '- PromotionBackMarginTiers';
PRINT '- PromotionBackMarginTrackings';
PRINT '- PromotionBackMarginEarneds';
PRINT '- PromotionBackMarginPayments';
PRINT '';
PRINT 'Updated tables:';
PRINT '- PromotionHeaders (added AccumulateRevenue, SupportTypeNumber if missing)';
PRINT '- PromotionBackMargins (added PaymentMethod, PaymentTiming, etc. if missing)';
PRINT '';
PRINT 'Next steps:';
PRINT '1. Run Entity Framework migrations to sync models';
PRINT '2. Update your application code to use new Back Margin entities';
PRINT '3. Test the new Back Margin functionality';

-- End of migration script
