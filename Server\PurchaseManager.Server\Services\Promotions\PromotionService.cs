﻿using System.Text.RegularExpressions;
using AutoMapper;
using Microsoft.Extensions.Localization;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Server.Services.Promotions.Interface;
using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Localizer;
using static Microsoft.AspNetCore.Http.StatusCodes;
using ClosedXML.Excel;
using PurchaseManager.Constants.Enum;

namespace PurchaseManager.Server.Services.Promotions;

public partial class PromotionService : IPromotionService
{
    private readonly IPromotionManager _promotionManager;
    private readonly IStringLocalizer<Global> _i18N;
    private readonly IItemManager _itemManager;
    private readonly IMapper _mapper;
    private readonly IAdminManager _adminManager;
    public PromotionService(IPromotionManager promotionManager, IStringLocalizer<Global> i18N, IAdminManager adminManager,
        IItemManager itemManager, IMapper mapper)
    {
        _promotionManager = promotionManager;
        _i18N = i18N;
        _adminManager = adminManager;
        _itemManager = itemManager;
        _mapper = mapper;
    }

    public async Task<ApiResponse> CreatePromotionFrontMarginAsync(CreatePromotionFrontMarginDto createDto)
    {
        var validationResult = await ValidatePromotionFrontMarginAsync(createDto);
        if (!validationResult.IsSuccessStatusCode)
        {
            return validationResult;
        }

        // Get ItemName from Item table
        var itemResponse = await _itemManager.GetItem(createDto.ItemNumber);
        if (itemResponse.IsSuccessStatusCode && itemResponse.Result != null)
        {
            var itemDto = itemResponse.Result as DetailItemDto;
            createDto.ItemName = itemDto?.Name ?? "";
        }

        // Get GiftItemName if GiftItemNumber is provided
        if (string.IsNullOrWhiteSpace(createDto.GiftItemNumber))
            return await _promotionManager.CreatePromotionFrontMarginAsync(createDto);

        var giftItemResponse = await _itemManager.GetItem(createDto.GiftItemNumber);

        if (!giftItemResponse.IsSuccessStatusCode || giftItemResponse.Result == null)
            return await _promotionManager.CreatePromotionFrontMarginAsync(createDto);

        var giftItemDto = giftItemResponse.Result as DetailItemDto;
        createDto.GiftItemName = giftItemDto?.Name ?? "";

        return await _promotionManager.CreatePromotionFrontMarginAsync(createDto);
    }
    public async Task<ApiResponse> UpdatePromotionFrontMarginAsync(string number, UpdatePromotionFrontMarginDto updateDto)
    {
        var poHeader = await _promotionManager.GetPromotionFrontMarginByNumberAsync(number);
        if (!poHeader.IsSuccessStatusCode)
        {
            return poHeader;
        }

        var statusLine = await _promotionManager.IsDocumentLockedByAnotherUserFrontMarginAsync(number);
        if (!statusLine.IsSuccessStatusCode)
        {
            return statusLine;
        }

        return await _promotionManager.UpdatePromotionFrontMarginAsync(number, updateDto);
    }
    public async Task<ApiResponse> OpenDocument(string documentNumber)
    {
        try
        {
            var loginId = _adminManager.GetUserLogin();
            if (loginId is null)
            {
                return new ApiResponse(Status400BadRequest, _i18N["LoginID is null (must Login)"]);
            }
            if (string.IsNullOrEmpty(documentNumber))
            {
                return ApiResponse.S404(_i18N["DocumentNumber is null"]);
            }

            var notification = await _promotionManager.IsDocumentLockedByAnotherUserAsync(documentNumber);
            if (!notification.IsSuccessStatusCode)
            {
                return notification;
            }

            var openDocument = await _promotionManager.OpenDocumentAsync(documentNumber);
            return !openDocument.IsSuccessStatusCode
                ? openDocument
                : new ApiResponse(Status200OK, _i18N["Open PO successful"]);
        }
        catch (Exception ex)
        {
            return ApiResponse.S500(ex.GetBaseException()
                .Message);
        }
    }

    private async Task<ApiResponse> ValidatePromotionFrontMarginAsync(CreatePromotionFrontMarginDto promotionFrontMarginDto)
    {
        var poHeader = await _promotionManager.GetPromotionByNumberAsync(promotionFrontMarginDto.ProgramNumber);
        if (!poHeader.IsSuccessStatusCode)
        {
            return poHeader;
        }

        var statusLine = await _promotionManager.IsDocumentLockedByAnotherUserAsync(promotionFrontMarginDto.ProgramNumber);
        if (!statusLine.IsSuccessStatusCode)
        {
            return statusLine;
        }

        var item = await _itemManager.GetItem(promotionFrontMarginDto.ItemNumber);
        if (!item.IsSuccessStatusCode)
        {
            return item;
        }

        var itemDto = _mapper.Map<DetailItemDto>(item.Result);
        if (itemDto.Status < 2)
        {
            return new ApiResponse(Status404NotFound, "ItemNumber is editing");
        }

        if (itemDto.Blocked == 1)
        {
            return new ApiResponse(Status404NotFound, "ItemNumber is blocked");
        }

        if (itemDto.ItemUnitOfMeasures == null) return ApiResponse.S200();
        var uom = itemDto.ItemUnitOfMeasures
            .FirstOrDefault(x => string.Equals(x.Code, promotionFrontMarginDto.UnitOfMeasure, StringComparison.OrdinalIgnoreCase));

        if (uom == null)
        {
            return new ApiResponse(Status404NotFound, "UOM is not correct");
        }

        return uom.Block == 1 ? new ApiResponse(Status404NotFound, "UOM is blocked") : ApiResponse.S200();

    }

    public async Task<ApiResponse> ImportPromotionFrontMarginsAsync(string promotionNumber, IFormFile file)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return ApiResponse.BadRequest("File không được để trống");
            }

            if (!file.FileName.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase))
            {
                return ApiResponse.BadRequest("Chỉ hỗ trợ file Excel (.xlsx)");
            }

            // Performance check: File size limit (10MB)
            if (file.Length > 10 * 1024 * 1024)
            {
                return ApiResponse.BadRequest("File không được vượt quá 10MB");
            }

            var startTime = DateTime.Now;

            await using var stream = file.OpenReadStream();
            using var workbook = new XLWorkbook(stream);

            // Check for password protection
            IXLWorksheet targetWorksheet;
            try
            {
                targetWorksheet = workbook.Worksheet(1);
                if (targetWorksheet == null)
                {
                    return ApiResponse.BadRequest("File Excel không hợp lệ hoặc không có sheet nào");
                }
            }
            catch (Exception ex) when (ex.Message.Contains("password") || ex.Message.Contains("protected"))
            {
                return ApiResponse.BadRequest("File Excel được bảo vệ bằng mật khẩu. Vui lòng gỡ bỏ mật khẩu và thử lại");
            }

            // Performance check: Row count limit
            var totalRows = targetWorksheet.LastRowUsed()?.RowNumber() ?? 0;
            if (totalRows > 1000)
            {
                return ApiResponse.BadRequest($"File có {totalRows} dòng vượt quá giới hạn 1000 dòng");
            }

            var frontMargins = new List<CreatePromotionFrontMarginDto>();
            var validationErrors = new List<string>();

            // Start from row 3 (after header and description rows)
            for (var row = 3; row <= targetWorksheet.LastRowUsed().RowNumber(); row++)
            {
                try
                {
                    var itemNumber = targetWorksheet.Cell(row, 1).GetString().Trim();

                    // Skip empty rows
                    if (string.IsNullOrEmpty(itemNumber))
                        continue;

                    // Enhanced item number validation
                    if (!IsValidItemNumber(itemNumber))
                    {
                        validationErrors.Add(
                        $"Dòng {row}: ItemNumber '{itemNumber}' không hợp lệ (chỉ chấp nhận chữ, số, gạch dưới, gạch ngang)");
                        continue;
                    }

                    var unitOfMeasure = targetWorksheet.Cell(row, 2).GetString().Trim();
                    var discountType = targetWorksheet.Cell(row, 3).GetValue<int>();

                    var frontMargin = new CreatePromotionFrontMarginDto
                    {
                        ProgramNumber = promotionNumber,
                        ItemNumber = itemNumber,
                        UnitOfMeasure = unitOfMeasure,
                        DiscountType = discountType
                    };

                    // Set fields based on discount type to ensure data integrity
                    switch (frontMargin.DiscountType)
                    {
                        case (int)FrontMarginDiscountTypeTypeEnum.PercentageDiscount:// Percentage Discount
                            frontMargin.DiscountPercentage = GetDecimalSafe(targetWorksheet.Cell(row, 4)) ?? 0;
                            frontMargin.MinimumQuantity = GetDecimalSafe(targetWorksheet.Cell(row, 11));
                            frontMargin.MinimumAmount = GetDecimalSafe(targetWorksheet.Cell(row, 12));
                            frontMargin.MaximumDiscountAmount = GetDecimalSafe(targetWorksheet.Cell(row, 13));
                            frontMargin.TierQuantityThreshold = GetDecimalSafe(targetWorksheet.Cell(row, 14));
                            frontMargin.TierBonusPercentage = GetDecimalSafe(targetWorksheet.Cell(row, 15));
                            break;

                        case (int)FrontMarginDiscountTypeTypeEnum.FixedAmountDiscount:// Fixed Amount Discount
                            frontMargin.FixedDiscountAmount = GetDecimalSafe(targetWorksheet.Cell(row, 5));
                            frontMargin.MinimumQuantity = GetDecimalSafe(targetWorksheet.Cell(row, 11));
                            frontMargin.MinimumAmount = GetDecimalSafe(targetWorksheet.Cell(row, 12));
                            frontMargin.MaximumDiscountAmount = GetDecimalSafe(targetWorksheet.Cell(row, 13));
                            frontMargin.TierQuantityThreshold = GetDecimalSafe(targetWorksheet.Cell(row, 14));
                            frontMargin.TierBonusAmount = GetDecimalSafe(targetWorksheet.Cell(row, 16));
                            break;

                        case (int)FrontMarginDiscountTypeTypeEnum.SameItemGift:// Same Item Gift
                            frontMargin.BuyQuantity = GetIntSafe(targetWorksheet.Cell(row, 6));
                            frontMargin.GiftQuantity = GetIntSafe(targetWorksheet.Cell(row, 7));
                            frontMargin.GiftCalculationType = GetIntSafe(targetWorksheet.Cell(row, 17)) ?? 1;
                            break;

                        case (int)FrontMarginDiscountTypeTypeEnum.DifferentItemGift:// Different Item Gift
                            frontMargin.BuyQuantity = GetIntSafe(targetWorksheet.Cell(row, 6));
                            frontMargin.GiftQuantity = GetIntSafe(targetWorksheet.Cell(row, 7));
                            frontMargin.GiftItemNumber = targetWorksheet.Cell(row, 8).GetString().Trim();
                            frontMargin.GiftItemUOM = targetWorksheet.Cell(row, 9).GetString().Trim();
                            frontMargin.GiftItemQuantity = GetDecimalSafe(targetWorksheet.Cell(row, 10));
                            frontMargin.GiftCalculationType = GetIntSafe(targetWorksheet.Cell(row, 17)) ?? 1;
                            break;
                    }

                    // Common fields for all types
                    frontMargin.Notes = targetWorksheet.Cell(row, 18).GetString();

                    // Validate each line
                    var lineValidation = await ValidatePromotionFrontMarginLineAsync(frontMargin, row);
                    if (!lineValidation.IsSuccessStatusCode)
                    {
                        validationErrors.Add(lineValidation.Message);
                    }
                    else
                    {
                        frontMargins.Add(frontMargin);
                    }
                }
                catch (Exception ex)
                {
                    validationErrors.Add($"Dòng {row}: Lỗi đọc dữ liệu - {ex.Message}");
                }
            }

            if (validationErrors.Any())
            {
                return ApiResponse.S400("Có lỗi validation", validationErrors);
            }

            if (!frontMargins.Any())
            {
                return ApiResponse.BadRequest("Không tìm thấy dữ liệu trong file");
            }

            // Import each front margin
            var successCount = 0;
            var failedItems = new List<string>();

            foreach (var frontMargin in frontMargins)
            {
                try
                {
                    var result = await CreatePromotionFrontMarginAsync(frontMargin);
                    if (result.IsSuccessStatusCode)
                    {
                        successCount++;
                    }
                    else
                    {
                        failedItems.Add($"{frontMargin.ItemNumber}: {result.Message}");
                    }
                }
                catch (Exception ex)
                {
                    failedItems.Add($"{frontMargin.ItemNumber}: {ex.Message}");
                }
            }

            var resultMessage = $"Import hoàn thành. {successCount}/{frontMargins.Count} mục được tạo thành công.";
            var processingTime = DateTime.Now - startTime;

            return ApiResponse.S200(resultMessage, new
            {
                SuccessCount = successCount,
                TotalProcessed = frontMargins.Count,
                FailedItems = failedItems,
                ProcessingTimeSeconds = processingTime.TotalSeconds,
                PerformanceNote = processingTime.TotalSeconds > 30 ? "Processing time exceeded 30 seconds" : "Good performance"
            });
        }
        catch (Exception ex)
        {
            return ApiResponse.S500($"Lỗi khi xử lý file: {ex.Message}");
        }
    }

    private static Task<ApiResponse> ValidatePromotionFrontMarginLineAsync(CreatePromotionFrontMarginDto dto, int rowIndex)
    {
        var errors = new List<string>();

        // Basic validation
        if (string.IsNullOrWhiteSpace(dto.ItemNumber))
        {
            errors.Add($"Dòng {rowIndex}: ItemNumber không được để trống");
        }

        if (string.IsNullOrWhiteSpace(dto.UnitOfMeasure))
        {
            errors.Add($"Dòng {rowIndex}: UnitOfMeasure không được để trống");
        }

        if (dto.DiscountType is < 1 or > 4)
        {
            errors.Add($"Dòng {rowIndex}: DiscountType phải là 1, 2, 3, hoặc 4");
            return Task.FromResult(ApiResponse.BadRequest(string.Join(", ", errors)));
        }

        // Case-specific validation and ensure only relevant fields are populated
        switch (dto.DiscountType)
        {
            case (int)FrontMarginDiscountTypeTypeEnum.PercentageDiscount:// Percentage Discount
                if (dto.DiscountPercentage is <= 0 or > 100)
                {
                    errors.Add($"Dòng {rowIndex}: DiscountPercentage phải từ 0.01-100% cho loại chiết khấu phần trăm");
                }

                // Ensure other discount type fields are not populated
                if (dto.FixedDiscountAmount.HasValue)
                {
                    errors.Add($"Dòng {rowIndex}: FixedDiscountAmount không được có giá trị cho loại chiết khấu phần trăm");
                }
                if (dto.BuyQuantity.HasValue || dto.GiftQuantity.HasValue)
                {
                    errors.Add($"Dòng {rowIndex}: BuyQuantity/GiftQuantity không được có giá trị cho loại chiết khấu phần trăm");
                }
                if (!string.IsNullOrEmpty(dto.GiftItemNumber))
                {
                    errors.Add($"Dòng {rowIndex}: GiftItemNumber không được có giá trị cho loại chiết khấu phần trăm");
                }

                // Tier validation for percentage - Enhanced validation
                if (dto.TierQuantityThreshold is > 0)
                {
                    // Must have main discount first
                    if (dto.DiscountPercentage <= 0)
                    {
                        errors.Add($"Dòng {rowIndex}: Tier bonus chỉ được áp dụng khi có discount chính (DiscountPercentage > 0)");
                    }

                    if (dto.TierBonusPercentage is not > 0)
                    {
                        errors.Add($"Dòng {rowIndex}: TierBonusPercentage bắt buộc khi có TierQuantityThreshold cho chiết khấu %");
                    }

                    // Tier bonus should not exceed main discount
                    if (dto.TierBonusPercentage.HasValue && dto.TierBonusPercentage.Value > dto.DiscountPercentage)
                    {
                        errors.Add(
                        $"Dòng {rowIndex}: TierBonusPercentage ({dto.TierBonusPercentage}%) không nên vượt quá DiscountPercentage chính ({dto.DiscountPercentage}%)");
                    }

                    if (dto.TierBonusAmount.HasValue)
                    {
                        errors.Add($"Dòng {rowIndex}: TierBonusAmount không được có giá trị cho loại chiết khấu phần trăm");
                    }
                }

                // Enhanced boundary validation for percentage
                if (dto.DiscountPercentage > 100)
                {
                    errors.Add($"Dòng {rowIndex}: DiscountPercentage không được vượt quá 100%");
                }

                if (dto.TierBonusPercentage is > 100)
                {
                    errors.Add($"Dòng {rowIndex}: TierBonusPercentage không được vượt quá 100%");
                }
                break;

            case (int)FrontMarginDiscountTypeTypeEnum.FixedAmountDiscount:// Fixed Amount Discount
                if (dto.FixedDiscountAmount is null or <= 0)
                {
                    errors.Add($"Dòng {rowIndex}: FixedDiscountAmount phải lớn hơn 0 cho loại chiết khấu cố định");
                }

                // Ensure other discount type fields are not populated
                if (dto.DiscountPercentage > 0)
                {
                    errors.Add($"Dòng {rowIndex}: DiscountPercentage không được có giá trị cho loại chiết khấu cố định");
                }
                if (dto.BuyQuantity.HasValue || dto.GiftQuantity.HasValue)
                {
                    errors.Add($"Dòng {rowIndex}: BuyQuantity/GiftQuantity không được có giá trị cho loại chiết khấu cố định");
                }
                if (!string.IsNullOrEmpty(dto.GiftItemNumber))
                {
                    errors.Add($"Dòng {rowIndex}: GiftItemNumber không được có giá trị cho loại chiết khấu cố định");
                }

                // Tier validation for fixed amount - Enhanced validation
                if (dto.TierQuantityThreshold is > 0)
                {
                    // Must have main discount first
                    if (dto.FixedDiscountAmount is not > 0)
                    {
                        errors.Add($"Dòng {rowIndex}: Tier bonus chỉ được áp dụng khi có discount chính (FixedDiscountAmount > 0)");
                    }

                    if (dto.TierBonusAmount is not > 0)
                    {
                        errors.Add($"Dòng {rowIndex}: TierBonusAmount bắt buộc khi có TierQuantityThreshold cho chiết khấu cố định");
                    }

                    // Tier bonus should not exceed main discount
                    if (dto.TierBonusAmount.HasValue && dto.FixedDiscountAmount.HasValue &&
                        dto.TierBonusAmount.Value > dto.FixedDiscountAmount.Value)
                    {
                        errors.Add(
                        $"Dòng {rowIndex}: TierBonusAmount ({dto.TierBonusAmount:N0}) không nên vượt quá FixedDiscountAmount chính ({dto.FixedDiscountAmount:N0})");
                    }

                    if (dto.TierBonusPercentage.HasValue)
                    {
                        errors.Add($"Dòng {rowIndex}: TierBonusPercentage không được có giá trị cho loại chiết khấu cố định");
                    }
                }

                // Enhanced boundary validation for fixed amount
                if (dto.FixedDiscountAmount is > 10000000)// 10M VND
                {
                    errors.Add($"Dòng {rowIndex}: FixedDiscountAmount không nên vượt quá 10,000,000 VND để tránh sai sót");
                }
                break;

            case (int)FrontMarginDiscountTypeTypeEnum.SameItemGift:// Same Item Gift - Enhanced validation
                if (dto.BuyQuantity is null or <= 0)
                {
                    errors.Add($"Dòng {rowIndex}: BuyQuantity phải lớn hơn 0 cho khuyến mãi tặng hàng cùng sản phẩm");
                }
                if (dto.GiftQuantity is null or <= 0)
                {
                    errors.Add($"Dòng {rowIndex}: GiftQuantity phải lớn hơn 0 cho khuyến mãi tặng hàng cùng sản phẩm");
                }

                // Business logic validation for gift ratio
                if (dto.BuyQuantity.HasValue && dto.GiftQuantity.HasValue)
                {
                    if (dto.GiftQuantity.Value > dto.BuyQuantity.Value)
                    {
                        errors.Add(
                        $"Dòng {rowIndex}: GiftQuantity ({dto.GiftQuantity}) không nên lớn hơn BuyQuantity ({dto.BuyQuantity}) để tránh lỗ");
                    }

                    // Warn about very generous ratios
                    var giftRatio = dto.GiftQuantity.Value / dto.BuyQuantity.Value;
                    if (giftRatio > 0.5m)// More than 50% gift ratio
                    {
                        errors.Add($"Dòng {rowIndex}: Tỷ lệ tặng hàng {giftRatio:P0} có thể quá cao, cần xem xét lại");
                    }
                }

                // Ensure other discount type fields are not populated
                if (dto.DiscountPercentage > 0)
                {
                    errors.Add($"Dòng {rowIndex}: DiscountPercentage không được có giá trị cho loại tặng hàng cùng sản phẩm");
                }
                if (dto.FixedDiscountAmount.HasValue)
                {
                    errors.Add($"Dòng {rowIndex}: FixedDiscountAmount không được có giá trị cho loại tặng hàng cùng sản phẩm");
                }
                if (!string.IsNullOrEmpty(dto.GiftItemNumber))
                {
                    errors.Add($"Dòng {rowIndex}: GiftItemNumber không được có giá trị cho loại tặng hàng cùng sản phẩm");
                }
                if (dto.MinimumAmount.HasValue || dto.MinimumQuantity.HasValue || dto.MaximumDiscountAmount.HasValue)
                {
                    errors.Add($"Dòng {rowIndex}: MinimumAmount/MinimumQuantity/MaximumDiscountAmount không áp dụng cho tặng hàng");
                }
                if (dto.TierQuantityThreshold.HasValue || dto.TierBonusPercentage.HasValue || dto.TierBonusAmount.HasValue)
                {
                    errors.Add($"Dòng {rowIndex}: Tier fields không áp dụng cho tặng hàng");
                }
                break;

            case (int)FrontMarginDiscountTypeTypeEnum.DifferentItemGift:// Different Item Gift - Enhanced validation
                if (dto.BuyQuantity is null or <= 0)
                {
                    errors.Add($"Dòng {rowIndex}: BuyQuantity phải lớn hơn 0 cho khuyến mãi tặng hàng khác sản phẩm");
                }
                if (dto.GiftQuantity is null or <= 0)
                {
                    errors.Add($"Dòng {rowIndex}: GiftQuantity phải lớn hơn 0 cho khuyến mãi tặng hàng khác sản phẩm");
                }
                if (string.IsNullOrWhiteSpace(dto.GiftItemNumber))
                {
                    errors.Add($"Dòng {rowIndex}: GiftItemNumber bắt buộc cho tặng hàng khác sản phẩm");
                }
                if (string.IsNullOrWhiteSpace(dto.GiftItemUOM))
                {
                    errors.Add($"Dòng {rowIndex}: GiftItemUOM bắt buộc cho tặng hàng khác sản phẩm");
                }

                // Enhanced validation for different item gifts
                if (!string.IsNullOrWhiteSpace(dto.GiftItemNumber))
                {
                    // Validate gift item number format
                    if (dto.GiftItemNumber.Length < 3)
                    {
                        errors.Add($"Dòng {rowIndex}: GiftItemNumber '{dto.GiftItemNumber}' quá ngắn (tối thiểu 3 ký tự)");
                    }

                    // Prevent gifting the same item (should use case 3 instead)
                    if (string.Equals(dto.GiftItemNumber, dto.ItemNumber, StringComparison.OrdinalIgnoreCase))
                    {
                        errors.Add(
                        $"Dòng {rowIndex}: GiftItemNumber không thể trùng với ItemNumber. Sử dụng DiscountType=3 cho tặng cùng sản phẩm");
                    }
                }

                // Validate gift quantity vs buy quantity ratio
                if (dto.BuyQuantity.HasValue && dto.GiftQuantity.HasValue && dto.GiftItemQuantity.HasValue)
                {
                    var giftPerPurchase = dto.GiftItemQuantity.Value * dto.GiftQuantity.Value / dto.BuyQuantity.Value;
                    if (giftPerPurchase > 10)// Arbitrary business rule
                    {
                        errors.Add($"Dòng {rowIndex}: Tổng số quà tặng ({giftPerPurchase:N1}) trên mỗi lần mua có thể quá nhiều");
                    }
                }

                // Ensure other discount type fields are not populated
                if (dto.DiscountPercentage > 0)
                {
                    errors.Add($"Dòng {rowIndex}: DiscountPercentage không được có giá trị cho loại tặng hàng khác sản phẩm");
                }
                if (dto.FixedDiscountAmount.HasValue)
                {
                    errors.Add($"Dòng {rowIndex}: FixedDiscountAmount không được có giá trị cho loại tặng hàng khác sản phẩm");
                }
                if (dto.MinimumAmount.HasValue || dto.MinimumQuantity.HasValue || dto.MaximumDiscountAmount.HasValue)
                {
                    errors.Add($"Dòng {rowIndex}: MinimumAmount/MinimumQuantity/MaximumDiscountAmount không áp dụng cho tặng hàng");
                }
                if (dto.TierQuantityThreshold.HasValue || dto.TierBonusPercentage.HasValue || dto.TierBonusAmount.HasValue)
                {
                    errors.Add($"Dòng {rowIndex}: Tier fields không áp dụng cho tặng hàng");
                }
                break;
        }

        return errors.Any()
            ? Task.FromResult(ApiResponse.BadRequest(string.Join("; ", errors)))
            : Task.FromResult(ApiResponse.S200());
    }

    /// <summary>
    /// Import multiple front margins from Excel with ItemName lookup
    /// </summary>
    public async Task<ApiResponse> ImportPromotionFrontMarginsAsync(string promotionNumber,
        List<CreatePromotionFrontMarginDto> frontMargins)
    {
        try
        {
            var successCount = 0;
            var failedItems = new List<string>();
            var validationErrors = new List<string>();

            foreach (var frontMargin in frontMargins)
            {
                try
                {
                    // Validate each item
                    var validationResult = await ValidatePromotionFrontMarginAsync(frontMargin);
                    if (!validationResult.IsSuccessStatusCode)
                    {
                        validationErrors.Add($"{frontMargin.ItemNumber}: {validationResult.Message}");
                        continue;
                    }

                    // Get ItemName from Item table
                    var itemResponse = await _itemManager.GetItem(frontMargin.ItemNumber);
                    if (itemResponse.IsSuccessStatusCode && itemResponse.Result != null)
                    {
                        var itemDto = itemResponse.Result as DetailItemDto;
                        frontMargin.ItemName = itemDto?.Name ?? "";
                    }
                    else
                    {
                        failedItems.Add($"{frontMargin.ItemNumber}: Item không tồn tại trong hệ thống");
                        continue;
                    }

                    // Get GiftItemName if GiftItemNumber is provided
                    if (!string.IsNullOrWhiteSpace(frontMargin.GiftItemNumber))
                    {
                        var giftItemResponse = await _itemManager.GetItem(frontMargin.GiftItemNumber);
                        if (giftItemResponse.IsSuccessStatusCode && giftItemResponse.Result != null)
                        {
                            var giftItemDto = giftItemResponse.Result as DetailItemDto;
                            frontMargin.GiftItemName = giftItemDto?.Name ?? "";
                        }
                        else
                        {
                            failedItems.Add($"{frontMargin.ItemNumber}: Gift item {frontMargin.GiftItemNumber} không tồn tại");
                            continue;
                        }
                    }

                    // Create front margin
                    var createResult = await _promotionManager.CreatePromotionFrontMarginAsync(frontMargin);
                    if (createResult.IsSuccessStatusCode)
                    {
                        successCount++;
                    }
                    else
                    {
                        failedItems.Add($"{frontMargin.ItemNumber}: {createResult.Message}");
                    }
                }
                catch (Exception ex)
                {
                    failedItems.Add($"{frontMargin.ItemNumber}: {ex.Message}");
                }
            }

            if (validationErrors.Any())
            {
                return ApiResponse.S400("Có lỗi validation", validationErrors);
            }

            var resultMessage = $"Import hoàn thành. {successCount}/{frontMargins.Count} mục được tạo thành công.";

            return ApiResponse.S200(resultMessage, new
            {
                SuccessCount = successCount,
                TotalProcessed = frontMargins.Count,
                FailedItems = failedItems
            });
        }
        catch (Exception ex)
        {
            return ApiResponse.S500($"Lỗi khi import: {ex.Message}");
        }
    }

    /// <summary>
    /// Validates item number format (alphanumeric, underscore, hyphen only)
    /// </summary>
    private static bool IsValidItemNumber(string itemNumber)
    {
        return !string.IsNullOrWhiteSpace(itemNumber) &&
               // Allow alphanumeric, underscore, hyphen, and some special characters commonly used in item numbers
               MyRegex().IsMatch(itemNumber);
    }

    private static decimal? GetDecimalSafe(IXLCell cell)
    {
        if (cell.IsEmpty()) return null;

        if (cell.TryGetValue(out double d))// đọc được kiểu số
            return (decimal)d;

        var str = cell.GetString().Trim();
        if (decimal.TryParse(str, out var dec))// đọc string
            return dec;

        return null;// không parse được
    }

    private static int? GetIntSafe(IXLCell cell)
    {
        if (cell.IsEmpty()) return null;

        if (cell.TryGetValue(out double d))// đọc được kiểu số
            return (int)d;

        var str = cell.GetString().Trim();
        if (int.TryParse(str, out var i))// đọc string
            return i;

        return null;
    }

    [GeneratedRegex(@"^[a-zA-Z0-9_\-\.@#$]+$")]
    private static partial Regex MyRegex();
}
