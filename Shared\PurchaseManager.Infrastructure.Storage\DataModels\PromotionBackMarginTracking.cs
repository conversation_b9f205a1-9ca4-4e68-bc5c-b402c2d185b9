using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;
namespace PurchaseManager.Infrastructure.Storage.DataModels;

/// <summary>
/// Back Margin tracking entity for accumulating sales/quantity data
/// Tracks vendor performance against Back Margin programs over time
/// </summary>
[Table("PromotionBackMarginTrackings")]
public class PromotionBackMarginTracking : FullTrackingEntity
{
    /// <summary>
    /// Reference to PromotionBackMargin program
    /// </summary>
    [Required]
    [StringLength(50)]
    public string BackMarginNumber { get; set; } = string.Empty;

    /// <summary>
    /// Vendor code
    /// </summary>
    [Required]
    [StringLength(20)]
    public string VendorCode { get; set; } = string.Empty;

    /// <summary>
    /// Item number (null for program-level tracking)
    /// </summary>
    [StringLength(50)]
    public string? ItemNumber { get; set; }

    /// <summary>
    /// PO Number that contributed to this tracking
    /// </summary>
    [Required]
    [StringLength(50)]
    public string PONumber { get; set; } = string.Empty;

    /// <summary>
    /// PO Line number
    /// </summary>
    [Required]
    public int POLineNumber { get; set; }

    /// <summary>
    /// Transaction date (PO date or delivery date)
    /// </summary>
    [Required]
    public DateTime TransactionDate { get; set; }

    /// <summary>
    /// Quantity purchased
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal Quantity { get; set; }

    /// <summary>
    /// Unit cost
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal UnitCost { get; set; }

    /// <summary>
    /// Total amount (Quantity * UnitCost)
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,2)")]
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// Accumulated quantity up to this transaction
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal AccumulatedQuantity { get; set; }

    /// <summary>
    /// Accumulated amount up to this transaction
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal AccumulatedAmount { get; set; }

    /// <summary>
    /// Payment date (for early payment discount calculation)
    /// </summary>
    public DateTime? PaymentDate { get; set; }

    /// <summary>
    /// Days from delivery to payment (for early payment discount)
    /// </summary>
    public int? PaymentDays { get; set; }

    /// <summary>
    /// Whether this transaction qualifies for early payment discount
    /// </summary>
    public bool QualifiesForEarlyPayment { get; set; } = false;

    /// <summary>
    /// Processing status:
    /// 1 = Pending (chưa xử lý)
    /// 2 = Processed (đã tính Back Margin)
    /// 3 = Paid (đã trả thưởng)
    /// </summary>
    [Required]
    public int ProcessingStatus { get; set; } = 1;

    /// <summary>
    /// Notes
    /// </summary>
    [StringLength(500)]
    public string? Notes { get; set; }

    // Navigation Properties
    [ForeignKey(nameof(BackMarginNumber))]
    public virtual PromotionBackMargin BackMargin { get; set; } = null!;

    [ForeignKey(nameof(VendorCode))]
    public virtual Vendor Vendor { get; set; } = null!;
}
