﻿using System.ComponentModel.DataAnnotations;
namespace PurchaseManager.Constants.Enum;

public enum PromotionFrontMarginStatus
{
    Inactive = 1,
    Active = 2
}
public enum FrontMarginDiscountTypeTypeEnum
{
    /// <summary>
    ///     Chiết khấu theo % trên từng sản phẩm
    ///     Giá Mua = Nguyên giá × (1 - % chiết khấu)
    /// </summary>
    ///
    [Display(Name = "Chiết khấu theo phần trăm")]
    PercentageDiscount = 1,
    /// <summary>
    ///     Chiết khấu theo số tiền cố định cho đơn hàng
    ///     Giá Mua = Nguyên giá - (Tổng tiền CK / Tổng giá trị ĐH × Giá trị item)
    /// </summary>
    [Display(Name = "Chiết khấu số tiền cố định")]
    FixedAmountDiscount = 2,

    /// <summary>
    ///     Chiết khấu bằng hàng - Cùng loại (Buy X Get Y Free)
    ///     Gi<PERSON>a = Tổng giá trị theo mã hàng / Tổng số lượng theo mã hàng
    /// </summary>
    [Display(Name = "Mua hàng tặng hàng cùng loại")]
    SameItemGift = 3,

    /// <summary>
    ///     Chiết khấu bằng hàng - Khác loại (Buy X Get Different Y)
    ///     Tặng hàng khác loại (không ảnh hưởng giá mua)
    /// </summary>
    [Display(Name = "Tặng hàng khác loại")]
    DifferentItemGift = 4
}
public enum FrontMarginGiftCalculationTypeEnum
{
    /// <summary>
    ///     Lũy tiến: Số lượng quà tặng tỷ lệ với số lượng mua
    /// </summary>
    [Display(Name = "Lũy tiến")]
    Progressive = 1,

    /// <summary>
    ///     Mốc: Số lượng quà tặng cố định khi đạt ngưỡng
    /// </summary>
    [Display(Name = "Mốc")]
    Milestone = 2
}

