﻿using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using Microsoft.AspNetCore.Http;

namespace PurchaseManager.Server.Services.Promotions.Interface;

public interface IPromotionService
{
    Task<ApiResponse> CreatePromotionFrontMarginAsync(CreatePromotionFrontMarginDto createDto);
    Task<ApiResponse> UpdatePromotionFrontMarginAsync(string promotionNumber, UpdatePromotionFrontMarginDto updateDto);
    Task<ApiResponse> OpenDocument(string documentNumber);
    Task<ApiResponse> ImportPromotionFrontMarginsAsync(string promotionNumber, IFormFile file);
}
