﻿using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.StockOrder;
using PurchaseManager.Shared.Models.StockOrder;
namespace PurchaseManager.Shared.Interfaces;

public interface IStockOrderApiClient
{
    Task<ApiResponseDto> CreateMultipleStockOrder(CreateStockOrderDto stockOrders);
    Task<ApiResponseDto> UpdateStockOrderAsync(string number, UpdateStockOrderDto dto);
    Task<ApiResponseDto> SaveDraftStockOrdersAsync(string headerNumber);
    Task<ApiResponseDto<List<GetStockOrderDto>>> GetStockOrderByPoHeader(string poHeader);
    Task<ApiResponseDto<PagedResultDto<GetStockOrderDto>>> GetStockOrderByPoHeaderAsync(StockOrderFilter filter);
    Task<ApiResponseDto<List<SOLineGetDto>>> GetLinesForStockOrder(string purchaseOrderNumber);
}
