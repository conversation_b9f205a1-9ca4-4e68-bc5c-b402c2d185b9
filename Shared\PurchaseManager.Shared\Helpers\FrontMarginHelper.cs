using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
namespace PurchaseManager.Shared.Helpers;

public static class FrontMarginHelper
{
    /// <summary>
    /// Get display name for discount type
    /// </summary>
    public static string GetDiscountTypeName(int discountType)
    {
        return discountType switch
        {
            1 => "Chiết khấu theo phần trăm",
            2 => "Chiết khấu số tiền cố định",
            3 => "Mua hàng tặng hàng cùng loại",
            4 => "Tặng hàng khác loại",
            _ => "Không xác định"
        };
    }

    /// <summary>
    /// Get short display name for discount type
    /// </summary>
    public static string GetDiscountTypeShortName(int discountType)
    {
        return discountType switch
        {
            1 => "% CK",
            2 => "Số tiền CK",
            3 => "Tặng cùng loại",
            4 => "Tặng khác loại",
            _ => "N/A"
        };
    }

    /// <summary>
    /// Validate promotion front margin data based on discount type
    /// </summary>
    public static List<string> ValidatePromotionData(CreatePromotionFrontMarginDto dto)
    {
        var errors = new List<string>();

        switch (dto.DiscountType)
        {
            case 1: // Percentage Discount
                if (dto.DiscountPercentage <= 0 || dto.DiscountPercentage > 100)
                    errors.Add("Phần trăm chiết khấu phải từ 0 đến 100");
                break;

            case 2: // Fixed Amount Discount
                if (!dto.FixedDiscountAmount.HasValue || dto.FixedDiscountAmount <= 0)
                    errors.Add("Số tiền chiết khấu cố định phải lớn hơn 0");
                break;

            case 3: // Same Item Gift
                if (!dto.BuyQuantity.HasValue || dto.BuyQuantity <= 0)
                    errors.Add("Số lượng mua phải lớn hơn 0");
                if (!dto.GiftQuantity.HasValue || dto.GiftQuantity <= 0)
                    errors.Add("Số lượng tặng phải lớn hơn 0");
                break;

            case 4: // Different Item Gift
                if (string.IsNullOrEmpty(dto.GiftItemNumber))
                    errors.Add("Mã hàng tặng không được để trống");
                if (string.IsNullOrEmpty(dto.GiftItemName))
                    errors.Add("Tên hàng tặng không được để trống");
                if (string.IsNullOrEmpty(dto.GiftItemUOM))
                    errors.Add("Đơn vị tính hàng tặng không được để trống");
                if (!dto.GiftItemQuantity.HasValue || dto.GiftItemQuantity <= 0)
                    errors.Add("Số lượng hàng tặng phải lớn hơn 0");
                break;

            default:
                errors.Add("Loại chiết khấu không hợp lệ");
                break;
        }

        return errors;
    }

    /// <summary>
    /// Get required fields for each discount type
    /// </summary>
    public static List<string> GetRequiredFields(int discountType)
    {
        return discountType switch
        {
            1 => new List<string> { "DiscountPercentage" },
            2 => new List<string> { "FixedDiscountAmount" },
            3 => new List<string> { "BuyQuantity", "GiftQuantity" },
            4 => new List<string> { "GiftItemNumber", "GiftItemName", "GiftItemUOM", "GiftItemQuantity" },
            _ => new List<string>()
        };
    }

    /// <summary>
    /// Check if promotion has valid configuration for its discount type
    /// </summary>
    public static bool IsValidConfiguration(GetPromotionFrontMarginDto promotion)
    {
        return promotion.DiscountType switch
        {
            1 => promotion.DiscountPercentage > 0 && promotion.DiscountPercentage <= 100,
            2 => promotion.FixedDiscountAmount > 0,
            3 => promotion.BuyQuantity > 0 &&
                 promotion.GiftQuantity > 0,
            4 => !string.IsNullOrEmpty(promotion.GiftItemNumber) &&
                 !string.IsNullOrEmpty(promotion.GiftItemName) &&
                 !string.IsNullOrEmpty(promotion.GiftItemUOM) &&
                 promotion.GiftItemQuantity > 0,
            _ => false
        };
    }

    /// <summary>
    /// Get display text for promotion details
    /// </summary>
    public static string GetPromotionDisplayText(GetPromotionFrontMarginDto promotion)
    {
        return promotion.DiscountType switch
        {
            1 => $"Chiết khấu {promotion.DiscountPercentage:F2}%",
            2 => $"Chiết khấu {promotion.FixedDiscountAmount:N0} VNĐ",
            3 => $"Mua {promotion.BuyQuantity:F0} tặng {promotion.GiftQuantity:F0}",
            4 => $"Tặng {promotion.GiftItemQuantity:F0} {promotion.GiftItemUOM} {promotion.GiftItemName}",
            _ => "Chưa cấu hình"
        };
    }

    /// <summary>
    /// Get all available discount types
    /// </summary>
    public static Dictionary<int, string> GetDiscountTypes()
    {
        return new Dictionary<int, string>
        {
            { 1, "Chiết khấu theo phần trăm" },
            { 2, "Chiết khấu số tiền cố định" },
            { 3, "Mua hàng tặng hàng cùng loại" },
            { 4, "Tặng hàng khác loại" }
        };
    }
}
