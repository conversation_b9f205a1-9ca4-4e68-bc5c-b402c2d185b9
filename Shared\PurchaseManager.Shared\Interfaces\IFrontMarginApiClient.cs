using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
namespace PurchaseManager.Shared.Interfaces;

public interface IFrontMarginApiClient : IBaseApiClient
{
    /// <summary>
    /// Gets promotions by vendor and specific items (approve for po)
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    Task<ApiResponseDto<List<PromotionSelectedDto>>> GetPromotionsByVendorItemsAsync(PromotionSelectedRequestDto request);
}
