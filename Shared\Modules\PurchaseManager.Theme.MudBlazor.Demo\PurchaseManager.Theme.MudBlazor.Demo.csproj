﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <RootNamespace>PurchaseManager.Theme.Material.Demo</RootNamespace>
        <Title>PurchaseManager Demo UI</Title>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="ExcelDataReader.DataSet" Version="3.7.0" />
        <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="8.0.6" />
        <PackageReference Include="System.Net.Http" Version="4.3.4" />
        <PackageReference Include="System.Net.Http.Json" Version="8.0.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\PurchaseManager.Theme.MudBlazor\PurchaseManager.Theme.MudBlazor.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Content Update="Pages\Test\ThanhTestPO.razor">
        <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      </Content>
    </ItemGroup>

  <ItemGroup>
    <Compile Update="Pages\Promotions\PromotionFrontMarginPage.razor.Extension.cs">
      <DependentUpon>PromotionFrontMarginPage.razor</DependentUpon>
    </Compile>
  </ItemGroup>

    <ItemGroup>
        <UpToDateCheckInput Remove="Components\POHeaderToolbar.razor"/>
        <UpToDateCheckInput Remove="Components\POLineForm.razor"/>
        <UpToDateCheckInput Remove="Components\POLineMasterData.razor"/>
        <UpToDateCheckInput Remove="Pages\Test\PromotionApiTest.razor"/>
        <UpToDateCheckInput Remove="Pages\Test\FrontMarginSelectorDemo.razor"/>
        <UpToDateCheckInput Remove="Pages\Test\PromotionApiTest.razor"/>
        <UpToDateCheckInput Remove="Pages\Test\PromotionHistoryDialogDemo.razor"/>
        <UpToDateCheckInput Remove="Pages\Test\PromotionHistoryDialogTest.razor"/>
    </ItemGroup>

    <Target Name="PostBuild" AfterTargets="PostBuildEvent">
        <Copy SourceFiles="$(TargetPath)" DestinationFolder="$(SolutionDir)Server\PurchaseManager.Server\Themes\MudBlazor\" />
    </Target>
</Project>
