using Breeze.AspNetCore;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.Promotions;
using static Microsoft.AspNetCore.Http.StatusCodes;

namespace PurchaseManager.Server.Controllers;

/// <summary>
/// Controller for Front Margin operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class FrontMarginController : ControllerBase
{
    private readonly IFrontMarginManager _frontMarginManager;

    public FrontMarginController(IFrontMarginManager frontMarginManager)
    {
        _frontMarginManager = frontMarginManager;
    }

    [HttpPost("by-vendor-items")]
    public async Task<ApiResponse> GetPromotionsByVendorItems([FromBody] PromotionSelectedRequestDto request)
    {
        return await _frontMarginManager.GetPromotionsByVendorItemsAsync(request);
    }
}
