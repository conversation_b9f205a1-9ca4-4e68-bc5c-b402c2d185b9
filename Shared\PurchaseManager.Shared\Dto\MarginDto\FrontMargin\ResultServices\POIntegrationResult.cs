using PurchaseManager.Shared.Dto.PO;
namespace PurchaseManager.Shared.Dto.MarginDto.FrontMargin.ResultServices;

public class POIntegrationResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public POHeaderGetDto OriginalPOHeader { get; set; } = null!;
    public List<POLineGetDto> OriginalPOLines { get; set; } = [];
    public List<POLineGetDto> UpdatedPOLines { get; set; } = [];
    public List<POLineGetDto> GiftLines { get; set; } = [];
    public List<AppliedPromotionInfo> AppliedPromotions { get; set; } = [];
    public decimal TotalOriginalAmount { get; set; }
    public decimal TotalFinalAmount { get; set; }
    public decimal TotalSavings { get; set; }
    public decimal TotalDiscountPercentage { get; set; }
}
