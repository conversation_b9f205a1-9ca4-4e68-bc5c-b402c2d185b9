using PurchaseManager.Shared.Dto.PO;
namespace PurchaseManager.Shared.Dto.MarginDto.FrontMargin.ResultServices;

public class POFrontMarginResult
{
    public decimal OriginalPOAmount { get; set; }
    public decimal FinalPOAmount { get; set; }
    public decimal TotalSavings { get; set; }
    public decimal TotalDiscountPercentage { get; set; }
    public List<FrontMarginCalculationResult> LineResults { get; set; } = [];
    public List<POLineGetDto> GiftLines { get; set; } = [];
}
