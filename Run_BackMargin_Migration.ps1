# =============================================
# Back Margin Database Migration Runner
# Created: 2025-01-14
# Description: PowerShell script to run Back Margin migration
# =============================================

param(
    [Parameter(Mandatory=$true)]
    [string]$ConnectionString,
    
    [Parameter(Mandatory=$false)]
    [string]$SqlFilePath = "Database_Migration_BackMargin.sql",
    
    [Parameter(Mandatory=$false)]
    [switch]$WhatIf = $false
)

# Function to write colored output
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

# Function to execute SQL script
function Execute-SqlScript {
    param(
        [string]$ConnectionString,
        [string]$SqlFilePath,
        [bool]$WhatIfMode = $false
    )
    
    try {
        # Check if SQL file exists
        if (-not (Test-Path $SqlFilePath)) {
            Write-ColorOutput Red "Error: SQL file not found at path: $SqlFilePath"
            return $false
        }
        
        # Read SQL content
        $sqlContent = Get-Content $SqlFilePath -Raw
        
        if ($WhatIfMode) {
            Write-ColorOutput Yellow "=== WHAT-IF MODE: SQL Script Content ==="
            Write-Output $sqlContent
            Write-ColorOutput Yellow "=== END OF SQL SCRIPT ==="
            return $true
        }
        
        # Import SqlServer module if available
        if (Get-Module -ListAvailable -Name SqlServer) {
            Import-Module SqlServer -Force
            
            Write-ColorOutput Green "Executing SQL script using SqlServer module..."
            Invoke-Sqlcmd -ConnectionString $ConnectionString -Query $sqlContent -Verbose
            
        } elseif (Get-Module -ListAvailable -Name SQLPS) {
            Import-Module SQLPS -Force
            
            Write-ColorOutput Green "Executing SQL script using SQLPS module..."
            Invoke-Sqlcmd -ConnectionString $ConnectionString -Query $sqlContent -Verbose
            
        } else {
            # Fallback to .NET SqlConnection
            Write-ColorOutput Yellow "SQL modules not found. Using .NET SqlConnection..."
            
            Add-Type -AssemblyName System.Data
            $connection = New-Object System.Data.SqlClient.SqlConnection($ConnectionString)
            $command = New-Object System.Data.SqlClient.SqlCommand($sqlContent, $connection)
            $command.CommandTimeout = 300 # 5 minutes timeout
            
            $connection.Open()
            $result = $command.ExecuteNonQuery()
            $connection.Close()
            
            Write-ColorOutput Green "SQL script executed successfully. Rows affected: $result"
        }
        
        return $true
        
    } catch {
        Write-ColorOutput Red "Error executing SQL script: $($_.Exception.Message)"
        Write-ColorOutput Red "Stack trace: $($_.Exception.StackTrace)"
        return $false
    }
}

# Main execution
Write-ColorOutput Cyan "=== Back Margin Database Migration Runner ==="
Write-ColorOutput White "Connection String: $($ConnectionString.Substring(0, [Math]::Min(50, $ConnectionString.Length)))..."
Write-ColorOutput White "SQL File Path: $SqlFilePath"
Write-ColorOutput White "What-If Mode: $WhatIf"
Write-ColorOutput White ""

if ($WhatIf) {
    Write-ColorOutput Yellow "Running in WHAT-IF mode - no changes will be made to the database"
}

# Validate connection string
if ([string]::IsNullOrWhiteSpace($ConnectionString)) {
    Write-ColorOutput Red "Error: Connection string cannot be empty"
    exit 1
}

# Execute the migration
$success = Execute-SqlScript -ConnectionString $ConnectionString -SqlFilePath $SqlFilePath -WhatIfMode $WhatIf

if ($success) {
    if ($WhatIf) {
        Write-ColorOutput Green "What-If execution completed successfully"
    } else {
        Write-ColorOutput Green "Back Margin migration completed successfully!"
        Write-ColorOutput White ""
        Write-ColorOutput White "Next steps:"
        Write-ColorOutput White "1. Verify the new tables were created in your database"
        Write-ColorOutput White "2. Run Entity Framework migrations: dotnet ef migrations add BackMarginTables"
        Write-ColorOutput White "3. Update your application code to use the new Back Margin entities"
        Write-ColorOutput White "4. Test the Back Margin functionality"
    }
} else {
    Write-ColorOutput Red "Migration failed. Please check the error messages above."
    exit 1
}

# Example usage instructions
Write-ColorOutput Cyan ""
Write-ColorOutput Cyan "=== Usage Examples ==="
Write-ColorOutput White "# Run migration:"
Write-ColorOutput Gray '.\Run_BackMargin_Migration.ps1 -ConnectionString "Server=localhost;Database=PurchaseManager;Trusted_Connection=true;"'
Write-ColorOutput White ""
Write-ColorOutput White "# Preview what will be executed (What-If mode):"
Write-ColorOutput Gray '.\Run_BackMargin_Migration.ps1 -ConnectionString "Server=localhost;Database=PurchaseManager;Trusted_Connection=true;" -WhatIf'
Write-ColorOutput White ""
Write-ColorOutput White "# Use custom SQL file path:"
Write-ColorOutput Gray '.\Run_BackMargin_Migration.ps1 -ConnectionString "..." -SqlFilePath "C:\path\to\custom_migration.sql"'
