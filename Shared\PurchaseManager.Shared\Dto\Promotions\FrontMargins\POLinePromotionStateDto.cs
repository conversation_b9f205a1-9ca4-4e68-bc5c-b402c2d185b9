namespace PurchaseManager.Shared.Dto.Promotions.FrontMargins;

/// <summary>
/// DTO for PO Line promotion state in Edit dialog
/// Combines applied promotions with available options
/// </summary>
public class POLinePromotionStateDto
{
    /// <summary>
    /// PO Line information
    /// </summary>
    public string PONumber { get; set; } = string.Empty;
    public int LineNumber { get; set; }
    public string ItemNumber { get; set; } = string.Empty;
    public string ItemName { get; set; } = string.Empty;
    public string UnitOfMeasure { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public decimal UnitCost { get; set; }

    /// <summary>
    /// Applied promotions from PromotionFrontMarginUsages table
    /// </summary>
    public List<AppliedPromotionDto> AppliedPromotions { get; set; } = [];

    /// <summary>
    /// Available promotions that can be applied (not currently applied)
    /// </summary>
    public List<GetPromotionFrontMarginDto> AvailablePromotions { get; set; } = [];

    /// <summary>
    /// Summary information
    /// </summary>
    public bool HasAppliedPromotions => AppliedPromotions.Count > 0;
    public bool HasAvailablePromotions => AvailablePromotions.Count > 0;
    public decimal TotalSavings => AppliedPromotions.Sum(p => p.SavingsAmount);
    public string StatusMessage => GetStatusMessage();

    private string GetStatusMessage()
    {
        if (HasAppliedPromotions && HasAvailablePromotions)
        {
            return $"Đã áp dụng {AppliedPromotions.Count} CTKM - Còn {AvailablePromotions.Count} CTKM khả dụng khác";
        }
        else if (HasAppliedPromotions)
        {
            return $"Đã áp dụng {AppliedPromotions.Count} CTKM - Tiết kiệm {TotalSavings:N0} VND";
        }
        else if (HasAvailablePromotions)
        {
            return $"Có {AvailablePromotions.Count} CTKM khả dụng chưa được áp dụng";
        }
        else
        {
            return "Không có CTKM nào khả dụng cho sản phẩm này";
        }
    }
}

/// <summary>
/// Applied promotion information from PromotionFrontMarginUsages
/// </summary>
public class AppliedPromotionDto
{
    public long UsageId { get; set; }
    public string PromotionNumber { get; set; } = string.Empty;
    public string ProgramName { get; set; } = string.Empty;
    public int DiscountType { get; set; }
    public string DiscountTypeName { get; set; } = string.Empty;
    
    // Original promotion details
    public decimal DiscountPercentage { get; set; }
    public decimal FixedDiscountAmount { get; set; }
    public decimal BuyQuantity { get; set; }
    public decimal GiftQuantity { get; set; }
    public string? GiftItemNumber { get; set; }
    public string? GiftItemName { get; set; }
    public decimal GiftItemQuantity { get; set; }
    
    // Applied results
    public decimal OriginalQuantity { get; set; }
    public decimal FinalQuantity { get; set; }
    public decimal OriginalUnitCost { get; set; }
    public decimal FinalUnitCost { get; set; }
    public decimal OriginalLineAmount { get; set; }
    public decimal FinalLineAmount { get; set; }
    public decimal SavingsAmount { get; set; }
    public decimal SavingsPercentage { get; set; }
    
    // Status and metadata
    public int Status { get; set; } // 1=Active, 2=Cancelled, 3=Modified
    public string StatusName { get; set; } = string.Empty;
    public DateTime AppliedDate { get; set; }
    public string AppliedBy { get; set; } = string.Empty;
    public string? Notes { get; set; }
    
    // Display helpers
    public string DiscountSummary => GetDiscountSummary();
    public bool CanModify => Status == 1; // Only active promotions can be modified
    public bool IsActive => Status == 1;
    
    private string GetDiscountSummary()
    {
        return DiscountType switch
        {
            1 => DiscountPercentage > 0 ? $"Giảm {DiscountPercentage}%" : $"Giảm {FixedDiscountAmount:N0} VND",
            2 => $"Mua {BuyQuantity} tặng {GiftQuantity}",
            3 => $"Tặng {GiftItemQuantity} {GiftItemName}",
            4 => $"Tặng {GiftItemQuantity} {GiftItemName}",
            _ => $"Tiết kiệm {SavingsAmount:N0} VND"
        };
    }
}
