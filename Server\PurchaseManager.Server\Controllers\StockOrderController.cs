﻿using Microsoft.AspNetCore.Mvc;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.StockOrder;
using PurchaseManager.Shared.Models.StockOrder;
namespace PurchaseManager.Server.Controllers;

[SecurityHeaders]
[Route("api/[controller]")]
[ApiController]
public class StockOrderController : ControllerBase
{
    private readonly IStockOrderManager _stockOrderManager;

    public StockOrderController(IStockOrderManager stockOrderManager)
    {
        _stockOrderManager = stockOrderManager;
    }

    /// <summary>
    /// Get PO lines with conversion rate display for Stock Order context
    /// </summary>
    /// <param name="number">Purchase Order number</param>
    /// <returns>List of PO lines with conversion rate information</returns>
    [HttpGet("stock-order-lines/{number}")]
    public async Task<ApiResponse> GetLinesForStockOrder(string number)
    {
        return await _stockOrderManager.GetLinesStockOrderAsync(number);
    }

    [HttpGet("Gets")]
    public async Task<ApiResponse> GetStockOrderByPoHeader([FromQuery] StockOrderFilter filter)
        => await _stockOrderManager.GetReceiveLotsByPoHeaderAsync(filter);

    [HttpPost("Create")]
    public async Task<ApiResponse> CreateStockOrder([FromBody] CreateStockOrderDto stockOrder)
        => await _stockOrderManager.CreateReceiveLotsAsync(stockOrder);
    [HttpPut("update/{number}")]
    public async Task<ApiResponse> UpdateStockOrder(string number, [FromBody] UpdateStockOrderDto stockOrder)
        => await _stockOrderManager.UpdateReceiveLotsAsync(number, stockOrder);
    [HttpPut("save-drafts/{headerNumber}")]
    public async Task<ApiResponse> SaveDraftStockOrders(string headerNumber)
        => await _stockOrderManager.SaveDraftStockOrdersAsync(headerNumber);
}
