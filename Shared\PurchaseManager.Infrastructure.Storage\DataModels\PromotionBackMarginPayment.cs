using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;
namespace PurchaseManager.Infrastructure.Storage.DataModels;

/// <summary>
/// Back Margin payment tracking entity
/// Records actual payments made for earned Back Margin rewards
/// </summary>
[Table("PromotionBackMarginPayments")]
public class PromotionBackMarginPayment : FullTrackingEntity
{
    /// <summary>
    /// Reference to PromotionBackMarginEarned
    /// </summary>
    [Required]
    [StringLength(50)]
    public string EarnedBackMarginNumber { get; set; } = string.Empty;

    /// <summary>
    /// PO Number where the payment was applied (for NextOrder payment timing)
    /// </summary>
    [StringLength(50)]
    public string? PONumber { get; set; }

    /// <summary>
    /// Payment date
    /// </summary>
    [Required]
    public DateTime PaymentDate { get; set; }

    /// <summary>
    /// Payment amount
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,2)")]
    public decimal PaymentAmount { get; set; }

    /// <summary>
    /// Payment method used:
    /// 1 = Cash (Tiền mặt)
    /// 2 = Transfer (Chuyển khoản)
    /// 3 = DebtOffset (Trừ công nợ)
    /// 4 = Goods (Hàng)
    /// </summary>
    [Required]
    public int PaymentMethod { get; set; }

    /// <summary>
    /// Payment reference (transaction ID, invoice number, etc.)
    /// </summary>
    [StringLength(100)]
    public string? PaymentReference { get; set; }

    /// <summary>
    /// Bank account or payment details
    /// </summary>
    [StringLength(200)]
    public string? PaymentDetails { get; set; }

    /// <summary>
    /// Payment status:
    /// 1 = Pending (chờ xử lý)
    /// 2 = Completed (đã hoàn thành)
    /// 3 = Failed (thất bại)
    /// 4 = Cancelled (hủy)
    /// </summary>
    [Required]
    public int PaymentStatus { get; set; } = 1;

    /// <summary>
    /// User who processed the payment
    /// </summary>
    [StringLength(100)]
    public string? ProcessedBy { get; set; }

    /// <summary>
    /// Date when payment was processed
    /// </summary>
    public DateTime? ProcessedDate { get; set; }

    /// <summary>
    /// Approval status:
    /// 1 = Pending (chờ duyệt)
    /// 2 = Approved (đã duyệt)
    /// 3 = Rejected (từ chối)
    /// </summary>
    [Required]
    public int ApprovalStatus { get; set; } = 1;

    /// <summary>
    /// User who approved the payment
    /// </summary>
    [StringLength(100)]
    public string? ApprovedBy { get; set; }

    /// <summary>
    /// Date when payment was approved
    /// </summary>
    public DateTime? ApprovedDate { get; set; }

    /// <summary>
    /// Notes
    /// </summary>
    [StringLength(500)]
    public string? Notes { get; set; }

    // Navigation Properties
    [ForeignKey(nameof(EarnedBackMarginNumber))]
    public virtual PromotionBackMarginEarned EarnedBackMargin { get; set; } = null!;
}
