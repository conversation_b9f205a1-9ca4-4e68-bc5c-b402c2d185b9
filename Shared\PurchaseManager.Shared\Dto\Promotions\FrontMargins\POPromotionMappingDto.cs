namespace PurchaseManager.Shared.Dto.Promotions.FrontMargins;

/// <summary>
/// DTO for PO promotion mapping - Compare applied vs available promotions
/// </summary>
public class POPromotionMappingDto
{
    public string PONumber { get; set; } = "";
    public List<POLinePromotionMappingDto> Lines { get; set; } = [];
    public int TotalLines { get; set; }
    public int LinesWithPromotions { get; set; }
    public int TotalAppliedPromotions { get; set; }
    public string Summary
        => $"PO {PONumber}: {LinesWithPromotions}/{TotalLines} lines có CTKM ({TotalAppliedPromotions} CTKM đã áp dụng)";
}
/// <summary>
/// DTO for individual PO line promotion mapping
/// </summary>
public class POLinePromotionMappingDto
{
    // PO Line info
    public int LineNumber { get; set; }
    public string ItemNumber { get; set; } = "";
    public string ItemName { get; set; } = "";
    public string VendorCode { get; set; } = "";

    // Applied promotions from Usage table
    public List<AppliedPromotionMappingDto> AppliedPromotions { get; set; } = [];

    // Available promotions for this item+vendor
    public List<AvailablePromotionMappingDto> AvailablePromotions { get; set; } = [];

    // Mapping results
    public List<PromotionMappingResultDto> MappingResults { get; set; } = [];

    // Summary
    public bool HasAppliedPromotions => AppliedPromotions.Count > 0;
    public bool HasAvailablePromotions => AvailablePromotions.Count > 0;
    public bool HasMappingConflicts => MappingResults.Any(m => m.MappingType == "CONFLICT");
    public string StatusSummary => GetStatusSummary();

    private string GetStatusSummary()
    {
        if (!HasAppliedPromotions) return "Chưa có CTKM";
        if (HasMappingConflicts) return $"Có {AppliedPromotions.Count} CTKM - Cần xem lại";
        return $"Có {AppliedPromotions.Count} CTKM - OK";
    }
}
/// <summary>
/// Applied promotion from Usage table
/// </summary>
public class AppliedPromotionMappingDto
{
    public long UsageId { get; set; }
    public string Number { get; set; }
    public string PromotionNumber { get; set; }
    public string ProgramName { get; set; }
    public int DiscountType { get; set; }
    public int Status { get; set; }// 1=Active, 2=Cancelled
    public DateTime AppliedDate { get; set; }
    public string AppliedBy { get; set; } = "";

    // Item details
    public string ItemNumber { get; set; } = "";
    public string ItemName { get; set; } = "";
    public string VendorCode { get; set; } = "";

    // Promotion details
    public decimal DiscountPercentage { get; set; }
    public decimal FixedDiscountAmount { get; set; }
    public decimal BuyQuantity { get; set; }
    public decimal GiftQuantity { get; set; }
    public string? GiftItemNumber { get; set; }

    public bool IsActive => Status == 1;
    public string DiscountSummary => GetDiscountSummary();

    private string GetDiscountSummary()
    {
        if (DiscountPercentage > 0) return $"Giảm {DiscountPercentage}%";
        if (FixedDiscountAmount > 0) return $"Giảm {FixedDiscountAmount:N0}đ";
        if (BuyQuantity <= 0 || GiftQuantity <= 0) return "N/A";
        return !string.IsNullOrEmpty(GiftItemNumber) ? $"Mua {BuyQuantity} tặng {GiftQuantity} {GiftItemNumber}"
            : $"Mua {BuyQuantity} tặng {GiftQuantity}";
    }
}
/// <summary>
/// Available promotion for item+vendor
/// </summary>
public class AvailablePromotionMappingDto
{
    public string Number { get; set; } = "";
    public string ProgramName { get; set; } = "";
    public int DiscountType { get; set; }
    public int Status { get; set; }// 2=Active

    // Promotion details
    public decimal DiscountPercentage { get; set; }
    public decimal FixedDiscountAmount { get; set; }
    public decimal BuyQuantity { get; set; }
    public decimal GiftQuantity { get; set; }
    public string? GiftItemNumber { get; set; }

    public bool IsActive => Status == 2;
    public string DiscountSummary => GetDiscountSummary();

    private string GetDiscountSummary()
    {
        if (DiscountPercentage > 0) return $"Giảm {DiscountPercentage}%";
        if (FixedDiscountAmount > 0) return $"Giảm {FixedDiscountAmount:N0}đ";
        if (BuyQuantity <= 0 || GiftQuantity <= 0) return "N/A";
        return !string.IsNullOrEmpty(GiftItemNumber) ? $"Mua {BuyQuantity} tặng {GiftQuantity} {GiftItemNumber}"
            : $"Mua {BuyQuantity} tặng {GiftQuantity}";
    }
}
/// <summary>
/// Mapping result between applied and available promotions
/// </summary>
public class PromotionMappingResultDto
{
    public int DiscountType { get; set; }
    public string MappingType { get; set; } = "";// "MATCH", "CONFLICT", "MISSING", "EXTRA"
    public string Description { get; set; } = "";

    // Applied promotion (if exists)
    public AppliedPromotionMappingDto? AppliedPromotion { get; set; }

    // Available promotions for this case
    public List<AvailablePromotionMappingDto> AvailableOptions { get; set; } = [];

    // Recommendation
    public bool CanChange { get; set; } = true;
    public bool ShouldKeep { get; set; } = true;
    public string Recommendation { get; set; } = "";

    public string GetMappingIcon()
    {
        return MappingType switch
        {
            "MATCH" => "✅",
            "CONFLICT" => "⚠️",
            "MISSING" => "❌",
            "EXTRA" => "➕",
            _ => "❓"
        };
    }
}
