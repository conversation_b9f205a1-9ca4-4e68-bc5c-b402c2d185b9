@page "/promotional/front-margin/{PromotionNumber}"
@using PurchaseManager.Constants.Enum
@using PurchaseManager.Shared.Dto.Item
@using PurchaseManager.Shared.Dto.Promotions
@using PurchaseManager.Shared.Dto.Promotions.FrontMargins
@using PurchaseManager.Theme.Material.Demo.Shared.Components.Promotions
@inherits FrontMarginPage;
@if (IsLoad)
{
    <LoadingBackground>
        <label>@L["Loading"]</label>
    </LoadingBackground>
}
else
{
    <CascadingAuthenticationState>
        <PromotionToolBar @ref="ToolBarRef"
                          Title="@(L["Promotion FrontMargin"] + " - " + PromotionHeader.ProgramCode)"
                          IsEdit="@IsEdit"
                          EmailButtonDisabled="@(GetCurrentStatus() != 2)"
                          PrintButtonDisabled="@(PromotionHeader.FrontMargins?.Count == 0)"
                          ExportButtonDisabled="@(PromotionHeader.FrontMargins?.Count == 0)"
                          ExportTooltipText="Export Front Margin Detail To Excel"
                          ShowEmailButton="true"
                          ShowPrintButton="true"
                          ShowExportButton="true"
                          OnSaveOrEdit="@SaveOrEditPromotion"
                          OnStatusChanged="@UpdatePromotionStatus"
                          OnEmail="@SendEmail"
                          OnPrint="@PrintPromotion"
                          OnExport="@ExportPromotion"/>
        <MudForm Spacing="4">
            <MudGrid Class="mb-2">
                <MudItem xs="4">
                    <MudStack Spacing="2">
                        <MudTextField Variant="Variant.Outlined" Adornment="Adornment.None"
                                      ReadOnly="@(!IsEdit)"
                                      Label="@L["ProgramName"]"
                                      Placeholder="Tên chương trình khuyến mãi"
                                      @bind-Value="@PromotionHeader.ProgramName">
                        </MudTextField>
                        <MudTextField Variant="Variant.Outlined" T="String" Label="@L["Vendor"]"
                                      Value="@($"{PromotionHeader.VendorCode} - {PromotionHeader.VendorName}")"
                                      ReadOnly/>
                    </MudStack>
                </MudItem>
                <MudItem xs="4">
                    <MudStack Spacing="2">
                        <MudSelect Variant="Variant.Outlined" T="int" Label="Trạng thái"
                                   Value="@PromotionHeader.Status"
                                   ValueChanged="@(async (int value) => await OnStatusChanged(value))"
                                   ReadOnly="@(!IsEdit)">
                            <MudSelectItem Value="1">
                                <div style="display: flex; align-items: center;">
                                    <MudIcon Icon="@Icons.Material.Filled.Edit" Color="Color.Info" Size="Size.Small" Style="margin-right: 8px;"/>
                                    Draft
                                </div>
                            </MudSelectItem>
                            <MudSelectItem Value="2">
                                <div style="display: flex; align-items: center;">
                                    <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Color="Color.Success" Size="Size.Small" Style="margin-right: 8px;"/>
                                    Active
                                </div>
                            </MudSelectItem>
                            <MudSelectItem Value="3">
                                <div style="display: flex; align-items: center;">
                                    <MudIcon Icon="@Icons.Material.Filled.Pause" Color="Color.Warning" Size="Size.Small" Style="margin-right: 8px;"/>
                                    Inactive
                                </div>
                            </MudSelectItem>
                            <MudSelectItem Value="4">
                                <div style="display: flex; align-items: center;">
                                    <MudIcon Icon="@Icons.Material.Filled.Schedule" Color="Color.Error" Size="Size.Small" Style="margin-right: 8px;"/>
                                    Expired
                                </div>
                            </MudSelectItem>
                        </MudSelect>
                        <MudStack Spacing="2" Row>
                            <MudDatePicker ReadOnly="@(!IsEdit)" Label="@L[nameof(GetPromotionHeaderDto.StartDate)]"
                                           MinDate="DateTime.Today"
                                           Variant="Variant.Outlined" Editable="true"
                                           Date="@PromotionHeader.StartDate" DateChanged="@OnStartDateChange"/>
                            <MudDatePicker ReadOnly="@(!IsEdit)" Label="@L[nameof(GetPromotionHeaderDto.EndDate)]" Editable="true"
                                           MinDate="PromotionHeader.StartDate.AddDays(1)" Variant="Variant.Outlined"
                                           Date="@PromotionHeader.EndDate" DateChanged="@OnEndDateChange"/>
                        </MudStack>
                    </MudStack>
                </MudItem>
                <MudItem xs="4">
                    <MudStack Spacing="2">
                        <MudTextField Lines="5" Variant="Variant.Outlined" Adornment="Adornment.None"
                                      Label="@L["Description"]"
                                      ReadOnly="@(!IsEdit)"
                                      Placeholder="Mô tả chương trình khuyến mãi"
                                      @bind-Value="@PromotionHeader.Description">
                        </MudTextField>
                    </MudStack>
                </MudItem>
            </MudGrid>
        </MudForm>
        <br>
        @if (IsEdit)
        {
            <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.FlexStart">
                @if (SelectedFrontMargins is { Count: > 0 })
                    {
                    <MudBadge Content="@SelectedFrontMargins.Count" Color="Color.Error"
                                  Overlap="true">
                            <MudIconButton Icon="@Icons.Material.Filled.Delete" Size="Size.Small"
                                           OnClick="DeletePromotionFrontMarginsAsync"
                                           Variant="Variant.Outlined"
                                           Disabled="@(!IsEdit)"
                                           Color="Color.Error">
                            </MudIconButton>
                        </MudBadge>
                    }
                    @if (IsEdit)
                    {
                    <MudButton Size="Size.Small" StartIcon="@Icons.Material.Filled.Add"
                               OnClick="AddCondition" Disabled="@(!IsEdit)"
                               Variant="Variant.Outlined"
                               Color="Color.Success">
                        Thêm
                    </MudButton>
                    <MudTooltip Placement="Placement.Bottom" Text="Import Promotion từ Excel">
                        <MudFileUpload T="IBrowserFile" MaximumFileCount="1" FilesChanged="UploadPromotionFromFiles" Accept=".xlsx">
                                <ActivatorContent>
                                    <MudIconButton Icon="@Icons.Custom.FileFormats.FileExcel" Color="Color.Success" Size="Size.Medium">
                                    </MudIconButton>
                                </ActivatorContent>
                            </MudFileUpload>
                        </MudTooltip>
                        <MudSpacer/>
                    <MudTooltip Placement="Placement.Bottom" Text="Download sample file for import Promotion">
                        <MudLink Underline="Underline.Always" Href="/files/Import_Promotion_SampleData.xlsx">
                            <MudIconButton Icon="@Icons.Material.Outlined.Download" Color="Color.Success" Size="Size.Medium">
                                </MudIconButton>
                            </MudLink>
                        </MudTooltip>
                    }
                </MudStack>
        }
        <MudTable Dense Items="@PromotionHeader.FrontMargins" T="GetPromotionFrontMarginDto" Height="550px"
                  FixedHeader FixedFooter
                  CustomFooter
                  HorizontalScrollbar
                  SelectOnRowClick="false"
                  @bind-SelectedItems="SelectedFrontMargins"
                  OnRowClick="@(async row => await OnEditPOLine(row.Item))"
                  ReadOnly="@(!IsEdit)" MultiSelection="@IsEdit">
            <HeaderContent>
                <MudTh>@L["ItemName"]</MudTh>
                <MudTh Style="cursor: pointer; width: 100px">@L["Status"]</MudTh>
                <MudTh>@L["Unit"]</MudTh>
                <MudTh Style="text-align: center">Loại</MudTh>
                <MudTh Style="text-align: center">Giá trị</MudTh>
                <MudTh Style="text-align: center">Loại tính KM</MudTh>
                <MudTh Style="text-align: center">Bổ sung</MudTh>
                <MudTh>@L["Notes"]</MudTh>
            </HeaderContent>
            <RowTemplate Context="poLine">
                <MudTd>
                    <MudText Typo="Typo.body1">
                        @poLine.ItemName
                    </MudText>
                    <MudText Typo="Typo.caption">
                        <b>Number:</b>
                        @poLine.ItemNumber
                    </MudText>
                </MudTd>
                <MudTd>
                    <MudText Style="max-width:200px; overflow: hidden;">
                        @switch (poLine.Status)
                        {
                            case (int)PromotionFrontMarginStatus.Active:
                                <MudChip Style="width: 160px" T="string" Icon="@Icons.Material.Filled.CheckCircle"
                                         Color="Color.Success">@L["Active"]
                                </MudChip>
                                break;
                            case (int)PromotionFrontMarginStatus.Inactive:
                                <MudChip Style="width: 103px" T="string" Icon="@Icons.Material.Filled.Block"
                                         Color="Color.Warning">@L["Inactive"]
                                </MudChip>
                                break;
                        }
                    </MudText>
                </MudTd>
                <MudTd Style="text-align: center">@poLine.UnitOfMeasure.ToUpper()</MudTd>
                <MudTd Style="text-align: center">
                    @switch (poLine.DiscountType)
                    {
                        case 1:
                            <MudChip T="string" Size="Size.Small" Color="Color.Primary">
                                Chiết khấu %
                            </MudChip>
                            break;
                        case 2:
                            <MudChip T="string" Size="Size.Small" Color="Color.Default">
                                Chiết khấu VND
                            </MudChip>
                            break;
                        case 3:
                            <MudChip T="string" Size="Size.Small" Color="Color.Info">
                                Tặng cùng SP
                            </MudChip>
                            break;
                        case 4:
                            <MudChip T="string" Size="Size.Small" Color="Color.Success">
                                Tặng SP khác
                            </MudChip>
                            break;
                    }
                </MudTd>
                <MudTd Style="text-align: center">
                    @switch (poLine.DiscountType)
                    {
                        case 1:
                            @:@poLine.DiscountPercentage.ToString("N2")%
                            break;
                        case 2:
                            @:@poLine.FixedDiscountAmount.ToString("N0") VND
                            break;
                        case 3:
                        case 4:
                            <MudText Typo="Typo.body2">
                                Mua <strong>@poLine.BuyQuantity.ToString("N0")</strong>
                            </MudText>
                            <MudText Typo="Typo.caption" Color="Color.Success">
                                Tặng <strong>@poLine.GiftQuantity.ToString("N0")</strong>
                            </MudText>
                            break;
                        default:
                            @poLine.DiscountSummary
                            break;
                    }
                </MudTd>
                <MudTd Style="text-align: center">
                    @switch (poLine.GiftCalculationType)
                    {
                        case 1:
                            <MudChip T="string" Size="Size.Small" Color="Color.Info">
                                Luỹ tiến
                            </MudChip>
                            break;
                        case 2:
                            <MudChip T="string" Size="Size.Small" Color="Color.Success">
                                Mốc
                            </MudChip>
                            break;
                        default:
                            <MudChip T="string" Size="Size.Small" Color="Color.Default" Variant="Variant.Text">
                                -
                            </MudChip>
                            break;
                    }
                </MudTd>
                <MudTd Style="text-align: center">
                    @switch (poLine.DiscountType)
                    {
                        case 1:
                        case 2:
                        {
                            if (poLine.TierQuantityThreshold is > 0)
                            {
                                switch (poLine.DiscountType)
                                {
                                    case 1 when poLine.TierBonusPercentage.HasValue:
                                        <MudChip T="string" Size="Size.Small" Color="Color.Success">
                                            +@poLine.TierBonusPercentage.Value.ToString("N1")% ≥@(poLine.TierQuantityThreshold.Value.ToString("N0"))
                                        </MudChip>
                                        break;
                                    case 2 when poLine.TierBonusAmount.HasValue:
                                        <MudChip T="string" Size="Size.Small" Color="Color.Success">
                                            +@poLine.TierBonusAmount.Value.ToString("N0") ≥@(poLine.TierQuantityThreshold.Value.ToString("N0"))
                                        </MudChip>
                                        break;
                                }
                            }
                            else
                            {
                                <MudChip T="string" Size="Size.Small" Color="Color.Default" Variant="Variant.Text">
                                    No tier
                                </MudChip>
                            }
                            break;
                        }
                        case 4:
                            <MudText Typo="Typo.body2">
                                @poLine.GiftItemName
                            </MudText>
                            <MudText Typo="Typo.caption">
                                @poLine.GiftItemNumber - @poLine.GiftItemUOM?.ToUpper()
                            </MudText>
                            break;
                        case 3:
                            <MudChip T="string" Size="Size.Small" Color="Color.Default" Variant="Variant.Text">
                                Cùng sản phẩm
                            </MudChip>
                            break;
                    }
                </MudTd>
                <MudTd>@poLine.Notes</MudTd>
            </RowTemplate>
            <NoRecordsContent>
                No Data
            </NoRecordsContent>
        </MudTable>
    </CascadingAuthenticationState>
}

<MudDialog Visible="@(IsShowEditFrontMarginDialog || IsShowAddFrontMarginDialog)"
           Options="new DialogOptions { FullWidth = true, MaxWidth = MaxWidth.Medium, CloseOnEscapeKey= false, BackdropClick= false }"
           TitleClass="mud-secondary" ContentStyle="min-height:400px">
    <TitleContent>
        @if (IsShowEditFrontMarginDialog)
        {
            <div>
                @L["Edit line"] -
                <MudText Inline Typo="Typo.subtitle2"
                         Color="Color.Primary">@CurrentLine.ItemNumber - @CurrentLine.ItemName</MudText>
                Unit:
                <MudText Inline Typo="Typo.subtitle2" Color="Color.Primary">@CurrentLine.UnitOfMeasure</MudText>
            </div>
        }
        else
        {
            @L["Add new line"]
        }
    </TitleContent>
    <DialogContent>
        <MudContainer Class="pa-0">
            <MudTabs Elevation="2" Rounded ApplyEffectsToContainer MinimumTabWidth="200px">
                @* Tab 1: Chiết Khấu (Discount Types 1 & 2) *@
                <MudTabPanel Text="Chiết Khấu">
                    <MudForm @ref="@EditPromotionFrontMarginFormRef" Model="@CurrentLine">
                        <MudGrid Spacing="3" Class="pa-4">
                            <MudItem xs="12" sm="6">
                                @if (IsShowEditFrontMarginDialog)
                                {
                                    <MudTextField Variant="Variant.Outlined" T="string" Label="@L["ItemNumber"]"
                                                  Value="@($"{CurrentLine.ItemNumber} - {CurrentLine.ItemName}")"
                                                  ReadOnly/>
                                }
                                else
                                {
                                    <MudAutocomplete Variant="Variant.Outlined" T="DetailItemDto" ShrinkLabel
                                                     Label="@L["ItemNumber"]"
                                                     ShowProgressIndicator
                                                     ValueChanged="@(dto => OnItemSelectedInAutoComplete(dto))"
                                                     ToStringFunc="@(dto => dto == null
                                                                       ? $"{CurrentLine.ItemNumber} - {CurrentLine.ItemName}"
                                                                       : dto.Number + " - " + dto.Name)"
                                                     Required
                                                     SearchFunc="@ItemSearch">
                                        <ProgressIndicatorInPopoverTemplate>
                                            <MudList T="string" ReadOnly>
                                                <MudListItem>
                                                    Loading...
                                                </MudListItem>
                                            </MudList>
                                        </ProgressIndicatorInPopoverTemplate>
                                        <ItemTemplate Context="e">
                                            <MudStack Row="false" StretchItems="StretchItems.All">
                                                <MudStack Spacing="0">
                                                    <MudText>@e.Name</MudText>
                                                    <MudStack Row Spacing="0">
                                                        <MudText Typo="Typo.caption">@e.Number</MudText>
                                                        <MudChip T="string" Size="Size.Small" Variant="Variant.Text"
                                                                 Color="@(e.Blocked == 1 ? Color.Warning : Color.Success)">@(e.Blocked == 1 ? "Blocked" : "Active")</MudChip>
                                                        <MudChip T="string" Size="Size.Small" Variant="Variant.Text"
                                                                 Color="@(e.Status == 2 ? Color.Success : Color.Warning)">
                                                            Status
                                                        </MudChip>
                                                    </MudStack>
                                                </MudStack>
                                            </MudStack>
                                        </ItemTemplate>
                                    </MudAutocomplete>
                                }
                            </MudItem>
                            <MudItem xs="12" sm="6">
                                <MudSelect Variant="Variant.Outlined" T="int" Label="Trạng thái"
                                           @bind-Value="CurrentLine.Status"
                                           ReadOnly="@IsShowAddFrontMarginDialog">
                                    <MudSelectItem Value="@((int)PromotionFrontMarginStatus.Active)">
                                        <div style="display: flex; align-items: center;">
                                            <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Color="Color.Success" Size="Size.Small"
                                                     Style="margin-right: 8px;"/>
                                            @L["Active"]
                                        </div>
                                    </MudSelectItem>
                                    @if (IsShowEditFrontMarginDialog)
                                    {
                                        <MudSelectItem Value="@((int)PromotionFrontMarginStatus.Inactive)">
                                            <div style="display: flex; align-items: center;">
                                                <MudIcon Icon="@Icons.Material.Filled.Cancel" Color="Color.Warning" Size="Size.Small"
                                                         Style="margin-right: 8px;"/>
                                                @L["Inactive"]
                                            </div>
                                        </MudSelectItem>
                                    }
                                </MudSelect>
                            </MudItem>
                            <MudItem xs="12" sm="4">
                                <MudSelect Variant="Variant.Outlined" T="string" Label="@L["Unit"]"
                                           @bind-Value="CurrentLine.UnitOfMeasure"
                                           ToStringFunc="dto => dto ?? CurrentLine.UnitOfMeasure">
                                    @if (LsDetailItemUnitOfMeasureDtoEditing != null)
                                    {
                                        foreach (var unit in LsDetailItemUnitOfMeasureDtoEditing)
                                        {
                                            <MudSelectItem Value="@unit.Code"
                                                           Disabled="@(unit.Block == 1)">@unit.Code.ToUpper()</MudSelectItem>
                                        }
                                    }
                                </MudSelect>
                            </MudItem>
                            <MudItem xs="12" sm="4">
                                <MudSelect Variant="Variant.Outlined" T="int" Label="Loại chiết khấu"
                                           Value="CurrentLine.DiscountType"
                                           ValueChanged="OnDiscountTypeChanged">
                                    <MudSelectItem Value="1">Phần trăm (%)</MudSelectItem>
                                    <MudSelectItem Value="2">Số tiền cố định (VND)</MudSelectItem>
                                </MudSelect>
                            </MudItem>
                            @if (CurrentLine.DiscountType != 1)
                            {
                                if (CurrentLine.DiscountType == 2)
                                {
                                    <MudItem xs="12" sm="4">
                                        <MudNumericField Variant="Variant.Outlined" T="decimal" HideSpinButtons
                                                         Label="Số tiền chiết khấu cố định (VND)"
                                                         @bind-Value="CurrentLine.FixedDiscountAmount"
                                                         Format="N0" Immediate Min="0" Required/>
                                    </MudItem>
                                }
                            }
                            else
                            {
                                <MudItem xs="12" sm="4">
                                    <MudNumericField Variant="Variant.Outlined" T="decimal" HideSpinButtons
                                                     Label="Phần trăm chiết khấu (%)"
                                                     @bind-Value="CurrentLine.DiscountPercentage"
                                                     Format="N2" Immediate Min="0" Max="100" Required/>
                                </MudItem>
                            }

                            <!-- Progressive/Tiered Discount Section -->
                            <MudItem xs="12" sm="4">
                                <MudNumericField Variant="Variant.Outlined" T="decimal?" HideSpinButtons
                                                 Label="Mốc khuyến mãi"
                                                 HelperText="Số lượng tối thiểu để kích hoạt bonus"
                                                 @bind-Value="CurrentLine.TierQuantityThreshold"
                                                 Format="N0" Immediate Min="0"/>
                            </MudItem>

                            @if (CurrentLine.DiscountType == 1 && CurrentLine.TierQuantityThreshold > 0)
                            {
                                <MudItem xs="12" sm="4">
                                    <MudNumericField Variant="Variant.Outlined" T="decimal?" HideSpinButtons
                                                     Label="Bonus % bổ sung"
                                                     HelperText="% chiết khấu thêm khi đạt ngưỡng"
                                                     @bind-Value="CurrentLine.TierBonusPercentage"
                                                     Format="N2" Immediate Min="0" Max="100" Required
                                                     ErrorText="Bắt buộc nhập % bonus khi có ngưỡng số lượng"/>
                                </MudItem>
                            }

                            @if (CurrentLine.DiscountType == 2 && CurrentLine.TierQuantityThreshold > 0)
                            {
                                <MudItem xs="12" sm="4">
                                    <MudNumericField Variant="Variant.Outlined" T="decimal?" HideSpinButtons
                                                     Label="Bonus số tiền bổ sung (VND)"
                                                     HelperText="Số tiền chiết khấu thêm khi đạt ngưỡng"
                                                     @bind-Value="CurrentLine.TierBonusAmount"
                                                     Format="N0" Immediate Min="0" Required
                                                     ErrorText="Bắt buộc nhập số tiền bonus khi có ngưỡng số lượng"/>
                                </MudItem>
                            }
                            @if (CurrentLine.TierQuantityThreshold > 0)
                            {
                                <MudItem xs="12" sm="4">
                                    <MudPaper Class="pa-3" Style="background-color: #e3f2fd;">
                                        <MudText Typo="Typo.body2" Color="Color.Info">
                                            <strong>Tổng chiết khấu:</strong><br/>
                                            @switch (CurrentLine.DiscountType)
                                            {
                                                case 1 when CurrentLine.TierBonusPercentage.HasValue:
                                                    <text>@(CurrentLine.DiscountPercentage + CurrentLine.TierBonusPercentage.Value)% khi SL
                                                        ≥ @CurrentLine.TierQuantityThreshold</text>
                                                    break;
                                                case 2 when CurrentLine.TierBonusAmount.HasValue:
                                                    <text>@((CurrentLine.FixedDiscountAmount + CurrentLine.TierBonusAmount.Value).ToString("N0")) VND khi SL
                                                        ≥ @CurrentLine.TierQuantityThreshold</text>
                                                    break;
                                                default:
                                                    @:Chưa cấu hình bonus lũy kế
                                                    break;
                                            }
                                        </MudText>
                                    </MudPaper>
                                </MudItem>
                            }
                            @if (CurrentLine.DiscountType == 2)
                            {
                                <MudItem xs="12" sm="4">
                                    <MudSelect Variant="Variant.Outlined" T="int" Label="Kiểu tính chiết khấu"
                                               @bind-Value="CurrentLine.GiftCalculationType"
                                               HelperText="Cách tính chiết khấu khi có nhiều mức">
                                        <MudSelectItem Value="1">
                                            <div style="display: flex; align-items: center;">
                                                <MudIcon Icon="@Icons.Material.Filled.TrendingUp" Color="Color.Info" Size="Size.Small"
                                                         Style="margin-right: 8px;"/>
                                                Luỹ tiến (Tỷ lệ thuận)
                                            </div>
                                        </MudSelectItem>
                                        <MudSelectItem Value="2">
                                            <div style="display: flex; align-items: center;">
                                                <MudIcon Icon="@Icons.Material.Filled.Flag" Color="Color.Success" Size="Size.Small"
                                                         Style="margin-right: 8px;"/>
                                                Mốc (Cố định khi đạt ngưỡng)
                                            </div>
                                        </MudSelectItem>
                                    </MudSelect>
                                </MudItem>
                            }
                            <MudItem xs="12" sm="12">
                                <MudTextField Variant="Variant.Outlined" T="string" Label="@L["Description"]" Lines="2"
                                              @bind-Value="CurrentLine.Notes" AutoGrow
                                              HelperText="Mô tả chi tiết về chương trình"/>
                            </MudItem>
                        </MudGrid>
                    </MudForm>
                </MudTabPanel>

                @* Tab 2: Tặng Hàng (Gift Types 3 & 4) *@
                <MudTabPanel Text="Tặng Hàng" Icon="@Icons.Material.Filled.CardGiftcard">
                    <MudForm Model="@CurrentLine">
                        <MudGrid Spacing="3" Class="pa-4">
                            @{
                                // Ensure default gift type is set when accessing this tab
                                if (CurrentLine.DiscountType != 3 && CurrentLine.DiscountType != 4)
                                {
                                    CurrentLine.DiscountType = 3;// Default to same product gift
                                }
                            }
                            <MudItem xs="12" sm="6">
                                @if (IsShowEditFrontMarginDialog)
                                {
                                    <MudTextField Variant="Variant.Outlined" T="string" Label="@L["ItemNumber"]"
                                                  Value="@($"{CurrentLine.ItemNumber} - {CurrentLine.ItemName}")"
                                                  ReadOnly/>
                                }
                                else
                                {
                                    <MudAutocomplete Variant="Variant.Outlined" T="DetailItemDto" ShrinkLabel
                                                     Label="Sản phẩm mua (Item mua để được tặng)"
                                                     ShowProgressIndicator
                                                     ValueChanged="@(dto => OnItemSelectedInAutoComplete(dto))"
                                                     ToStringFunc="@(dto => dto == null
                                                                       ? $"{CurrentLine.ItemNumber} - {CurrentLine.ItemName}"
                                                                       : dto.Number + " - " + dto.Name)"
                                                     Required
                                                     SearchFunc="@ItemSearch">
                                        <ProgressIndicatorInPopoverTemplate>
                                            <MudList T="string" ReadOnly>
                                                <MudListItem>
                                                    Loading...
                                                </MudListItem>
                                            </MudList>
                                        </ProgressIndicatorInPopoverTemplate>
                                        <ItemTemplate Context="e">
                                            <MudStack Row="false" StretchItems="StretchItems.All">
                                                <MudStack Spacing="0">
                                                    <MudText>@e.Name</MudText>
                                                    <MudStack Row Spacing="0">
                                                        <MudText Typo="Typo.caption">@e.Number</MudText>
                                                        <MudChip T="string" Size="Size.Small" Variant="Variant.Text"
                                                                 Color="@(e.Blocked == 1 ? Color.Warning : Color.Success)">@(e.Blocked == 1 ? "Blocked" : "Active")</MudChip>
                                                        <MudChip T="string" Size="Size.Small" Variant="Variant.Text"
                                                                 Color="@(e.Status == 2 ? Color.Success : Color.Warning)">
                                                            Status
                                                        </MudChip>
                                                    </MudStack>
                                                </MudStack>
                                            </MudStack>
                                        </ItemTemplate>
                                    </MudAutocomplete>
                                }
                            </MudItem>
                            <MudItem xs="12" sm="6">
                                <MudSelect Variant="Variant.Outlined" T="int" Label="Trạng thái"
                                           @bind-Value="CurrentLine.Status"
                                           ReadOnly="@IsShowAddFrontMarginDialog">
                                    <MudSelectItem Value="@((int)PromotionFrontMarginStatus.Active)">
                                        <div style="display: flex; align-items: center;">
                                            <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Color="Color.Success" Size="Size.Small"
                                                     Style="margin-right: 8px;"/>
                                            @L["Active"]
                                        </div>
                                    </MudSelectItem>
                                    @if (IsShowEditFrontMarginDialog)
                                    {
                                        <MudSelectItem Value="@((int)PromotionFrontMarginStatus.Inactive)">
                                            <div style="display: flex; align-items: center;">
                                                <MudIcon Icon="@Icons.Material.Filled.Cancel" Color="Color.Warning" Size="Size.Small"
                                                         Style="margin-right: 8px;"/>
                                                @L["Inactive"]
                                            </div>
                                        </MudSelectItem>
                                    }
                                </MudSelect>
                            </MudItem>

                            <MudItem xs="12" sm="6">
                                <MudSelect Variant="Variant.Outlined" T="string" Label="Đơn vị tính sản phẩm mua"
                                           @bind-Value="CurrentLine.UnitOfMeasure"
                                           ToStringFunc="dto => dto ?? CurrentLine.UnitOfMeasure"
                                           Required
                                           RequiredError="Vui lòng chọn đơn vị tính">
                                    @if (LsDetailItemUnitOfMeasureDtoEditing != null)
                                    {
                                        foreach (var unit in LsDetailItemUnitOfMeasureDtoEditing)
                                        {
                                            <MudSelectItem Value="@unit.Code"
                                                           Disabled="@(unit.Block == 1)">@unit.Code.ToUpper()</MudSelectItem>
                                        }
                                    }
                                </MudSelect>
                            </MudItem>

                            <MudItem xs="12" sm="6">
                                <MudSelect Variant="Variant.Outlined" T="int" Label="Loại tặng hàng"
                                           Value="CurrentLine.DiscountType"
                                           ValueChanged="OnDiscountTypeChanged"
                                           Required
                                           RequiredError="Vui lòng chọn loại tặng hàng">
                                    <MudSelectItem Value="3">Tặng cùng sản phẩm</MudSelectItem>
                                    <MudSelectItem Value="4">Tặng sản phẩm khác</MudSelectItem>
                                </MudSelect>
                            </MudItem>

                            <MudItem xs="12" sm="6">
                                <MudNumericField Variant="Variant.Outlined" T="decimal" HideSpinButtons
                                                 Label="Số lượng mua"
                                                 HelperText="Số lượng cần mua để được tặng"
                                                 @bind-Value="CurrentLine.BuyQuantity"
                                                 Format="N0" Immediate Min="1" Required
                                                 RequiredError="Vui lòng nhập số lượng mua"/>
                            </MudItem>
                            <MudItem xs="12" sm="6">
                                <MudNumericField Variant="Variant.Outlined" T="decimal" HideSpinButtons
                                                 Label="Số lượng tặng"
                                                 HelperText="Số lượng được tặng"
                                                 @bind-Value="CurrentLine.GiftQuantity"
                                                 Format="N0" Immediate Min="1" Required
                                                 RequiredError="Vui lòng nhập số lượng tặng"/>
                            </MudItem>

                            @* GiftCalculationType - chỉ hiển thị cho DiscountType 3, 4 trong tab Tặng Hàng *@
                            @if (CurrentLine.DiscountType == 3 || CurrentLine.DiscountType == 4)
                            {
                                <MudItem xs="12" sm="6">
                                    <MudSelect Variant="Variant.Outlined" T="int" Label="Kiểu tính quà tặng"
                                               @bind-Value="CurrentLine.GiftCalculationType"
                                               HelperText="Cách tính số lượng quà tặng">
                                        <MudSelectItem Value="1">
                                            <div style="display: flex; align-items: center;">
                                                <MudIcon Icon="@Icons.Material.Filled.TrendingUp" Color="Color.Info" Size="Size.Small"
                                                         Style="margin-right: 8px;"/>
                                                Luỹ tiến (Tỷ lệ thuận)
                                            </div>
                                        </MudSelectItem>
                                        <MudSelectItem Value="2">
                                            <div style="display: flex; align-items: center;">
                                                <MudIcon Icon="@Icons.Material.Filled.Flag" Color="Color.Success" Size="Size.Small"
                                                         Style="margin-right: 8px;"/>
                                                Mốc (Cố định khi đạt ngưỡng)
                                            </div>
                                        </MudSelectItem>
                                    </MudSelect>
                                </MudItem>
                            }

                            @if (CurrentLine.DiscountType == 4)
                            {
                                <MudItem xs="12">
                                    <MudAutocomplete Variant="Variant.Outlined" T="DetailItemDto" ShrinkLabel
                                                     Label="Sản phẩm tặng"
                                                     ShowProgressIndicator
                                                     ValueChanged="@OnGiftItemSelectedInAutoComplete"
                                                     ToStringFunc="@(dto => dto == null
                                                                       ? $"{CurrentLine.GiftItemNumber} - {CurrentLine.GiftItemName}"
                                                                       : dto.Number + " - " + dto.Name)"
                                                     Required
                                                     RequiredError="Vui lòng chọn sản phẩm tặng"
                                                     SearchFunc="@ItemSearch">
                                        <ProgressIndicatorInPopoverTemplate>
                                            <MudList T="string" ReadOnly>
                                                <MudListItem>
                                                    Loading...
                                                </MudListItem>
                                            </MudList>
                                        </ProgressIndicatorInPopoverTemplate>
                                        <ItemTemplate Context="e">
                                            <MudStack Row="false" StretchItems="StretchItems.All">
                                                <MudStack Spacing="0">
                                                    <MudText>@e.Name</MudText>
                                                    <MudStack Row Spacing="0">
                                                        <MudText Typo="Typo.caption">@e.Number</MudText>
                                                        <MudChip T="string" Size="Size.Small" Variant="Variant.Text"
                                                                 Color="@(e.Blocked == 1 ? Color.Warning : Color.Success)">@(e.Blocked == 1 ? "Blocked" : "Active")</MudChip>
                                                        <MudChip T="string" Size="Size.Small" Variant="Variant.Text"
                                                                 Color="@(e.Status == 2 ? Color.Success : Color.Warning)">
                                                            Status
                                                        </MudChip>
                                                    </MudStack>
                                                </MudStack>
                                            </MudStack>
                                        </ItemTemplate>
                                    </MudAutocomplete>
                                </MudItem>
                                <MudItem xs="12" sm="6">
                                    <MudSelect Variant="Variant.Outlined" T="string" Label="Đơn vị tính sản phẩm tặng"
                                               @bind-Value="CurrentLine.GiftItemUOM"
                                               Required
                                               RequiredError="Vui lòng chọn đơn vị tính">
                                        @if (LsDetailItemUnitOfMeasureDtoEditing != null)
                                        {
                                            foreach (var unit in LsDetailItemUnitOfMeasureDtoEditing)
                                            {
                                                <MudSelectItem Value="@unit.Code"
                                                               Disabled="@(unit.Block == 1)">@unit.Code.ToUpper()</MudSelectItem>
                                            }
                                        }
                                    </MudSelect>
                                </MudItem>
                            }

                            <!-- Điều kiện tối thiểu -->
                            <MudItem xs="12" sm="6">
                                <MudNumericField Variant="Variant.Outlined" T="decimal" HideSpinButtons
                                                 Label="Số lượng tối thiểu"
                                                 HelperText="Số lượng tối thiểu trong đơn hàng"
                                                 @bind-Value="CurrentLine.MinimumQuantity"
                                                 Format="N0" Immediate Min="0"/>
                            </MudItem>
                            <MudItem xs="12" sm="6">
                                <MudNumericField Variant="Variant.Outlined" T="decimal" HideSpinButtons
                                                 Label="Giá trị tối thiểu (VND)"
                                                 HelperText="Giá trị đơn hàng tối thiểu"
                                                 @bind-Value="CurrentLine.MinimumAmount"
                                                 Format="N0" Immediate Min="0"/>
                            </MudItem>
                            <MudItem xs="12" sm="12">
                                <MudTextField Variant="Variant.Outlined" T="string" Label="@L["Description"]" Lines="3"
                                              @bind-Value="CurrentLine.Notes" AutoGrow
                                              HelperText="Mô tả chi tiết về chương trình"/>
                            </MudItem>
                        </MudGrid>
                    </MudForm>
                </MudTabPanel>
            </MudTabs>
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton Variant="Variant.Text" Color="Color.Error"
                   OnClick="@OnCloseEditPOLineDialog">@L["Cancel"]</MudButton>
        <MudButton Variant="Variant.Filled" Color="Color.Primary"
                   OnClick="@OnSavePOLineAsync">@L["Save"]</MudButton>
    </DialogActions>
</MudDialog>
<MudDialog Visible="@(!string.IsNullOrWhiteSpace(ErrorMessage))"
           Options="new DialogOptions { FullWidth = true, MaxWidth = MaxWidth.Medium, CloseOnEscapeKey = false, BackdropClick = false }"
           TitleClass="mud-secondary" ContentStyle="min-height:200px">
    <TitleContent>
        <MudText Inline Typo="Typo.h5" Align="Align.Start">@L["List error create Line"]</MudText>
    </TitleContent>
    <DialogContent>
        <div>
            @foreach (var error in ErrorMessage.Split("\n"))
            {
                <MudChip T="String" Variant="Variant.Text" Color="Color.Warning">@error</MudChip>
            }
        </div>
    </DialogContent>
    <DialogActions>
        <MudButton Variant="Variant.Text" Color="Color.Error"
                   OnClick="@(_ => ErrorMessage = string.Empty)">@L["Clear"]</MudButton>
    </DialogActions>
</MudDialog>
