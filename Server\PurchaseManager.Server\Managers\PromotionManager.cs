using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PurchaseManager.Constants.Enum;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Promotions;
using PurchaseManager.Shared.Models.Promotions;
using PurchaseManager.Storage;
namespace PurchaseManager.Server.Managers;

public partial class PromotionManager : IPromotionManager
{
    private readonly ApplicationDbContext _context;
    private readonly ApplicationPersistenceManager _persistenceManager;
    private readonly IAdminManager _adminManager;
    private readonly ILogger<PromotionManager> _logger;
    private readonly IMapper _mapper;

    public PromotionManager(ApplicationDbContext context, IMapper mapper, ILogger<PromotionManager> logger, IAdminManager adminManager,
        ApplicationPersistenceManager persistenceManager)
    {
        _context = context;
        _mapper = mapper;
        _logger = logger;
        _adminManager = adminManager;
        _persistenceManager = persistenceManager;
    }
    
    public async Task<ApiResponse> GetPromotionsAsync()
    {
        try
        {
            var promotionHeaders = await _context.PromotionHeaders
                .Where(p => p.ModificationStatus != (int)ModificationStatusEnum.DELETED)
                .ToListAsync();
            var promotionDtos = _mapper.Map<List<GetPromotionHeaderDto>>(promotionHeaders);
            return ApiResponse.S200("Promotions retrieved successfully", promotionDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving promotions");
            return ApiResponse.S500(ex.GetBaseException()
                .Message);
        }
    }

    public async Task<ApiResponse> GetPromotionByFilterAsync(PromotionFilter filter)
    {
        try
        {
            var take = filter.PageSize ?? 10;
            var skip = (filter.PageIndex ?? 0) * take;

            var query = _context.PromotionHeaders.AsNoTracking()
                .Include(x => x.Vendor)
                .Where(p =>
                    p.ModificationStatus != (int)ModificationStatusEnum.DELETED &&
                    (filter.VendorNumber == null || p.VendorCode.Contains(filter.VendorNumber)) &&
                    (filter.Description == null || p.Description.Contains(filter.Description)) &&
                    (filter.PromotionNumber == null || p.Number.Contains(filter.PromotionNumber)) &&
                    (filter.Status == null || p.Status == filter.Status)
                );

            // Get total count
            var totalCount = await query.CountAsync();

            // Apply pagination if needed
            var promotions = await query
                .OrderByDescending(p => p.RowId)
                .Skip(skip)
                .Take(take)
                .ToListAsync();

            var promotionDtos = _mapper.Map<List<GetPromotionHeaderDto>>(promotions);
            var pagedResult = new PagedResult<GetPromotionHeaderDto>
            {
                Data = promotionDtos, CurrentPage = filter.PageIndex ?? 0, PageSize = filter.PageSize ?? 10, RowCount = totalCount
            };

            return ApiResponse.S200("Promotions retrieved successfully", pagedResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all promotions with filter");
            return ApiResponse.S500(ex.GetBaseException()
                .Message);
        }
    }

    public async Task<ApiResponse> GetPromotionByNumberAsync(string number)
    {
        try
        {
            var promotion = await _context.PromotionHeaders
                .AsNoTracking()
                .Include(p => p.Vendor)
                .Where(p => p.ModificationStatus != (int)ModificationStatusEnum.DELETED)
                .FirstOrDefaultAsync(p => p.Number == number);

            if (promotion == null)
            {
                return ApiResponse.S404("Promotion not found");
            }

            switch (promotion.ProgramType)
            {
                case (int)PromotionProgramTypeEnum.FrontMargin:
                    promotion.PromotionFrontMargins = await _context.PromotionFrontMargins
                        .AsNoTracking()
                        .Where(v => v.ProgramNumber == promotion.Number)
                        .ToListAsync();
                    break;
                case (int)PromotionProgramTypeEnum.BackMargin:
                    promotion.PromotionBackMargins = await _context.PromotionBackMargins
                        .AsNoTracking()
                        .Where(v => v.ProgramNumber == promotion.Number)
                        .ToListAsync();
                    break;
            }

            var promotionDto = _mapper.Map<GetPromotionHeaderDto>(promotion);
            return ApiResponse.S200("Promotion retrieved successfully", promotionDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving promotion by number: {Number}", number);
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }

    public async Task<ApiResponse> CreatePromotionAsync(CreatePromotionHeaderDto createDto)
    {
        try
        {
            const string business = "PROMOTION";
            const string branch = "AL";

            createDto.Number = await _adminManager.CreateNumberSeries(business, branch);
            var promotion = _mapper.Map<PromotionHeader>(createDto);
            _context.PromotionHeaders.Add(promotion);
            await _context.SaveChangesAsync();

            return ApiResponse.S200("Promotion created successfully", createDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating promotion");
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }

    public async Task<ApiResponse> UpdatePromotionAsync(string number, UpdatePromotionHeaderDto updateDto)
    {
        try
        {
            var promotion = await _context.PromotionHeaders
                .FirstOrDefaultAsync(p => p.Number == number);

            if (promotion == null)
            {
                return ApiResponse.S404("Promotion not found");
            }

            // Update properties
            _mapper.Map(updateDto, promotion);

            await _context.SaveChangesAsync();

            return ApiResponse.S200("Promotion updated successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating promotion: {Number}", number);
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }

    public async Task<ApiResponse> DeletePromotionAsync(List<string> numbers)
    {
        try
        {
            if (numbers == null || numbers.Count == 0)
            {
                return ApiResponse.S400("No promotion numbers provided");
            }

            var promotions = await _context.PromotionHeaders
                .Where(p => numbers.Contains(p.Number)
                            && p.ModificationStatus != (int)ModificationStatusEnum.DELETED)
                .ToListAsync();

            if (promotions.Count == 0)
            {
                return ApiResponse.S404("No promotions found with the provided numbers");
            }

            var foundNumbers = promotions.Select(p => p.Number)
                .ToList();
            var notFoundNumbers = numbers.Except(foundNumbers)
                .ToList();

            // Soft delete: Update ModificationStatus to DELETED instead of removing from DB
            foreach (var promotion in promotions)
            {
                promotion.ModificationStatus = (int)ModificationStatusEnum.DELETED;
                promotion.LastModifiedBy = _adminManager.GetUserLogin();
                promotion.LastModifiedAt = DateTime.Now;
            }

            await _context.SaveChangesAsync();

            var message = $"Successfully deleted {promotions.Count} promotion(s)";
            if (notFoundNumbers.Count != 0)
            {
                message += $". Not found: {string.Join(", ", notFoundNumbers)}";
            }

            return ApiResponse.S200(message);
        }
        catch (Exception ex)
        {
            if (numbers != null)
            {
                _logger.LogError(ex, "Error deleting promotions: {Numbers}", string.Join(", ", numbers));
            }
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }

    public async Task<ApiResponse> IsDocumentLockedByAnotherUserAsync(string documentNumber)
    {
        var poHeader = await _context.PromotionHeaders.FirstOrDefaultAsync(x => x.Number == documentNumber);
        var usingId = poHeader.UsingId;
        var userLogin = _adminManager.GetUserLogin();
        if (string.IsNullOrEmpty(usingId)
            || string.Equals(userLogin, usingId, StringComparison.CurrentCultureIgnoreCase)
            || poHeader.BeginUsingTime == null)
        {
            return ApiResponse.S200();
        }
        var usingMinute = (DateTime.Now - poHeader.BeginUsingTime.Value).TotalMinutes;
        return usingMinute < 30
            ? ApiResponse.S500(
            $"The document is currently being accessed by {usingId}." +
            $" Please try again in {Math.Ceiling(30 - usingMinute)} minutes.")
            : ApiResponse.S200();
    }

    public async Task<ApiResponse> OpenDocumentAsync(string documentNumber)
    {
        var poHeader = await _context.PromotionHeaders.FirstOrDefaultAsync(x => x.Number == documentNumber);
        if (poHeader == null)
        {
            return ApiResponse.S404("Document not found");
        }
        poHeader.UsingId = _adminManager.GetUserLogin();
        poHeader.BeginUsingTime = DateTime.Now;
        await _context.SaveChangesAsync();

        return ApiResponse.S200();
    }

    public async Task<ApiResponse> CloseDocumentAsync(string documentNumber)
    {
        var poHeader = await _context.PromotionHeaders.FirstOrDefaultAsync(x => x.Number == documentNumber);
        if (poHeader == null)
        {
            return ApiResponse.S404("Document not found");
        }
        var userLogin = _adminManager.GetUserLogin();

        poHeader.UsingId = null;
        poHeader.LastModifiedBy = userLogin;
        poHeader.LastModifiedAt = DateTime.Now;

        await _context.SaveChangesAsync();
        return ApiResponse.S200();
    }

}
