using System.Net.Http.Json;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Forms;
using MudBlazor;
using ObjectCloner.Extensions;
using PurchaseManager.Constants.Enum;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Dto.Promotions;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Providers;
using PurchaseManager.Theme.Material.Demo.Shared.Components.Promotions;
using PurchaseManager.UI.Base.Shared.Components;

namespace PurchaseManager.Theme.Material.Demo.Pages.Promotions;

public partial class FrontMarginPage : BaseComponent, IAsyncDisposable
{
    [Parameter]
    public string PromotionNumber { get; set; }

    #region Api Clients
    [Inject]
    protected IItemApiClient ItemApiClient { get; set; }

    [Inject]
    protected IPromotionApiClient PromotionApiClient { get; set; }
    #endregion

    [CascadingParameter]
    [Inject]
    public AuthenticationStateProvider AuthStateProvider { get; set; }

    [Inject]
    protected IViewNotifier ViewNotifier { get; set; }

    [Inject]
    protected HttpClient HttpClient { get; set; }

    [Inject]
    protected IDialogService DialogService { get; set; }

    // Main data properties
    protected GetPromotionHeaderDto PromotionHeader { get; set; } = new GetPromotionHeaderDto();

    protected PromotionToolBar ToolBarRef { get; set; }
    // User and state properties
    protected string ErrorMessage { get; set; }
    protected List<DetailItemUnitOfMeasureDto> LsDetailItemUnitOfMeasureDtoEditing { get; set; } = [];
    protected bool IsLoad { get; set; }
    protected bool IsShowAddFrontMarginDialog { get; set; }
    protected bool IsShowEditFrontMarginDialog { get; set; }
    protected DetailItemDto SelectedItem { get; set; } = new DetailItemDto();
    protected GetPromotionFrontMarginDto CurrentLine { get; set; } = new GetPromotionFrontMarginDto();
    protected MudForm EditPromotionFrontMarginFormRef { get; set; }
    protected HashSet<GetPromotionFrontMarginDto> SelectedFrontMargins { get; set; } = [];

    private const int MaxAllowedSize = 1024 * 1024 * 30;// 30MB

    protected bool IsEdit { get; set; }

    protected override async Task OnInitializedAsync()
    {
        IsLoad = true;
        await GetPromotionHeaderAsync(PromotionNumber);
        await GetUserRolesAsync();
        await ((IdentityAuthenticationStateProvider)AuthStateProvider).GetUserViewModel();
        IsLoad = false;
        UpdateToolbarState();
        await base.OnInitializedAsync();
    }

    protected async Task OnStartDateChange(DateTime? dueDate)
    {
        if (dueDate is not null)
        {
            PromotionHeader.StartDate = dueDate.Value;
            // Validate EndDate when StartDate changes
            if (PromotionHeader.EndDate < PromotionHeader.StartDate)
            {
                PromotionHeader.EndDate = PromotionHeader.StartDate.AddDays(1);
                ViewNotifier.Show("Ngày kết thúc đã được điều chỉnh để lớn hơn ngày bắt đầu", ViewNotifierType.Info);
            }

            // Auto-save when date changes
            if (IsEdit)
            {
                await UpdatePromotionHeaderAsync();
            }
        }
    }

    protected async Task OnEndDateChange(DateTime? dueDate)
    {
        if (dueDate is not null)
        {
            if (dueDate.Value < PromotionHeader.StartDate)
            {
                ViewNotifier.Show("Ngày kết thúc phải lớn hơn hoặc bằng ngày bắt đầu", ViewNotifierType.Warning);
                return;
            }
            PromotionHeader.EndDate = dueDate.Value;

            // Auto-save when date changes
            if (IsEdit)
            {
                await UpdatePromotionHeaderAsync();
            }
        }
    }

    protected async Task OnStatusChanged(int newStatus)
    {
        PromotionHeader.Status = newStatus;
        // Auto-save when status changes
        if (IsEdit)
        {
            await UpdatePromotionHeaderAsync();
        }
    }

    protected async Task UpdatePromotionHeaderAsync()
    {
        try
        {
            var updateDto = Mapper.Map<UpdatePromotionHeaderDto>(PromotionHeader);
            var response = await PromotionApiClient.UpdatePromotionHeaderAsync(PromotionNumber, updateDto);

            if (response.IsSuccessStatusCode)
            {
                ViewNotifier.Show("Đã cập nhật thông tin khuyến mãi", ViewNotifierType.Success);
                StateHasChanged();
            }
            else
            {
                ViewNotifier.Show("Cập nhật thất bại: " + response.Message, ViewNotifierType.Error);
                // Reload to revert changes
                await GetPromotionHeaderAsync(PromotionNumber);
            }
        }
        catch (Exception ex)
        {
            ViewNotifier.Show("Lỗi cập nhật: " + ex.Message, ViewNotifierType.Error);
            await GetPromotionHeaderAsync(PromotionNumber);
        }
    }

    protected async Task SaveOrEditPromotion()
    {
        if (IsEdit)
        {
            await SavePromotionAsync();
            await HandleOpenMode(false);
        }
        else
        {
            await HandleOpenMode();
            StateHasChanged();
        }
        UpdateToolbarState();
    }

    protected async Task PurchaserApprove()
    {
        try
        {
            var response = await HttpClient.PostAsync($"api/promotions/front-margin/{PromotionNumber}/approve/purchaser", null);
            if (response.IsSuccessStatusCode)
            {
                ViewNotifier.Show("Purchaser approval completed", ViewNotifierType.Success);
                await GetPromotionHeaderAsync(PromotionNumber);
                UpdateToolbarState();
                StateHasChanged();
            }
            else
            {
                ViewNotifier.Show("Failed to approve", ViewNotifierType.Error);
            }
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.Message, ViewNotifierType.Error);
        }
    }

    protected Task PrintPromotion()
    {
        try
        {
            viewNotifier.Show("Print functionality not implemented yet", ViewNotifierType.Info);
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error);
        }
        return Task.CompletedTask;
    }

    protected Task ExportPromotion()
    {
        try
        {
            viewNotifier.Show("Export functionality not implemented yet", ViewNotifierType.Info);
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error);
        }
        return Task.CompletedTask;
    }

    protected Task SendEmail()
    {
        try
        {
            viewNotifier.Show("Email functionality not implemented yet", ViewNotifierType.Info);
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error);
        }
        return Task.CompletedTask;
    }

    protected void UpdateToolbarState()
    {
        var hasData = PromotionHeader.FrontMargins?.Count > 0;
        var status = GetCurrentStatus();
        ToolBarRef?.SetFrontMarginState(IsEdit, status, hasData);
    }

    protected int GetCurrentStatus()
    {
        // Map from PromotionHeader.Status to dropdown values
        // Assuming PromotionHeader has a Status field
        return PromotionHeader.Status switch
        {
            1 => 1, // Draft
            2 => 2, // Active
            3 => 3, // Inactive
            4 => 4, // Expired
            _ => 1  // Default to Draft
        };
    }

    protected async Task UpdatePromotionStatus(int newStatus)
    {
        try
        {
            // Update the promotion status via API
            var response = await HttpClient.PutAsJsonAsync($"api/promotions/{PromotionNumber}/status", new { Status = newStatus });

            if (response.IsSuccessStatusCode)
            {
                PromotionHeader.Status = newStatus;
                ViewNotifier.Show($"Promotion status updated to {GetStatusText(newStatus)}", ViewNotifierType.Success);
                UpdateToolbarState();
                StateHasChanged();
            }
            else
            {
                ViewNotifier.Show("Failed to update promotion status", ViewNotifierType.Error);
            }
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.Message, ViewNotifierType.Error);
        }
    }

    private string GetStatusText(int status)
    {
        return status switch
        {
            1 => "Draft",
            2 => "Active",
            3 => "Inactive",
            4 => "Expired",
            _ => "Unknown"
        };
    }

    // Condition management methods
    protected void AddCondition()
    {
        CurrentLine = new GetPromotionFrontMarginDto()
        {
            Status = (int)PromotionFrontMarginStatus.Active,// Set default status to Active for new items
            GiftCalculationType = 2 // Default to Milestone (Mốc)
        };
        IsShowAddFrontMarginDialog = true;
    }

    protected async Task DeletePromotionFrontMarginsAsync()
    {
        if (SelectedFrontMargins is { Count: > 0 })
        {
            var confirm = await DialogService.ShowMessageBox(
            "Confirm Deletion",
            "Are you sure you want to delete the selected front margins?",
            "Delete",
            cancelText: "Cancel");

            if (confirm.GetValueOrDefault())
            {
                try
                {
                    // Get list of numbers from selected front margins
                    var numbers = SelectedFrontMargins.Select(x => x.Number)
                        .ToList();

                    var response = await PromotionApiClient.DeletePromotionFrontMarginAsync(numbers);

                    if (response.IsSuccessStatusCode)
                    {
                        ViewNotifier.Show($"Successfully deleted {numbers.Count} front margin(s)",
                        ViewNotifierType.Success);
                        SelectedFrontMargins.Clear();
                        await GetDetailPromotionAsync(PromotionNumber);
                    }
                    else
                    {
                        ViewNotifier.Show(response.Message, ViewNotifierType.Error);
                    }
                }
                catch (Exception ex)
                {
                    ViewNotifier.Show(ex.Message, ViewNotifierType.Error);
                }
            }
        }
    }

    protected Task OnItemSelectedInAutoComplete(DetailItemDto dto)
    {
        try
        {
            SelectedItem = dto;
            CurrentLine.ItemName = dto.Name;
            CurrentLine.ItemNumber = dto.Number;
            LsDetailItemUnitOfMeasureDtoEditing = dto.ItemUnitOfMeasures;
            if (dto.Status < 2)
            {
                ViewNotifier.Show("Item is editing.", ViewNotifierType.Warning);
            }
            if (dto.Blocked == 1)
            {
                ViewNotifier.Show("Item is blocked", ViewNotifierType.Warning);
            }
        }
        catch (Exception e)
        {
            ViewNotifier.Show("Get Last Unit Cost has error: " + e.GetBaseException()
                .Message, ViewNotifierType.Error);
        }
        return Task.CompletedTask;
    }

    protected Task OnGiftItemSelectedInAutoComplete(DetailItemDto dto)
    {
        try
        {
            if (dto != null)
            {
                CurrentLine.GiftItemName = dto.Name;
                CurrentLine.GiftItemNumber = dto.Number;

                // Load unit of measures for gift item
                LsDetailItemUnitOfMeasureDtoEditing = dto.ItemUnitOfMeasures;

                if (dto.Status < 2)
                {
                    ViewNotifier.Show("Gift item is editing.", ViewNotifierType.Warning);
                }
                if (dto.Blocked == 1)
                {
                    ViewNotifier.Show("Gift item is blocked", ViewNotifierType.Warning);
                }
            }
        }
        catch (Exception e)
        {
            ViewNotifier.Show("Select gift item has error: " + e.GetBaseException()
                .Message, ViewNotifierType.Error);
        }
        return Task.CompletedTask;
    }
    protected async Task OnEditPOLine(GetPromotionFrontMarginDto dto)
    {
        try
        {
            CurrentLine = new GetPromotionFrontMarginDto()
            {
                GiftCalculationType = 2 // Default to Milestone (Mốc)
            };
            if (!IsEdit)
            {
                return;
            }
            var itemNumber = dto.ItemNumber;

            // Get item unit of measure by itemNumber
            var apiResponse = await ItemApiClient.GetItemUnitOfMeasure(itemNumber);
            if (apiResponse.IsSuccessStatusCode)
            {
                LsDetailItemUnitOfMeasureDtoEditing = apiResponse.Result;
            }
            CurrentLine = dto.DeepClone();
            IsShowEditFrontMarginDialog = true;
            IsShowAddFrontMarginDialog = false;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    protected async Task HandleOpenMode(bool open = true)
    {
        if (open)
        {
            var openResult = await PromotionApiClient.OpenDocumentAsync(PromotionNumber);
            if (!openResult.IsSuccessStatusCode)
            {
                ViewNotifier.Show(openResult.Message, ViewNotifierType.Error);
                return;
            }

            IsEdit = true;
            SelectedFrontMargins?.Clear();
        }
        else
        {
            var closeResult = await PromotionApiClient.CloseDocumentAsync(PromotionNumber);
            if (!closeResult.IsSuccessStatusCode)
            {
                ViewNotifier.Show(closeResult.Message, ViewNotifierType.Error);
                return;
            }

            IsEdit = false;
        }
    }

    // Item search for conditions and rewards
    protected async Task<IEnumerable<DetailItemDto>> ItemSearch(string value, CancellationToken cancellationToken)
    {
        try
        {
            IEnumerable<DetailItemDto> result = new List<DetailItemDto>();

            if (string.IsNullOrWhiteSpace(value) || string.IsNullOrEmpty(value))
            {
                value = string.Empty;
            }
            var apiResponse = await HttpClient.GetFromJsonAsync<ApiResponseDto<List<DetailItemDto>>>(
            $"api/data/SearchItem?number={value.Trim()}", cancellationToken);

            if (!apiResponse.IsSuccessStatusCode)
            {
                return result;
            }

            result = apiResponse.Result;

            return result;
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
            return new List<DetailItemDto>();
        }
    }
    protected void OnCloseEditPOLineDialog()
    {
        IsShowAddFrontMarginDialog = false;
        IsShowEditFrontMarginDialog = false;
    }

    protected async Task OnSavePOLineAsync()
    {
        try
        {
            if (SelectedItem.Status < 2)
            {
                ViewNotifier.Show("Item is editing.", ViewNotifierType.Warning);
                return;
            }
            if (SelectedItem.Blocked == 1)
            {
                ViewNotifier.Show("Item is blocked", ViewNotifierType.Warning);
                return;
            }

            // Validation for Gift Promotions (Types 3 & 4)
            if (CurrentLine.DiscountType == 3 || CurrentLine.DiscountType == 4)
            {
                if (CurrentLine.BuyQuantity <= 0)
                {
                    ViewNotifier.Show("Vui lòng nhập số lượng mua hợp lệ", ViewNotifierType.Warning);
                    return;
                }
                if (CurrentLine.GiftQuantity <= 0)
                {
                    ViewNotifier.Show("Vui lòng nhập số lượng tặng hợp lệ", ViewNotifierType.Warning);
                    return;
                }
                if (CurrentLine.DiscountType == 4)// Different product gift
                {
                    if (string.IsNullOrWhiteSpace(CurrentLine.GiftItemNumber))
                    {
                        ViewNotifier.Show("Vui lòng chọn sản phẩm tặng", ViewNotifierType.Warning);
                        return;
                    }
                    if (string.IsNullOrWhiteSpace(CurrentLine.GiftItemUOM))
                    {
                        ViewNotifier.Show("Vui lòng chọn đơn vị tính cho sản phẩm tặng", ViewNotifierType.Warning);
                        return;
                    }
                }
            }

            // Validation for Tier Bonus when TierQuantityThreshold is set
            if (CurrentLine.TierQuantityThreshold is > 0)
            {
                switch (CurrentLine.DiscountType)
                {
                    // Percentage discount
                    case 1 when CurrentLine.TierBonusPercentage is not > 0:
                        ViewNotifier.Show("Vui lòng nhập % chiết khấu bonus khi đã điền ngưỡng số lượng", ViewNotifierType.Warning);
                        return;
                    // Fixed amount discount
                    case 2 when CurrentLine.TierBonusAmount is not > 0:
                        ViewNotifier.Show("Vui lòng nhập số tiền chiết khấu bonus khi đã điền ngưỡng số lượng",
                        ViewNotifierType.Warning);
                        return;
                }
            }

            if (IsShowAddFrontMarginDialog)
            {
                await CreatePromotionFrontMarginAsync();
                return;
            }

            await EditPromotionFrontMarginFormRef.Validate();
            if (EditPromotionFrontMarginFormRef.IsValid)
            {
                var rq = Mapper.Map<UpdatePromotionFrontMarginDto>(CurrentLine);
                var apiResponse = await PromotionApiClient.UpdatePromotionFrontMarginAsync(CurrentLine.Number, rq);
                if (!apiResponse.IsSuccessStatusCode)
                {
                    ViewNotifier.Show(apiResponse.Message, ViewNotifierType.Error);
                }
                else
                {
                    ViewNotifier.Show(L["Update success"], ViewNotifierType.Success);
                    OnCloseEditPOLineDialog();
                    await GetDetailPromotionAsync(PromotionNumber);
                }
            }
        }
        catch (Exception e)
        {
            ViewNotifier.Show(e.GetBaseException()
                .Message, ViewNotifierType.Error);
        }
    }

    protected async Task UploadPromotionFromFiles(IBrowserFile file)
    {
        try
        {
            IsLoad = true;

            if (file.Size > MaxAllowedSize)
            {
                ViewNotifier.Show($"File size exceeds the limit of {MaxAllowedSize} bytes.", ViewNotifierType.Error);
                return;
            }

            // Sử dụng ApiClient để gửi file lên server
            var result = await PromotionApiClient.ImportPromotionFrontMarginAsync(PromotionNumber, file);

            if (result.IsSuccessStatusCode)
            {
                await GetDetailPromotionAsync(PromotionNumber);
                ViewNotifier.Show("Import thành công!", ViewNotifierType.Success);
                SelectedFrontMargins.Clear();
            }
            else
            {
                ViewNotifier.Show($"Lỗi từ server: {result.Message}", ViewNotifierType.Error);
            }
        }
        catch (Exception ex)
        {
            ViewNotifier.Show($"Lỗi khi import file: {ex.Message}", ViewNotifierType.Error);
        }
        finally
        {
            IsLoad = false;
        }
    }

    // Handle DiscountType change to set appropriate default values
    protected void OnDiscountTypeChanged(int discountType)
    {
        CurrentLine.DiscountType = discountType;

        // Set GiftCalculationType default to Milestone (2) for cases 2, 3, 4
        if (discountType == 2 || discountType == 3 || discountType == 4)
        {
            CurrentLine.GiftCalculationType = 2; // Default to Milestone (Mốc)
        }
        else
        {
            CurrentLine.GiftCalculationType = 1; // Default to Progressive for other cases
        }
    }

    public async ValueTask DisposeAsync()
    {
        if (IsEdit)
        {
            await PromotionApiClient.CloseDocumentAsync(PromotionNumber);
        }
    }
}
