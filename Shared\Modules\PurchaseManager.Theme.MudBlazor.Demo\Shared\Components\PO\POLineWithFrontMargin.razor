@* @using PurchaseManager.Shared.Services *@
@* @using PurchaseManager.Shared.Dto.PO *@
@* @inject IFrontMarginUIService FrontMarginService *@
@* *@
@* <MudTableRow> *@
@*     <!-- Item Number --> *@
@*     <MudTd> *@
@*         <div class="d-flex align-items-center"> *@
@*             @POLine.ItemNumber *@
@*             @if (POLine.HasFrontMargin) *@
@*             { *@
@*                 <MudIcon Icon="@Icons.Material.Filled.LocalOffer"  *@
@*                          Color="Color.Success"  *@
@*                          Size="Size.Small"  *@
@*                          Class="ml-2" *@
@*                          Title="Front Margin Applied" /> *@
@*             } *@
@*         </div> *@
@*     </MudTd> *@
@* *@
@*     <!-- Description --> *@
@*     <MudTd>@POLine.Description</MudTd> *@
@* *@
@*     <!-- Quantity --> *@
@*     <MudTd> *@
@*         <MudNume<PERSON>Field @bind-Value="POLine.Quantity"  *@
@*                          Min="0"  *@
@*                          Step="1" *@
@*                          Disabled="@IsReadOnly" *@
@*                          ValueChanged="OnQuantityChanged" /> *@
@*     </MudTd> *@
@* *@
@*     <!-- Unit Cost --> *@
@*     <MudTd> *@
@*         <div class="unit-cost-container"> *@
@*             @if (POLine.HasFrontMargin && POLine.OriginalUnitCost > 0) *@
@*             { *@
@*                 <div class="original-price"> *@
@*                     <del>@POLine.OriginalUnitCost.ToString("N2")</del> *@
@*                 </div> *@
@*             } *@
@*             <MudNumericField @bind-Value="POLine.UnitCost"  *@
@*                              Min="0"  *@
@*                              Step="0.01" *@
@*                              Format="N2" *@
@*                              Disabled="@(IsReadOnly || POLine.HasFrontMargin)" *@
@*                              ValueChanged="OnUnitCostChanged" /> *@
@*         </div> *@
@*     </MudTd> *@
@* *@
@*     <!-- Discount --> *@
@*     <MudTd> *@
@*         <div class="discount-container"> *@
@*             @if (POLine.HasFrontMargin) *@
@*             { *@
@*                 <MudChip Color="Color.Success" Size="Size.Small"> *@
@*                     FM: @POLine.FrontMarginDiscountPercent.ToString("N1")% *@
@*                 </MudChip> *@
@*             } *@
@*             else *@
@*             { *@
@*                 <MudNumericField @bind-Value="POLine.LineDiscountPercent"  *@
@*                                  Min="0"  *@
@*                                  Max="100" *@
@*                                  Step="0.1" *@
@*                                  Format="N1" *@
@*                                  Disabled="@IsReadOnly" *@
@*                                  Suffix="%" *@
@*                                  ValueChanged="OnDiscountChanged" /> *@
@*             } *@
@*         </div> *@
@*     </MudTd> *@
@* *@
@*     <!-- Amount --> *@
@*     <MudTd> *@
@*         <div class="amount-container"> *@
@*             @if (POLine.HasFrontMargin && POLine.FrontMarginDiscountAmount > 0) *@
@*             { *@
@*                 <div class="savings-info"> *@
@*                     <small class="text-success"> *@
@*                         Saved: @POLine.FrontMarginDiscountAmount.ToString("N2") *@
@*                     </small> *@
@*                 </div> *@
@*             } *@
@*             <strong>@POLine.Amount.ToString("N2")</strong> *@
@*         </div> *@
@*     </MudTd> *@
@* *@
@*     <!-- Actions --> *@
@*     <MudTd> *@
@*         <MudButtonGroup> *@
@*             @if (POLine.HasFrontMargin) *@
@*             { *@
@*                 <MudIconButton Icon="@Icons.Material.Filled.Info"  *@
@*                                Color="Color.Info"  *@
@*                                Size="Size.Small" *@
@*                                OnClick="ShowFrontMarginDetails" *@
@*                                Title="Front Margin Details" /> *@
@*             } *@
@*              *@
@*             @if (!IsReadOnly) *@
@*             { *@
@*                 <MudIconButton Icon="@Icons.Material.Filled.Delete"  *@
@*                                Color="Color.Error"  *@
@*                                Size="Size.Small" *@
@*                                OnClick="DeleteLine" *@
@*                                Title="Delete Line" /> *@
@*             } *@
@*         </MudButtonGroup> *@
@*     </MudTd> *@
@* </MudTableRow> *@
@* *@
@* <!-- Front Margin Details Dialog --> *@
@* <MudDialog @bind-IsVisible="ShowDetailsDialog" Options="DialogOptions"> *@
@*     <TitleContent> *@
@*         <MudText Typo="Typo.h6">Front Margin Details</MudText> *@
@*     </TitleContent> *@
@*     <DialogContent> *@
@*         @if (POLine.HasFrontMargin) *@
@*         { *@
@*             <MudGrid> *@
@*                 <MudItem xs="12"> *@
@*                     <MudAlert Severity="Severity.Success"> *@
@*                         <strong>Front Margin Applied</strong> *@
@*                     </MudAlert> *@
@*                 </MudItem> *@
@*                  *@
@*                 <MudItem xs="6"> *@
@*                     <MudText Typo="Typo.subtitle2">Promotion Number:</MudText> *@
@*                     <MudText>@POLine.FrontMarginNumber</MudText> *@
@*                 </MudItem> *@
@*                  *@
@*                 <MudItem xs="6"> *@
@*                     <MudText Typo="Typo.subtitle2">Discount Source:</MudText> *@
@*                     <MudText>@POLine.DiscountSource</MudText> *@
@*                 </MudItem> *@
@*                  *@
@*                 <MudItem xs="6"> *@
@*                     <MudText Typo="Typo.subtitle2">Original Unit Cost:</MudText> *@
@*                     <MudText>@POLine.OriginalUnitCost.ToString("N2")</MudText> *@
@*                 </MudItem> *@
@*                  *@
@*                 <MudItem xs="6"> *@
@*                     <MudText Typo="Typo.subtitle2">Discounted Unit Cost:</MudText> *@
@*                     <MudText>@POLine.UnitCost.ToString("N2")</MudText> *@
@*                 </MudItem> *@
@*                  *@
@*                 <MudItem xs="6"> *@
@*                     <MudText Typo="Typo.subtitle2">Discount Percentage:</MudText> *@
@*                     <MudText>@POLine.FrontMarginDiscountPercent.ToString("N1")%</MudText> *@
@*                 </MudItem> *@
@*                  *@
@*                 <MudItem xs="6"> *@
@*                     <MudText Typo="Typo.subtitle2">Total Savings:</MudText> *@
@*                     <MudText Class="text-success">@POLine.FrontMarginDiscountAmount.ToString("N2")</MudText> *@
@*                 </MudItem> *@
@*             </MudGrid> *@
@*         } *@
@*     </DialogContent> *@
@*     <DialogActions> *@
@*         <MudButton OnClick="CloseDetailsDialog">Close</MudButton> *@
@*     </DialogActions> *@
@* </MudDialog> *@
@* *@
@* @code { *@
@*     [Parameter] public POLineGetDto POLine { get; set; } = new(); *@
@*     [Parameter] public string VendorCode { get; set; } = string.Empty; *@
@*     [Parameter] public DateTime OrderDate { get; set; } = DateTime.Now; *@
@*     [Parameter] public bool IsReadOnly { get; set; } = false; *@
@*     [Parameter] public EventCallback<POLineGetDto> OnLineChanged { get; set; } *@
@*     [Parameter] public EventCallback<POLineGetDto> OnLineDeleted { get; set; } *@
@* *@
@*     private bool ShowDetailsDialog = false; *@
@*     private DialogOptions DialogOptions = new() { MaxWidth = MaxWidth.Medium, FullWidth = true }; *@
@* *@
@*     protected override async Task OnParametersSetAsync() *@
@*     { *@
@*         if (!POLine.HasFrontMargin && !string.IsNullOrEmpty(VendorCode) && !string.IsNullOrEmpty(POLine.ItemNumber)) *@
@*         { *@
@*             await CheckAndApplyFrontMarginAsync(); *@
@*         } *@
@*     } *@
@* *@
@*     private async Task CheckAndApplyFrontMarginAsync() *@
@*     { *@
@*         try *@
@*         { *@
@*             var discount = await FrontMarginService.GetLineDiscountAsync(VendorCode, POLine.ItemNumber, OrderDate); *@
@*              *@
@*             if (discount != null) *@
@*             { *@
@*                 // Store original values *@
@*                 POLine.OriginalUnitCost = POLine.UnitCost; *@
@*                  *@
@*                 // Apply Front Margin *@
@*                 POLine.HasFrontMargin = true; *@
@*                 POLine.FrontMarginNumber = discount.PromotionNumber; *@
@*                 POLine.FrontMarginDiscountPercent = discount.DiscountPercentage; *@
@*                 POLine.DiscountSource = "Front Margin"; *@
@*                 POLine.CanEditDiscount = false; *@
@*                  *@
@*                 // Calculate new unit cost *@
@*                 if (discount.DiscountType == 1) // Percentage *@
@*                 { *@
@*                     POLine.UnitCost = POLine.OriginalUnitCost * (1 - discount.DiscountPercentage / 100); *@
@*                 } *@
@*                 else if (discount.DiscountType == 2) // Fixed amount *@
@*                 { *@
@*                     POLine.UnitCost = POLine.OriginalUnitCost - (discount.DiscountValue / POLine.Quantity); *@
@*                 } *@
@*                  *@
@*                 // Calculate discount amount *@
@*                 POLine.FrontMarginDiscountAmount = (POLine.OriginalUnitCost - POLine.UnitCost) * POLine.Quantity; *@
@*                  *@
@*                 // Update total amount *@
@*                 POLine.Amount = POLine.UnitCost * POLine.Quantity; *@
@*                  *@
@*                 await OnLineChanged.InvokeAsync(POLine); *@
@*             } *@
@*         } *@
@*         catch (Exception ex) *@
@*         { *@
@*             Console.WriteLine($"Error applying Front Margin: {ex.Message}"); *@
@*         } *@
@*     } *@
@* *@
@*     private async Task OnQuantityChanged(decimal newQuantity) *@
@*     { *@
@*         POLine.Quantity = newQuantity; *@
@*         POLine.Amount = POLine.UnitCost * POLine.Quantity; *@
@*          *@
@*         if (POLine.HasFrontMargin) *@
@*         { *@
@*             POLine.FrontMarginDiscountAmount = (POLine.OriginalUnitCost - POLine.UnitCost) * POLine.Quantity; *@
@*         } *@
@*          *@
@*         await OnLineChanged.InvokeAsync(POLine); *@
@*     } *@
@* *@
@*     private async Task OnUnitCostChanged(decimal newUnitCost) *@
@*     { *@
@*         if (!POLine.HasFrontMargin) *@
@*         { *@
@*             POLine.UnitCost = newUnitCost; *@
@*             POLine.Amount = POLine.UnitCost * POLine.Quantity; *@
@*             await OnLineChanged.InvokeAsync(POLine); *@
@*         } *@
@*     } *@
@* *@
@*     private async Task OnDiscountChanged(decimal newDiscount) *@
@*     { *@
@*         if (!POLine.HasFrontMargin) *@
@*         { *@
@*             POLine.LineDiscountPercent = newDiscount; *@
@*             // Recalculate amount with discount *@
@*             var discountAmount = (POLine.UnitCost * POLine.Quantity) * (newDiscount / 100); *@
@*             POLine.Amount = (POLine.UnitCost * POLine.Quantity) - discountAmount; *@
@*             await OnLineChanged.InvokeAsync(POLine); *@
@*         } *@
@*     } *@
@* *@
@*     private void ShowFrontMarginDetails() *@
@*     { *@
@*         ShowDetailsDialog = true; *@
@*     } *@
@* *@
@*     private void CloseDetailsDialog() *@
@*     { *@
@*         ShowDetailsDialog = false; *@
@*     } *@
@* *@
@*     private async Task DeleteLine() *@
@*     { *@
@*         await OnLineDeleted.InvokeAsync(POLine); *@
@*     } *@
@*     private Task OnQuantityChanged(int arg) *@
@*     { *@
@*         throw new NotImplementedException(); *@
@*     } *@
@* } *@
@* *@
@* <style> *@
@*     .unit-cost-container .original-price { *@
@*         font-size: 0.8em; *@
@*         color: #666; *@
@*         margin-bottom: 4px; *@
@*     } *@
@* *@
@*     .discount-container .mud-chip { *@
@*         font-size: 0.75em; *@
@*     } *@
@* *@
@*     .amount-container .savings-info { *@
@*         margin-bottom: 4px; *@
@*     } *@
@* *@
@*     .text-success { *@
@*         color: #4caf50 !important; *@
@*     } *@
@* </style> *@
