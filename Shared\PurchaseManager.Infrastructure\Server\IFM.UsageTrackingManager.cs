using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Dto.Promotions.Summarizes;
using PurchaseManager.Shared.Filters;
namespace PurchaseManager.Infrastructure.Server;

public interface IFrontMarginUsageTrackingManager
{
    Task<ApiResponse> TrackPromotionUsageAsync(CreatePromotionFrontMarginUsageDto usageDto);
    Task<ApiResponse> GetPromotionFrontMarginUsage(string poNumber);
    Task<ApiResponse> UpdateUsageAsync(string number, UpdatePromotionFrontMarginUsageDto dto);
    Task<ApiResponse> CancelUsageAsync(string number, CancelUsageRequest cancelUsageRequest);
}
