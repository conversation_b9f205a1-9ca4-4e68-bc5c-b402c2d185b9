using Microsoft.EntityFrameworkCore;
using PurchaseManager.Constants.Enum;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Storage;
namespace PurchaseManager.Server.Managers;

public class FrontMarginManager : IFrontMarginManager
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<FrontMarginManager> _logger;
    public FrontMarginManager(ApplicationDbContext context, ILogger<FrontMarginManager> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<ApiResponse> GetPromotionsByVendorItemsAsync(PromotionSelectedRequestDto request)
    {
        try
        {
            _logger.LogInformation("Getting promotions for vendor {VendorNumber} and {ItemCount} items",
            request.VendorNumber, request.ItemNumbers.Count);

            var checkDate = DateTime.Now;

            // Get active Front Margin promotions for the vendor and specific items
            var promotions = await _context.PromotionFrontMargins
                .AsNoTracking()
                .Include(p => p.PromotionHeader)
                .ThenInclude(h => h.Vendor)
                .Where(p => p.PromotionHeader.VendorCode == request.VendorNumber &&
                            request.ItemNumbers.Contains(p.ItemNumber) &&
                            p.PromotionHeader.ProgramType == 1 &&// Front Margin
                            p.PromotionHeader.StartDate <= checkDate &&
                            p.PromotionHeader.EndDate >= checkDate &&
                            p.ModificationStatus != (int)ModificationStatusEnum.DELETED)
                .Where(p => p.PromotionHeader.Status == (int)PromotionStatusEnum.Active)
                .OrderBy(p => p.PromotionHeader.ProgramName)
                .ThenBy(p => p.ItemNumber)
                .ThenBy(p => p.DiscountType)
                .ToListAsync();

            // Group by promotion program and map to PromotionSelectedDto
            var groupedPromotions = promotions
                .GroupBy(p => new
                {
                    p.PromotionHeader.Number,
                    p.PromotionHeader.ProgramName
                })
                .Select(g => new PromotionSelectedDto
                {
                    PromotionNumber = g.Key.Number,
                    PromotionName = g.Key.ProgramName,
                    FrontMargins = g.Select(p => new PromotionFrontMarginSelected
                    {
                        Number = p.Number,
                        ItemNumber = p.ItemNumber,
                        ItemName = p.ItemName,
                        UnitOfMeasure = p.UnitOfMeasure,
                        DiscountType = p.DiscountType,
                        DiscountPercentage = p.DiscountPercentage,
                        FixedDiscountAmount = p.FixedDiscountAmount,
                        BuyQuantity = p.BuyQuantity,
                        GiftQuantity = p.GiftQuantity,
                        GiftItemNumber = p.GiftItemNumber ?? string.Empty,
                        GiftItemName = p.GiftItemName ?? string.Empty,
                        GiftItemUOM = p.GiftItemUOM ?? string.Empty,
                        GiftItemQuantity = p.GiftItemQuantity,
                        MinimumQuantity = p.MinimumQuantity,
                        MinimumAmount = p.MinimumAmount,
                        TierQuantityThreshold = p.TierQuantityThreshold ?? 0,
                        TierBonusPercentage = p.TierBonusPercentage ?? 0,
                        TierBonusAmount = p.TierBonusAmount ?? 0,
                        GiftCalculationType = p.GiftCalculationType
                    }).ToList()
                })
                .ToList();

            _logger.LogInformation("Found {Count} promotion programs with {ItemCount} items for vendor {VendorNumber}",
            groupedPromotions.Count, promotions.Count, request.VendorNumber);

            return ApiResponse.S200($"Found {groupedPromotions.Count} promotion programs", groupedPromotions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting promotions for vendor {VendorNumber} and items", request.VendorNumber);
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }
}
