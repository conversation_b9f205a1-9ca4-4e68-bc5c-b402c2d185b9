namespace PurchaseManager.Shared.Dto.MarginDto.FrontMargin.ResultServices;

public class LineImpactPreview
{
    public int LineNumber { get; set; }
    public string ItemNumber { get; set; } = string.Empty;
    public string ItemName { get; set; } = string.Empty;
    public decimal OriginalAmount { get; set; }
    public decimal ProjectedFinalAmount { get; set; }
    public decimal ProjectedSavings { get; set; }
    public decimal DiscountPercentage { get; set; }
    public bool HasPromotion { get; set; }
    public int ApplicablePromotions { get; set; }
    public bool HasGifts { get; set; }
    public int GiftCount { get; set; }
}
