namespace PurchaseManager.Shared.Dto.Promotions.FrontMargins;

public class PromotionSelectedDto
{
    public string PromotionNumber { get; set; } = string.Empty;
    public string PromotionName { get; set; } = string.Empty;

    public List<PromotionFrontMarginSelected> FrontMargins { get; set; } = [];
}
public class PromotionFrontMarginSelected
{
    /// <summary>
    /// Front margin key
    /// </summary>
    public string Number { get; set; } = string.Empty;
    public string ItemNumber { get; set; } = string.Empty;
    public string ItemName { get; set; } = string.Empty;
    public string UnitOfMeasure { get; set; } = string.Empty;
    public int DiscountType { get; set; }
    public decimal BuyQuantity { get; set; }
    public decimal GiftQuantity { get; set; }
    public string GiftItemNumber { get; set; } = string.Empty;
    public string GiftItemName { get; set; } = string.Empty;
    public string GiftItemUOM { get; set; } = string.Empty;
    public decimal GiftItemQuantity { get; set; }
    public decimal MinimumQuantity { get; set; }
    public decimal MinimumAmount { get; set; }
    public decimal TierQuantityThreshold { get; set; }
    public decimal TierBonusPercentage { get; set; }
    public decimal TierBonusAmount { get; set; }

    /// <summary>
    /// Main discount percentage (for case 1 - percentage discount)
    /// </summary>
    public decimal DiscountPercentage { get; set; }

    /// <summary>
    /// Main fixed discount amount (for case 2 - fixed amount discount)
    /// </summary>
    public decimal FixedDiscountAmount { get; set; }

    /// <summary>
    /// Gift calculation type: 1=Progressive, 2=Milestone
    /// Only used for DiscountType = 3 (Same Item Gift) and 4 (Different Item Gift)
    /// </summary>
    public int GiftCalculationType { get; set; } = 1;// Default to Progressive
}
public class PromotionTableItemDto
{
    public string ProgramName { get; set; } = string.Empty;
    public string PromotionNumber { get; set; } = string.Empty;
    public string PromotionName { get; set; } = string.Empty;
    public int DiscountType { get; set; }
    public decimal MinOrderValue { get; set; }
    public decimal? TierQuantityThreshold { get; set; }
    public decimal? TierBonusPercentage { get; set; }
    public decimal? TierBonusAmount { get; set; }
    public decimal DiscountPercentage { get; set; }
    public decimal FixedDiscountAmount { get; set; }
    public int GiftCalculationType { get; set; }
    public string DiscountValue { get; set; } = string.Empty;
    public string BonusInfo { get; set; } = string.Empty;
    public bool IsSelected { get; set; }

    // Additional properties from CreateTableItemFromPromotion
    public string ProgramNumber { get; set; } = string.Empty;
    public string ItemNumber { get; set; } = string.Empty;
    public string ItemName { get; set; } = string.Empty;
    public string UnitOfMeasure { get; set; } = string.Empty;
    public decimal BuyQuantity { get; set; }
    public decimal GiftQuantity { get; set; }
    public string? GiftItemNumber { get; set; }
    public string? GiftItemName { get; set; }
    public string? GiftItemUOM { get; set; }
    public decimal MinimumQuantity { get; set; }
    public decimal MinimumAmount { get; set; }
    public bool IsEligible { get; set; }
    public string Note { get; set; } = string.Empty;

    // For compatibility with existing methods
    public object? Program { get; set; }
    public object? FrontMargin { get; set; }
}
