using Microsoft.EntityFrameworkCore;
using PurchaseManager.Constants.Enum;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Server.Services.Promotions.Interface;
using PurchaseManager.Shared.Dto.MarginDto.FrontMargin.ResultServices;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Storage;
namespace PurchaseManager.Server.Services.Promotions;

public class FrontMarginCalculationService : IFrontMarginCalculationService
{
    private readonly ApplicationDbContext _context;

    public FrontMarginCalculationService(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<FrontMarginCalculationResult> CalculateLineDiscountAsync(POLineGetDto poLine, List<PromotionFrontMargin> applicablePromotions)
    {
        var result = new FrontMarginCalculationResult
        {
            OriginalUnitCost = poLine.UnitCost,
            OriginalAmount = poLine.UnitCost * poLine.Quantity
        };

        foreach (var promotion in applicablePromotions)
        {
            var discountResult = promotion.DiscountType switch
            {
                (int)FrontMarginDiscountTypeTypeEnum.PercentageDiscount => CalculatePercentageDiscount(poLine, promotion),
                (int)FrontMarginDiscountTypeTypeEnum.FixedAmountDiscount => CalculateFixedAmountDiscount(poLine, promotion),
                (int)FrontMarginDiscountTypeTypeEnum.SameItemGift => CalculateSameItemGift(poLine, promotion),
                (int)FrontMarginDiscountTypeTypeEnum.DifferentItemGift => CalculateDifferentItemGift(poLine, promotion),
                _ => new DiscountCalculationResult()
            };

            result.AppliedPromotions.Add(new AppliedPromotionInfo
            {
                PromotionNumber = promotion.ProgramNumber,
                DiscountType = promotion.DiscountType,
                DiscountAmount = discountResult.DiscountAmount,
                NewUnitCost = discountResult.NewUnitCost,
                GiftLines = discountResult.GiftLines
            });

            // Apply the best discount (highest savings)
            if (discountResult.DiscountAmount <= result.TotalDiscountAmount)
            {
                continue;
            }
            result.TotalDiscountAmount = discountResult.DiscountAmount;
            result.FinalUnitCost = discountResult.NewUnitCost;
            result.BestPromotionNumber = promotion.ProgramNumber;
            result.GiftLines = discountResult.GiftLines;
        }

        result.FinalAmount = result.FinalUnitCost * poLine.Quantity;
        result.TotalSavings = result.OriginalAmount - result.FinalAmount;

        return result;
    }

    /// <summary>
    /// Case I.1: Percentage Discount
    /// Công thức: Giá Mua = Nguyên giá × (1 - % chiết khấu)
    /// </summary>
    private DiscountCalculationResult CalculatePercentageDiscount(
        POLineGetDto poLine,
        PromotionFrontMargin promotion)
    {
        var discountPercent = promotion.DiscountPercentage / 100;
        var newUnitCost = poLine.UnitCost * (1 - discountPercent);
        var discountAmount = (poLine.UnitCost - newUnitCost) * poLine.Quantity;

        // Apply maximum discount limit if set
        if (promotion.MaximumDiscountAmount <= 0 || discountAmount <= promotion.MaximumDiscountAmount)
        {
            return new DiscountCalculationResult
            {
                NewUnitCost = newUnitCost,
                DiscountAmount = discountAmount,
                CalculationMethod = "Percentage Discount",
                Details = $"Original: {poLine.UnitCost:C}, Discount: {promotion.DiscountPercentage}%, New: {newUnitCost:C}"
            };
        }
        discountAmount = promotion.MaximumDiscountAmount;
        newUnitCost = poLine.UnitCost - discountAmount / poLine.Quantity;

        return new DiscountCalculationResult
        {
            NewUnitCost = newUnitCost,
            DiscountAmount = discountAmount,
            CalculationMethod = "Percentage Discount",
            Details = $"Original: {poLine.UnitCost:C}, Discount: {promotion.DiscountPercentage}%, New: {newUnitCost:C}"
        };
    }

    /// <summary>
    /// Case I.2: Fixed Amount Discount
    /// Công thức:
    /// - Progressive (GiftCalculationType = 1): Discount tỷ lệ với quantity (mua 100SP KM 200k → 1000SP KM 2 triệu)
    /// - Milestone (GiftCalculationType = 2): Discount cố định (mua từ 100SP trở lên chỉ KM 200k)
    /// </summary>
    private static DiscountCalculationResult CalculateFixedAmountDiscount(POLineGetDto poLine, PromotionFrontMargin promotion)
    {
        if (promotion.FixedDiscountAmount <= 0)
            return new DiscountCalculationResult();

        decimal discountAmount;

        // Check GiftCalculationType for case 2 (Fixed Amount)
        if (promotion.GiftCalculationType == 1) // Progressive (Luỹ tiến)
        {
            // Calculate base quantity for discount calculation
            // If MinimumQuantity is set, use it as base, otherwise use 1
            var baseQuantity = promotion.MinimumQuantity > 0 ? promotion.MinimumQuantity : 1;

            // Progressive discount: multiply by ratio of actual quantity to base quantity
            var quantityRatio = poLine.Quantity / baseQuantity;
            discountAmount = promotion.FixedDiscountAmount * quantityRatio;
        }
        else // Milestone (Mốc) - Default case 2 behavior
        {
            // Fixed discount amount regardless of quantity (as long as minimum is met)
            discountAmount = promotion.FixedDiscountAmount;
        }

        // Add tier bonus if applicable
        if (promotion.TierQuantityThreshold > 0 &&
            poLine.Quantity >= promotion.TierQuantityThreshold &&
            promotion.TierBonusAmount.HasValue)
        {
            switch (promotion.GiftCalculationType)
            {
                case 1: // Progressive (Lũy kế) - nhân tier bonus với số tier sets
                    var tierSets = (int)((poLine.Quantity - promotion.TierQuantityThreshold.Value) /
                                         promotion.TierQuantityThreshold.Value) + 1;
                    discountAmount += promotion.TierBonusAmount.Value * tierSets;
                    break;

                default: // Default to Milestone (Mốc) for safety
                    discountAmount += promotion.TierBonusAmount.Value; // Add bonus once
                    break;
            }
        }

        // Apply maximum discount limit if set
        if (promotion.MaximumDiscountAmount > 0 && discountAmount > promotion.MaximumDiscountAmount)
        {
            discountAmount = promotion.MaximumDiscountAmount;
        }

        var newUnitCost = Math.Max(0, poLine.UnitCost - (discountAmount / poLine.Quantity));

        var calculationType = promotion.GiftCalculationType == 1 ? "Progressive" : "Milestone";
        var details = promotion.GiftCalculationType == 1
            ? $"Fixed discount (Progressive): Base {promotion.FixedDiscountAmount:C} × {poLine.Quantity}/{(promotion.MinimumQuantity > 0 ? promotion.MinimumQuantity : 1)} = {discountAmount:C}"
            : $"Fixed discount (Milestone): {promotion.FixedDiscountAmount:C}, Applied: {discountAmount:C}";

        return new DiscountCalculationResult
        {
            NewUnitCost = newUnitCost,
            DiscountAmount = discountAmount,
            CalculationMethod = $"Fixed Amount Discount ({calculationType})",
            Details = details
        };
    }

    /// <summary>
    /// Case II: Same Item Gift (Buy X Get Y Free)
    /// Công thức: Giá Mua = Tổng giá trị / Tổng số lượng
    /// </summary>
    private DiscountCalculationResult CalculateSameItemGift(
        POLineGetDto poLine,
        PromotionFrontMargin promotion)
    {
        if (promotion.BuyQuantity <= 0 || promotion.GiftQuantity <= 0)
            return new DiscountCalculationResult();

        var buyQty = promotion.BuyQuantity;
        var giftQty = promotion.GiftQuantity;

        // Calculate how many gift sets can be applied
        var giftSets = Math.Floor(poLine.Quantity / buyQty);
        var totalGiftQuantity = giftSets * giftQty;

        if (totalGiftQuantity <= 0)
        {
            return new DiscountCalculationResult();
        }
        var totalQuantityIncludingGifts = poLine.Quantity + totalGiftQuantity;
        var newUnitCost = poLine.UnitCost * poLine.Quantity / totalQuantityIncludingGifts;
        var discountAmount = (poLine.UnitCost - newUnitCost) * poLine.Quantity;

        // Apply maximum discount limit if set
        if (promotion.MaximumDiscountAmount <= 0 || discountAmount <= promotion.MaximumDiscountAmount)
        {
            return new DiscountCalculationResult
            {
                NewUnitCost = newUnitCost,
                DiscountAmount = discountAmount,
                CalculationMethod = "Same Item Gift",
                Details = $"Buy {buyQty}, Get {giftQty} free. Gift sets: {giftSets}, Total gifts: {totalGiftQuantity}",
                GiftLines =
                [
                    new POLineGetDto
                    {
                        ItemNumber = poLine.ItemNumber,
                        Description = $"{poLine.Description} (GIFT)",
                        Quantity = totalGiftQuantity,
                        UnitOfMeasure = poLine.UnitOfMeasure,
                        UnitCost = 0,
                        UnitPrice = 0,
                        Amount = 0,
                        DocumentType = 2,// Promotional item
                        LotNo = "KM"
                    }
                ]
            };
        }
        discountAmount = promotion.MaximumDiscountAmount;
        newUnitCost = poLine.UnitCost - discountAmount / poLine.Quantity;
        // Recalculate gift quantity based on max discount
        totalGiftQuantity = Math.Min(totalGiftQuantity,
        discountAmount * totalQuantityIncludingGifts / (poLine.UnitCost * poLine.Quantity) - poLine.Quantity);

        return new DiscountCalculationResult
        {
            NewUnitCost = newUnitCost,
            DiscountAmount = discountAmount,
            CalculationMethod = "Same Item Gift",
            Details = $"Buy {buyQty}, Get {giftQty} free. Gift sets: {giftSets}, Total gifts: {totalGiftQuantity}",
            GiftLines =
            [
                new POLineGetDto
                {
                    ItemNumber = poLine.ItemNumber,
                    Description = $"{poLine.Description} (GIFT)",
                    Quantity = totalGiftQuantity,
                    UnitOfMeasure = poLine.UnitOfMeasure,
                    UnitCost = 0,
                    UnitPrice = 0,
                    Amount = 0,
                    DocumentType = 2,// Promotional item
                    LotNo = "KM"
                }
            ]
        };

    }

    /// <summary>
    /// Case III: Different Item Gift
    /// Tặng hàng khác loại (không ảnh hưởng giá mua)
    /// </summary>
    private static DiscountCalculationResult CalculateDifferentItemGift(POLineGetDto poLine, PromotionFrontMargin promotion)
    {
        if (string.IsNullOrEmpty(promotion.GiftItemNumber) || promotion.GiftItemQuantity == 0)
            return new DiscountCalculationResult();

        // Check if minimum quantity/amount conditions are met
        if (promotion.MinimumQuantity > 0 && poLine.Quantity < promotion.MinimumQuantity)
            return new DiscountCalculationResult();

        if (promotion.MinimumAmount > 0 && poLine.UnitCost * poLine.Quantity < promotion.MinimumAmount)
            return new DiscountCalculationResult();

        return new DiscountCalculationResult
        {
            NewUnitCost = poLine.UnitCost, // No change to unit cost
            DiscountAmount = 0, // No monetary discount
            CalculationMethod = "Different Item Gift",
            Details = $"Gift: {promotion.GiftItemQuantity} {promotion.GiftItemUOM} {promotion.GiftItemName}",
            GiftLines =
            [
                new POLineGetDto
                {
                    ItemNumber = promotion.GiftItemNumber,
                    Description = $"{promotion.GiftItemName} (GIFT)",
                    Quantity = promotion.GiftItemQuantity,
                    UnitOfMeasure = promotion.GiftItemUOM ?? "",
                    UnitCost = 0,
                    UnitPrice = 0,
                    Amount = 0,
                    DocumentType = 2,// Promotional item
                    LotNo = "KM"
                }
            ]
        };
    }

    public async Task<POFrontMarginResult> ApplyFrontMarginToPOAsync(
        POHeaderGetDto poHeader,
        List<POLineGetDto> poLines)
    {
        var result = new POFrontMarginResult
        {
            OriginalPOAmount = poLines.Sum(l => l.UnitCost * l.Quantity)
        };

        foreach (var line in poLines)
        {
            // Get applicable promotions for this line
            var applicablePromotions = await GetApplicablePromotionsAsync(poHeader, line);

            if (applicablePromotions.Count == 0)
            {
                continue;
            }
            var lineResult = await CalculateLineDiscountAsync(line, applicablePromotions);

            // Update line with best discount
            line.UnitCost = lineResult.FinalUnitCost;
            line.Amount = lineResult.FinalAmount;

            result.LineResults.Add(lineResult);
            result.TotalSavings += lineResult.TotalSavings;

            // Add gift lines
            if (lineResult.GiftLines?.Any() == true)
            {
                result.GiftLines.AddRange(lineResult.GiftLines);
            }
        }

        result.FinalPOAmount = poLines.Sum(l => l.Amount);
        result.TotalDiscountPercentage = result.OriginalPOAmount > 0
            ? (result.TotalSavings / result.OriginalPOAmount) * 100
            : 0;

        return result;
    }

    public async Task<List<PromotionFrontMargin>> GetApplicablePromotionsAsync(POHeaderGetDto poHeader, POLineGetDto poLine)
    {
        var currentDate = DateTime.Now;

        // Get active Front Margin promotions for the vendor and item
        var promotions = await _context.PromotionFrontMargins
            .Include(p => p.PromotionHeader)
            .Where(p => p.PromotionHeader.VendorCode == poHeader.BuyFromVendorNumber &&
                       p.PromotionHeader.ProgramType == 1 && // Front Margin
                       p.PromotionHeader.Status == 2 && // Active
                       p.PromotionHeader.StartDate <= currentDate &&
                       p.PromotionHeader.EndDate >= currentDate &&
                       p.ItemNumber == poLine.ItemNumber &&
                       p.Status == 1 && // Active
                       p.ModificationStatus == 1) // Not deleted
            .Where(p => p.MinimumQuantity == 0 || poLine.Quantity >= p.MinimumQuantity)
            .Where(p => p.MinimumAmount == 0 || poLine.UnitCost * poLine.Quantity >= p.MinimumAmount)
            .ToListAsync();

        return promotions;
    }
}
