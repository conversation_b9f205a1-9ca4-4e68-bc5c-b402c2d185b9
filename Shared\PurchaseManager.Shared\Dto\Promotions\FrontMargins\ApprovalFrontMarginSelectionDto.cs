namespace PurchaseManager.Shared.Dto.Promotions.FrontMargins;

/// <summary>
/// DTO for Front Margin selections during PO approval
/// </summary>
public class ApprovalFrontMarginSelectionDto
{
    /// <summary>
    /// PO Number being approved
    /// </summary>
    public string PONumber { get; set; } = string.Empty;

    /// <summary>
    /// User who is approving
    /// </summary>
    public string ApprovedBy { get; set; } = string.Empty;

    /// <summary>
    /// Front Margin selections for each line
    /// </summary>
    public List<LinePromotionSelectionDto> LineSelections { get; set; } = [];

    /// <summary>
    /// Approval notes
    /// </summary>
    public string? Notes { get; set; }
}

/// <summary>
/// Front Margin selection for a specific PO line
/// </summary>
public class LinePromotionSelectionDto
{
    /// <summary>
    /// PO Line information
    /// </summary>
    public int POLineNumber { get; set; }
    public string ItemNumber { get; set; } = string.Empty;

    /// <summary>
    /// Selected promotion (null if no promotion selected)
    /// </summary>
    public string? SelectedPromotionNumber { get; set; }

    /// <summary>
    /// Selection action
    /// </summary>
    public PromotionSelectionAction Action { get; set; }

    /// <summary>
    /// Reason for selection/rejection
    /// </summary>
    public string? Reason { get; set; }
}

/// <summary>
/// Actions for promotion selection
/// </summary>
public enum PromotionSelectionAction
{
    /// <summary>
    /// Apply selected promotion
    /// </summary>
    Apply = 1,

    /// <summary>
    /// Skip promotion for this line
    /// </summary>
    Skip = 2,

    /// <summary>
    /// Keep existing selection
    /// </summary>
    KeepExisting = 3
}
