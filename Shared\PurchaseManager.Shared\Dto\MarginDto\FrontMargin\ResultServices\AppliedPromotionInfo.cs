using PurchaseManager.Shared.Dto.PO;
namespace PurchaseManager.Shared.Dto.MarginDto.FrontMargin.ResultServices;

public class AppliedPromotionInfo
{
    public string PromotionNumber { get; set; } = string.Empty;
    public int DiscountType { get; set; }
    public decimal DiscountAmount { get; set; }
    public decimal NewUnitCost { get; set; }
    public List<POLineGetDto>? GiftLines { get; set; }
}
