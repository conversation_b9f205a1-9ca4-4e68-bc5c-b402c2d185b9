namespace PurchaseManager.Shared.Dto.Promotions.Summarizes;

/// <summary>
/// Front Margin usage summary by vendor
/// </summary>
public class FrontMarginUsageSummary
{
    public string VendorCode { get; set; } = string.Empty;
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public int TotalUsages { get; set; }
    public decimal TotalSavingsAmount { get; set; }
    public decimal TotalOriginalAmount { get; set; }
    public decimal TotalFinalAmount { get; set; }
    public decimal AverageSavingsPercentage { get; set; }
    public Dictionary<int, int> UsagesByDiscountType { get; set; } = new Dictionary<int, int>();
    public List<PromotionUsageSummary> TopPromotions { get; set; } = [];
}
