using PurchaseManager.Shared.Dto.PO;
namespace PurchaseManager.Shared.Dto.MarginDto.FrontMargin.ResultServices;

// Result classes
public class FrontMarginCalculationResult
{
    public decimal OriginalUnitCost { get; set; }
    public decimal OriginalAmount { get; set; }
    public decimal FinalUnitCost { get; set; }
    public decimal FinalAmount { get; set; }
    public decimal TotalDiscountAmount { get; set; }
    public decimal TotalSavings { get; set; }
    public string? BestPromotionNumber { get; set; }
    public List<AppliedPromotionInfo> AppliedPromotions { get; set; } = [];
    public List<POLineGetDto>? GiftLines { get; set; }
}
