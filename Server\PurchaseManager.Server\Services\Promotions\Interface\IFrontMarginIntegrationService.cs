using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.MarginDto.FrontMargin.ResultServices;
using PurchaseManager.Shared.Dto.PO;
namespace PurchaseManager.Server.Services.Promotions.Interface;

/// <summary>
///     Service for integrating Front Margin with PO system
/// </summary>
public interface IFrontMarginIntegrationService
{
    /// <summary>
    ///     Get applicable Front Margin promotions for PO
    /// </summary>
    Task<List<PromotionFrontMargin>> GetApplicablePromotionsAsync(POHeaderGetDto poHeader);

    /// <summary>
    ///     Apply Front Margin to PO and return updated PO
    /// </summary>
    Task<POIntegrationResult> ApplyFrontMarginToPOAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines);

    /// <summary>
    ///     Preview Front Margin impact without applying
    /// </summary>
    Task<POIntegrationPreview> PreviewFrontMarginImpactAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines);

    /// <summary>
    ///     Validate PO for Front Margin eligibility
    /// </summary>
    Task<POValidationResult> ValidatePOForFrontMarginAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines);

    /// <summary>
    ///     Remove Front Margin from PO
    /// </summary>
    Task<POIntegrationResult> RemoveFrontMarginFromPOAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines);

    /// <summary>
    ///     Get Front Margin history for vendor
    /// </summary>
    Task<List<FrontMarginUsageHistory>> GetUsageHistoryAsync(string vendorCode, DateTime? fromDate = null, DateTime? toDate = null);
}
