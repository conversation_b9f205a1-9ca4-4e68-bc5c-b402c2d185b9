# Back Margin Database Migration Guide

## Tổng quan
Hướng dẫn này sẽ giúp bạn update database để hỗ trợ hệ thống Back Margin mới. Có 2 cách để thực hiện migration:

1. **SQL Script trực tiếp** - <PERSON><PERSON>h và đơn giản
2. **Entity Framework Migration** - Tích hợp với code và version control

## Phương pháp 1: SQL Script trực tiếp

### Bước 1: Chuẩn bị
```bash
# Backup database trước khi migration
sqlcmd -S your_server -d PurchaseManager -Q "BACKUP DATABASE PurchaseManager TO DISK = 'C:\Backup\PurchaseManager_BackMargin_Backup.bak'"
```

### Bước 2: Chạy SQL Script
```powershell
# Cách 1: Sử dụng PowerShell script (Khuyến nghị)
.\Run_BackMargin_Migration.ps1 -ConnectionString "Server=localhost;Database=PurchaseManager;Trusted_Connection=true;"

# Cách 2: Preview trước khi chạy
.\Run_BackMargin_Migration.ps1 -ConnectionString "Server=localhost;Database=PurchaseManager;Trusted_Connection=true;" -WhatIf

# Cách 3: Sử dụng SQL Server Management Studio
# Mở file Database_Migration_BackMargin.sql và execute
```

### Bước 3: Verify kết quả
```sql
-- Kiểm tra các bảng mới được tạo
SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_NAME LIKE 'PromotionBackMargin%'
ORDER BY TABLE_NAME;

-- Kết quả mong đợi:
-- PromotionBackMarginEarneds
-- PromotionBackMarginPayments
-- PromotionBackMarginTiers
-- PromotionBackMarginTrackings
```

## Phương pháp 2: Entity Framework Migration

### Bước 1: Generate Migration
```powershell
# Sử dụng PowerShell script
.\Generate_EF_Migration.ps1

# Hoặc manual command
cd Server\PurchaseManager.Storage
dotnet ef migrations add AddBackMarginTables --context ApplicationDbContext --startup-project ..\..\Server\PurchaseManager.Server
```

### Bước 2: Review Migration
```powershell
# Xem SQL script sẽ được execute
dotnet ef migrations script --context ApplicationDbContext --startup-project ..\..\Server\PurchaseManager.Server
```

### Bước 3: Apply Migration
```powershell
# Apply to database
dotnet ef database update --context ApplicationDbContext --startup-project ..\..\Server\PurchaseManager.Server
```

## Cấu trúc bảng được tạo

### 1. PromotionBackMarginTiers
- **Mục đích**: Lưu các mốc/bậc thang cho tính toán Back Margin
- **Quan hệ**: N-1 với PromotionBackMargins
- **Trường chính**: TierLevel, MinimumThreshold, MaximumThreshold, DiscountPercentage

### 2. PromotionBackMarginTrackings
- **Mục đích**: Tracking doanh số/số lượng theo thời gian
- **Quan hệ**: N-1 với PromotionBackMargins, N-1 với Vendors
- **Trường chính**: PONumber, Quantity, TotalAmount, AccumulatedQuantity, AccumulatedAmount

### 3. PromotionBackMarginEarneds
- **Mục đích**: Lưu Back Margin đã đạt điều kiện chờ trả thưởng
- **Quan hệ**: N-1 với PromotionBackMargins, 1-N với PromotionBackMarginPayments
- **Trường chính**: EarnedAmount, PeriodStartDate, PeriodEndDate, Status

### 4. PromotionBackMarginPayments
- **Mục đích**: Tracking việc trả thưởng Back Margin
- **Quan hệ**: N-1 với PromotionBackMarginEarneds
- **Trường chính**: PaymentAmount, PaymentDate, PaymentStatus, ApprovalStatus

## Troubleshooting

### Lỗi thường gặp

#### 1. "Foreign key constraint error"
```sql
-- Kiểm tra bảng PromotionBackMargins có tồn tại không
SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'PromotionBackMargins';

-- Nếu không có, tạo bảng này trước
-- (Bảng này có thể đã tồn tại từ trước)
```

#### 2. "Column already exists"
```sql
-- Script đã có check IF NOT EXISTS, nhưng nếu vẫn lỗi:
-- Comment out phần tạo column đã tồn tại trong script
```

#### 3. "SqlServer module not found"
```powershell
# Install SQL Server PowerShell module
Install-Module -Name SqlServer -Force -AllowClobber
```

### Rollback Migration

#### SQL Script Rollback
```sql
-- Xóa các bảng theo thứ tự ngược lại (do foreign key constraints)
DROP TABLE IF EXISTS PromotionBackMarginPayments;
DROP TABLE IF EXISTS PromotionBackMarginEarneds;
DROP TABLE IF EXISTS PromotionBackMarginTrackings;
DROP TABLE IF EXISTS PromotionBackMarginTiers;

-- Xóa các column đã thêm (nếu cần)
-- ALTER TABLE PromotionHeaders DROP COLUMN AccumulateRevenue;
-- ALTER TABLE PromotionHeaders DROP COLUMN SupportTypeNumber;
```

#### Entity Framework Rollback
```powershell
# Rollback to previous migration
dotnet ef database update PreviousMigrationName --context ApplicationDbContext --startup-project ..\..\Server\PurchaseManager.Server

# Remove migration file
dotnet ef migrations remove --context ApplicationDbContext --startup-project ..\..\Server\PurchaseManager.Server
```

## Verification Checklist

- [ ] Backup database đã được tạo
- [ ] 4 bảng Back Margin mới đã được tạo thành công
- [ ] Foreign key constraints hoạt động đúng
- [ ] Indexes đã được tạo
- [ ] Application có thể connect và query các bảng mới
- [ ] Entity Framework models sync với database schema

## Next Steps

Sau khi migration thành công:

1. **Update Application Code**
   - Verify Entity Framework models
   - Update DbContext registrations
   - Test CRUD operations

2. **Create Business Logic**
   - Implement Back Margin calculation services
   - Create tracking services
   - Implement payment processing

3. **Create UI Components**
   - Back Margin program management
   - Tracking dashboard
   - Payment management interface

4. **Testing**
   - Unit tests for new entities
   - Integration tests for Back Margin workflows
   - Performance testing with sample data

## Support

Nếu gặp vấn đề trong quá trình migration, hãy:

1. Check error logs chi tiết
2. Verify database permissions
3. Ensure all prerequisites are installed
4. Contact development team với error details
