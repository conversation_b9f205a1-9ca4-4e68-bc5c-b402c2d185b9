@using PurchaseManager.Shared.Dto.Promotions.FrontMargins

<MudDialog>
    <DialogContent>
        <MudContainer Class="pa-0">
            <MudPaper Class="pa-4" Elevation="0" Style="max-height: 75vh; overflow-y: auto;">
                <MudStack Spacing="3">
                    @* Header *@
                    <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                        <MudText Typo="Typo.h6" Color="Color.Primary">
                            <MudIcon Icon="Icons.Material.Filled.History" Class="mr-2"/>
                            KHUYẾN MÃI
                        </MudText>
                        <MudChip T="string" Color="Color.Info" Size="Size.Small">
                            @_promotionList.Count chương trình
                        </MudChip>
                        <MudIconButton Icon="@Icons.Material.Outlined.Refresh" OnClick="RefreshTableData" Size="Size.Small"/>
                    </MudStack>
                    @* Item Info *@
                    <MudPaper Class="pa-3" Style="background-color: #f5f5f5;" Elevation="0">
                        <MudGrid>
                            <MudItem xs="6">
                                <MudText Typo="Typo.body2"><strong>Mã hàng:</strong> @GetItemNumbersDisplay()</MudText>
                            </MudItem>
                            <MudItem xs="6">
                                <MudText Typo="Typo.body2"><strong>Nhà cung cấp:</strong> @VendorCode</MudText>
                            </MudItem>
                        </MudGrid>
                    </MudPaper>
                    @* Loading State *@
                    @if (IsLoading)
                    {
                        <MudStack AlignItems="AlignItems.Center" Class="pa-8">
                            <MudProgressCircular Color="Color.Primary" Indeterminate="true"/>
                            <MudText Typo="Typo.body2">Đang tải danh sách khuyến mãi...</MudText>
                        </MudStack>
                    }
                    else if (_promotionList.Count == 0)
                    {
                        @* Empty State *@
                        <MudStack AlignItems="AlignItems.Center" Class="pa-8">
                            <MudIcon Icon="Icons.Material.Filled.SearchOff" Size="Size.Large" Color="Color.Default"/>
                            <MudText Typo="Typo.body1" Color="Color.Default">
                                Không tìm thấy chương trình khuyến mãi nào
                            </MudText>
                        </MudStack>
                    }
                    else
                    {
                        @* Tabs for different promotion types *@
                        <MudTabs Elevation="2" Rounded ApplyEffectsToContainer MinimumTabWidth="20px">

                            @* Tab 1: Chiết Khấu (Cases 1 & 2) *@
                            <MudTabPanel Text="Chiết Khấu" Icon="@Icons.Material.Filled.Percent">
                                @{
                                    var discountData = _tableData.Where(x => x.DiscountType is 1 or 2).ToList();
                                }
                                @if (discountData.Any())
                                {
                                    @RenderDiscountPromotionTable(discountData)
                                }
                                else
                                {
                                    <MudStack AlignItems="AlignItems.Center" Class="pa-6">
                                        <MudIcon Icon="Icons.Material.Filled.Percent" Size="Size.Large" Color="Color.Default"/>
                                        <MudText Typo="Typo.body1" Color="Color.Default">
                                            Không có chương trình chiết khấu nào
                                        </MudText>
                                    </MudStack>
                                }
                            </MudTabPanel>

                            @* Tab 2: Tặng Hàng (Cases 3 & 4) *@
                            <MudTabPanel Text="Tặng Hàng" Icon="@Icons.Material.Filled.CardGiftcard">
                                @{
                                    var giftData = _tableData.Where(x => x.DiscountType is 3 or 4).ToList();
                                }
                                @if (giftData.Any())
                                {
                                    @RenderGiftPromotionTable(giftData)
                                }
                                else
                                {
                                    <MudStack AlignItems="AlignItems.Center" Class="pa-6">
                                        <MudIcon Icon="@Icons.Material.Filled.CardGiftcard" Size="Size.Large" Color="Color.Default"/>
                                        <MudText Typo="Typo.body1" Color="Color.Default">
                                            Không có chương trình tặng hàng nào*
                                        </MudText>
                                    </MudStack>
                                }
                            </MudTabPanel>
                        </MudTabs>
                    }
                </MudStack>
            </MudPaper>
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton Variant="Variant.Text"
                   Color="Color.Default"
                   OnClick="CloseDialog"
                   StartIcon="@Icons.Material.Filled.Close">
            Hủy
        </MudButton>
        <MudButton Variant="Variant.Filled"
                   Color="Color.Primary"
                   OnClick="ApplySelections"
                   StartIcon="@Icons.Material.Filled.Check">
            Áp dụng (@_selectedTableItems.Count)
        </MudButton>
    </DialogActions>
</MudDialog>

@code
{
    private RenderFragment RenderDiscountPromotionTable(List<PromotionTableItemDto> data) => __builder =>
    {
        <MudTable Items="@data"
                  Dense="true"
                  T="PromotionTableItemDto"
                  LoadingProgressColor="Color.Primary"
                  Hover="true"
                  GroupBy="@_groupDefinition"
                  GroupHeaderStyle="background-color: var(--mud-palette-background-grey); font-weight: 600;"
                  GroupFooterClass="mb-4"
                  Class="mt-2"
                  Elevation="2"
                  Style="table-layout: fixed; width: 100%;"
                  FixedHeader="true">
            <ColGroup>
                <col style="width: 50px;"/>
                <col style="width: 100px;"/>
                <col style="width: 180px;"/>
                <col style="width: 80px;"/>
                <col style="width: 80px;"/>
                <col style="width: 120px;"/>
                <col style="width: 100px;"/>
                <col style="width: 150px;"/>
                <col style="width: 200px;"/>
            </ColGroup>
            <HeaderContent>
                <MudTh Style="width: 50px; text-align: center; padding: 4px;">
                    <MudIcon Icon="Icons.Material.Filled.CheckBox" Size="Size.Small"/>
                </MudTh>
                <MudTh Style="width: 100px; padding: 4px; white-space: nowrap;">
                    <MudTableSortLabel SortBy="new Func<PromotionTableItemDto, object>(x => x.ItemNumber)">
                        Mã SP
                    </MudTableSortLabel>
                </MudTh>
                <MudTh Style="width: 180px; padding: 4px;">
                    <MudTableSortLabel SortBy="new Func<PromotionTableItemDto, object>(x => x.ItemName)">
                        Tên SP
                    </MudTableSortLabel>
                </MudTh>
                <MudTh Style="width: 80px; text-align: center; padding: 4px; white-space: nowrap;">Đơn vị mua</MudTh>
                <MudTh Style="width: 80px; text-align: center; padding: 4px; white-space: nowrap;">SL mua</MudTh>
                <MudTh Style="width: 120px; text-align: center; padding: 4px; white-space: nowrap;">Loại chiết khấu</MudTh>
                <MudTh Style="width: 100px; text-align: center; padding: 4px; white-space: nowrap;">Chiết khấu</MudTh>
                <MudTh Style="width: 150px; text-align: center; padding: 4px; white-space: nowrap;">Bonus</MudTh>
                <MudTh Style="width: 200px; padding: 4px;">Điều kiện</MudTh>
            </HeaderContent>

            <GroupHeaderTemplate>
                <MudTh Class="mud-table-cell-custom-group"
                       colspan="9"
                       Style="background-color: #f5f5f5; border-bottom: 2px solid #e0e0e0; padding: 12px 16px;">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="3">
                        <MudIcon Icon="@Icons.Material.Filled.Percent"
                                 Color="Color.Primary"
                                 Size="Size.Medium"/>
                        <MudText Typo="Typo.subtitle1"
                                 Style="font-weight: 700; color: #1976d2;">
                            @context.Key
                        </MudText>
                        <MudChip T="string"
                                 Color="Color.Info"
                                 Size="Size.Small"
                                 Variant="Variant.Text">
                            @context.Items.Count() sản phẩm
                        </MudChip>
                        <MudSpacer/>
                        <MudChip T="string"
                                 Color="Color.Success"
                                 Size="Size.Small"
                                 Variant="Variant.Filled">
                            Đang hoạt động
                        </MudChip>
                    </MudStack>
                </MudTh>
            </GroupHeaderTemplate>

            <RowTemplate>
                <MudTd Style="text-align: center; padding: 4px;">
                    @if (CanSelectPromotion(context))
                    {
                        <MudCheckBox T="bool"
                                     Value="@_selectedTableItems.Contains(context)"
                                     ValueChanged="@(value => OnTableItemSelected(context, value))"
                                     Color="Color.Primary"
                                     Size="Size.Small"/>
                    }
                    else
                    {
                        <MudTooltip Text="@GetPromotionBlockReason(context)" Arrow="true" Placement="Placement.Top">
                            <MudIcon Icon="@Icons.Material.Filled.Block"
                                     Color="Color.Error"
                                     Size="Size.Small"
                                     Style="cursor: help;"/>
                        </MudTooltip>
                    }
                </MudTd>
                <MudTd Style="padding: 4px; white-space: nowrap;">
                    <MudText Typo="Typo.body2"
                             Style="@($"font-weight: 500; font-size: 0.875rem; {GetRowStyle(context)}")">
                        @context.ItemNumber
                    </MudText>
                </MudTd>
                <MudTd Style="padding: 4px;">
                    <MudText Typo="Typo.body2"
                             Style="@($"font-size: 0.875rem; {GetRowStyle(context)}")">
                        @context.ItemName
                    </MudText>
                </MudTd>
                <MudTd Style="text-align: center; padding: 4px; white-space: nowrap;">
                    <MudText Typo="Typo.body2"
                             Style="@($"font-size: 0.875rem; {GetRowStyle(context)}")">
                        @{
                            var poLineForUOM = POLines?.FirstOrDefault(line =>
                                string.Equals(line.ItemNumber, context.ItemNumber, StringComparison.OrdinalIgnoreCase));
                        }
                        @(poLineForUOM?.UnitOfMeasure ?? context.UnitOfMeasure ?? "-")
                    </MudText>
                </MudTd>
                <MudTd Style="text-align: center; padding: 4px; white-space: nowrap;">
                    <MudText Typo="Typo.body2"
                             Style="@($"font-weight: 600; font-size: 0.875rem; {(IsRowMuted(context) ? "color: #999;" : "color: #2196f3;")}")">
                        @{
                            var poLine = POLines?.FirstOrDefault(line =>
                                string.Equals(line.ItemNumber, context.ItemNumber, StringComparison.OrdinalIgnoreCase));
                        }
                        @(poLine != null ? $"{(int)poLine.Quantity}" : "-")
                    </MudText>
                </MudTd>
                <MudTd Style="text-align: center; padding: 4px; white-space: nowrap;">
                    @{
                        var chipOpacity = IsRowMuted(context) ? "0.6" : "1";
                    }
                    @switch (context.DiscountType)
                    {
                        case 1:
                            <MudChip T="string" Color="Color.Primary" Size="Size.Small" Variant="Variant.Text" Style="@($"opacity: {chipOpacity};")">
                                <MudIcon Icon="@Icons.Material.Filled.Percent" Size="Size.Small" Class="mr-1"/>
                                Phần trăm
                            </MudChip>
                            break;
                        case 2:
                            <MudChip T="string" Color="Color.Default" Size="Size.Small" Variant="Variant.Text" Style="@($"opacity: {chipOpacity};")">
                                <MudIcon Icon="@Icons.Material.Filled.AttachMoney" Size="Size.Small" Class="mr-1"/>
                                Số tiền cố định
                            </MudChip>
                            break;
                    }
                </MudTd>
                <MudTd Style="text-align: center; padding: 4px; white-space: nowrap;">
                    <MudText Typo="Typo.body2"
                             Style="@($"font-weight: 600; font-size: 0.875rem; {(IsRowMuted(context) ? "color: #999;" : "color: #4caf50;")}")">
                        @context.DiscountValue
                    </MudText>
                </MudTd>
                <MudTd Style="text-align: center; padding: 4px; white-space: nowrap;">
                    @{
                        var bonusText = GetBonusDisplayText(context);
                        var isBonusEligible = IsTierBonusEligible(context);
                        var isUnitCompatible = IsUnitOfMeasureCompatible(context);
                    }
                    @if (bonusText != "-")
                    {
                        <MudText Typo="Typo.body2"
                                 Style="@($"font-weight: 600; font-size: 0.875rem; {(isUnitCompatible ? (isBonusEligible ? "color: #4caf50;" : "color: #ff9800;") : "color: #999;")}")">
                            @bonusText
                        </MudText>
                    }
                    else
                    {
                        <MudText Typo="Typo.body2" Style="color: #999; font-size: 0.875rem;">
                            Không có
                        </MudText>
                    }
                </MudTd>
                <MudTd Style="padding: 4px;">
                    <MudText Typo="Typo.caption"
                             Style="@($"font-size: 0.75rem; {GetRowStyle(context)}")">
                        @(context.MinimumQuantity > 0 ? $"Mua tối thiểu {(int)context.MinimumQuantity} {context.UnitOfMeasure}" :
                        context.MinimumAmount > 0 ? $"Giá trị tối thiểu {context.MinimumAmount:N0} VND" : "Không có điều kiện")
                    </MudText>
                </MudTd>
            </RowTemplate>
        </MudTable>
    };

    private RenderFragment RenderGiftPromotionTable(List<PromotionTableItemDto> data) => __builder =>
    {
        <MudTable Items="@data"
                  Dense="true"
                  T="PromotionTableItemDto"
                  LoadingProgressColor="Color.Primary"
                  Hover="true"
                  GroupBy="@_groupDefinition"
                  GroupHeaderStyle="background-color: var(--mud-palette-background-grey); font-weight: 600;"
                  GroupFooterClass="mb-4"
                  Class="mt-2"
                  Elevation="2"
                  Style="table-layout: fixed; width: 100%;"
                  FixedHeader="true">
            <ColGroup>
                <col style="width: 50px;"/>
                <col style="width: 100px;"/>
                <col style="width: 160px;"/>
                <col style="width: 100px;"/>
                <col style="width: 160px;"/>
                <col style="width: 80px;"/>
                <col style="width: 100px;"/>
                <col style="width: 100px;"/>
                <col style="width: 200px;"/>
            </ColGroup>
            <HeaderContent>
                <MudTh Style="width: 50px; text-align: center; padding: 4px;">
                    <MudIcon Icon="Icons.Material.Filled.CheckBox" Size="Size.Small"/>
                </MudTh>
                <MudTh Style="width: 100px; padding: 4px; white-space: nowrap;">
                    <MudTableSortLabel SortBy="new Func<dynamic, object>(x => x.ItemNumber)">
                        Mã SP
                    </MudTableSortLabel>
                </MudTh>
                <MudTh Style="width: 160px; padding: 4px;">
                    <MudTableSortLabel SortBy="new Func<dynamic, object>(x => x.ItemName)">
                        Tên SP
                    </MudTableSortLabel>
                </MudTh>
                <MudTh Style="width: 100px; padding: 4px; white-space: nowrap;">Mã SP tặng</MudTh>
                <MudTh Style="width: 160px; padding: 4px;">Tên SP tặng</MudTh>
                <MudTh Style="width: 80px; text-align: center; padding: 4px; white-space: nowrap;">Đơn vị tính</MudTh>
                <MudTh Style="width: 100px; text-align: center; padding: 4px; white-space: nowrap;">Số lượng tặng</MudTh>
                <MudTh Style="width: 100px; text-align: center; padding: 4px; white-space: nowrap;">Loại Bonus</MudTh>
                <MudTh Style="width: 200px; padding: 4px;">Điều kiện</MudTh>
            </HeaderContent>

            <GroupHeaderTemplate>
                <MudTh Class="mud-table-cell-custom-group"
                       colspan="9"
                       Style="background-color: #f5f5f5; border-bottom: 2px solid #e0e0e0; padding: 12px 16px;">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="3">
                        <MudIcon Icon="@Icons.Material.Filled.CardGiftcard"
                                 Color="Color.Primary"
                                 Size="Size.Medium"/>
                        <MudText Typo="Typo.subtitle1"
                                 Style="font-weight: 700; color: #1976d2;">
                            @context.Key
                        </MudText>
                        <MudChip T="string"
                                 Color="Color.Info"
                                 Size="Size.Small"
                                 Variant="Variant.Text">
                            @context.Items.Count() sản phẩm
                        </MudChip>
                        <MudSpacer/>
                        <MudChip T="string"
                                 Color="Color.Success"
                                 Size="Size.Small"
                                 Variant="Variant.Filled">
                            Đang hoạt động
                        </MudChip>
                    </MudStack>
                </MudTh>
            </GroupHeaderTemplate>

            <RowTemplate>
                <MudTd Style="@($"text-align: center; padding: 4px; {GetRowStyle(context)}")">
                    @if (CanSelectPromotion(context))
                    {
                        <MudCheckBox T="bool"
                                     Value="@_selectedTableItems.Contains(context)"
                                     ValueChanged="@(value => OnTableItemSelected(context, value))"
                                     Color="Color.Primary"
                                     Size="Size.Small"/>
                    }
                    else
                    {
                        <MudTooltip Text="@GetPromotionBlockReason(context)" Arrow="true" Placement="Placement.Top">
                            <MudIcon Icon="@Icons.Material.Filled.Block"
                                     Color="Color.Error"
                                     Size="Size.Small"
                                     Style="cursor: help;"/>
                        </MudTooltip>
                    }
                </MudTd>
                <MudTd Style="padding: 4px; white-space: nowrap;">
                    <MudText Typo="Typo.body2"
                             Style="@($"font-weight: 500; font-size: 0.875rem; {GetRowStyle(context)}")">
                        @context.ItemNumber
                    </MudText>
                </MudTd>
                <MudTd Style="padding: 4px;">
                    <MudText Typo="Typo.body2"
                             Style="@($"font-size: 0.875rem; {GetRowStyle(context)}")">
                        @context.ItemName
                    </MudText>
                </MudTd>
                <MudTd Style="padding: 4px; white-space: nowrap;">
                    <MudText Typo="Typo.body2"
                             Style="@($"font-weight: 500; font-size: 0.875rem; {GetRowStyle(context)}")">
                        @GetGiftItemNumber(context)
                    </MudText>
                </MudTd>
                <MudTd Style="padding: 4px;">
                    <MudText Typo="Typo.body2"
                             Style="@($"font-size: 0.875rem; {GetRowStyle(context)}")">
                        @GetGiftItemName(context)
                    </MudText>
                </MudTd>
                <MudTd Style="text-align: center; padding: 4px; white-space: nowrap;">
                    <MudText Typo="Typo.body2"
                             Style="@($"font-size: 0.875rem; {GetRowStyle(context)}")">
                        @GetGiftItemUOM(context)
                    </MudText>
                </MudTd>
                <MudTd Style="text-align: center; padding: 4px; white-space: nowrap;">
                    <MudText Typo="Typo.body2"
                             Style="@($"font-weight: 600; font-size: 0.875rem; {(IsRowMuted(context) ? "color: #999;" : "color: #4caf50;")}")">
                        @GetCalculatedGiftQuantity(context)
                    </MudText>
                </MudTd>
                <MudTd Style="text-align: center; padding: 4px; white-space: nowrap;">
                    @{
                        var chipOpacity = IsRowMuted(context) ? "0.6" : "1";
                    }
                    @if (context.GiftCalculationType == 1)
                    {
                        <MudChip T="string" Color="Color.Warning" Size="Size.Small" Variant="Variant.Text" Style="@($"opacity: {chipOpacity};")">
                            <MudIcon Icon="@Icons.Material.Filled.TrendingUp" Size="Size.Small" Class="mr-1"/>
                            Lũy kế
                        </MudChip>
                    }
                    else
                    {
                        <MudChip T="string" Color="Color.Info" Size="Size.Small" Variant="Variant.Text" Style="@($"opacity: {chipOpacity};")">
                            <MudIcon Icon="@Icons.Material.Filled.Lock" Size="Size.Small" Class="mr-1"/>
                            Cố định
                        </MudChip>
                    }
                </MudTd>
                <MudTd Style="padding: 4px;">
                    <MudText Typo="Typo.caption"
                             Style="@($"font-size: 0.75rem; {GetRowStyle(context)}")">
                        @GetGiftConditionText(context)
                    </MudText>
                </MudTd>
            </RowTemplate>
        </MudTable>
    };
}
