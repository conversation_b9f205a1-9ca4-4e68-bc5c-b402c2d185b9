﻿






//Autogenerated from Permissions.tt
using System.ComponentModel.DataAnnotations;

namespace PurchaseManager.Infrastructure.Storage.Permissions
{
    public static partial class Permissions
    {
        #region Admin

        public static class User
        {

            [Display(Name = "CreateUserPermission")]
            public const string Create = "User.Create";

            [Display(Name = "UpdateUserPermission")]
            public const string Update = "User.Update";

            [Display(Name = "ReadUserPermission")]
            public const string Read = "User.Read";

            [Display(Name = "DeleteUserPermission")]
            public const string Delete = "User.Delete";

        }

        public static class Role
        {

            [Display(Name = "CreateRolePermission")]
            public const string Create = "Role.Create";

            [Display(Name = "UpdateRolePermission")]
            public const string Update = "Role.Update";

            [Display(Name = "ReadRolePermission")]
            public const string Read = "Role.Read";

            [Display(Name = "DeleteRolePermission")]
            public const string Delete = "Role.Delete";

        }

        public static class Tenant
        {

            [Display(Name = "CreateTenantPermission")]
            public const string Create = "Tenant.Create";

            [Display(Name = "UpdateTenantPermission")]
            public const string Update = "Tenant.Update";

            [Display(Name = "ReadTenantPermission")]
            public const string Read = "Tenant.Read";

            [Display(Name = "DeleteTenantPermission")]
            public const string Delete = "Tenant.Delete";

        }

        public static class Promotion
        {
            [Display(Name = "CreatePromotionPermission")]
            public const string Create = "Promotion.Create";

            [Display(Name = "UpdatePromotionPermission")]
            public const string Update = "Promotion.Update";

            [Display(Name = "ViewPromotionPermission")]
            public const string View = "Promotion.View";

            [Display(Name = "DeletePromotionPermission")]
            public const string Delete = "Promotion.Delete";
        }

        #endregion
    }
}
