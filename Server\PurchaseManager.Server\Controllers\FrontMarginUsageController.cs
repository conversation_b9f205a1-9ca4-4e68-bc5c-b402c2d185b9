using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Dto.Promotions.Summarizes;
using PurchaseManager.Shared.Filters;
using PurchaseManager.Shared.Localizer;
using static Microsoft.AspNetCore.Http.StatusCodes;

namespace PurchaseManager.Server.Controllers;

/// <summary>
/// Controller for Front Margin usage tracking and reporting
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class FrontMarginUsageController : ControllerBase
{
    private readonly IFrontMarginUsageTrackingManager _usageTrackingManager;
    private readonly ApiResponse _invalidData;
    private readonly ILogger<FrontMarginUsageController> _logger;

    public FrontMarginUsageController(IStringLocalizer<Global> i18N, ILogger<FrontMarginUsageController> logger,
        IFrontMarginUsageTrackingManager usageTrackingManager)
    {
        _invalidData = new ApiResponse(Status400BadRequest, i18N["InvalidData"]);
        _logger = logger;
        _usageTrackingManager = usageTrackingManager;
    }
    
    
    /// <summary>
    /// Create Draft Front Margin usage when creating PO Line
    /// </summary>
    [HttpPost]
    public async Task<ApiResponse> CreateDraftUsage([FromBody] CreatePromotionFrontMarginUsageDto dto)
    {
        if (!ModelState.IsValid)
            return _invalidData;
        try
        {
            // Set status to Draft
            return await _usageTrackingManager.TrackPromotionUsageAsync(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating draft Front Margin usage");
            return ApiResponse.S500("Error creating draft usage");
        }
    }
    
    /// <summary>
    /// Cancel a Front Margin usage record
    /// </summary>
    [HttpPut("{number}/cancel")]
    public async Task<ApiResponse> CancelUsage(string number, [FromBody] CancelUsageRequest request)
    {
        return await _usageTrackingManager.CancelUsageAsync(number, request);
    }

    [HttpGet("front-margin/usage/{poNumber}")]
    public async Task<ApiResponse> GetPromotionFrontMarginUsage(string poNumber)
    {
        return await _usageTrackingManager.GetPromotionFrontMarginUsage(poNumber);
    }

    /// <summary>
    /// Update an existing Front Margin usage record
    /// </summary>
    [HttpPut("{number}/update")]
    public async Task<ApiResponse> UpdateUsage(string number, [FromBody] UpdatePromotionFrontMarginUsageDto dto)
    {
        return await _usageTrackingManager.UpdateUsageAsync(number, dto);
    }
}
