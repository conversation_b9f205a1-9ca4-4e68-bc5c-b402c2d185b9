using FluentValidation;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;

namespace PurchaseManager.Shared.Validators.Promotions;

public class CreatePromotionFrontMarginDtoValidator : AbstractValidator<CreatePromotionFrontMarginDto>
{
    public CreatePromotionFrontMarginDtoValidator()
    {
        RuleFor(x => x.ProgramNumber)
            .NotEmpty()
            .WithMessage("Program Number is required");

        RuleFor(x => x.ItemNumber)
            .NotEmpty()
            .WithMessage("Item Number is required");

        RuleFor(x => x.ItemName)
            .NotEmpty()
            .WithMessage("Item Name is required");

        RuleFor(x => x.UnitOfMeasure)
            .NotEmpty()
            .WithMessage("Unit of Measure is required");

        RuleFor(x => x.DiscountType)
            .InclusiveBetween(1, 4)
            .WithMessage("Discount Type must be between 1 and 4");

        // Case I.1: Percentage Discount
        When(x => x.DiscountType == 1, () => {
            RuleFor(x => x.DiscountPercentage)
                .GreaterThan(0)
                .LessThanOrEqualTo(100)
                .WithMessage("Discount Percentage must be between 0 and 100");
        });

        // Case I.2: Fixed Amount Discount
        When(x => x.DiscountType == 2, () => {
            RuleFor(x => x.FixedDiscountAmount)
                .NotNull()
                .GreaterThan(0)
                .WithMessage("Fixed Discount Amount is required and must be greater than 0");
        });

        // Case II: Same Item Gift
        When(x => x.DiscountType == 3, () => {
            RuleFor(x => x.BuyQuantity)
                .NotNull()
                .GreaterThan(0)
                .WithMessage("Buy Quantity is required for Same Item Gift");
                
            RuleFor(x => x.GiftQuantity)
                .NotNull()
                .GreaterThan(0)
                .WithMessage("Gift Quantity is required for Same Item Gift");
        });

        // Case III: Different Item Gift
        When(x => x.DiscountType == 4, () => {
            RuleFor(x => x.GiftItemNumber)
                .NotEmpty()
                .WithMessage("Gift Item Number is required for Different Item Gift");
                
            RuleFor(x => x.GiftItemName)
                .NotEmpty()
                .WithMessage("Gift Item Name is required for Different Item Gift");
                
            RuleFor(x => x.GiftItemUOM)
                .NotEmpty()
                .WithMessage("Gift Item UOM is required for Different Item Gift");
                
            RuleFor(x => x.GiftItemQuantity)
                .NotNull()
                .GreaterThan(0)
                .WithMessage("Gift Item Quantity is required for Different Item Gift");
        });

        // Optional validations
        When(x => x.MinimumQuantity.HasValue, () => {
            RuleFor(x => x.MinimumQuantity)
                .GreaterThan(0)
                .WithMessage("Minimum Quantity must be greater than 0");
        });

        When(x => x.MinimumAmount.HasValue, () => {
            RuleFor(x => x.MinimumAmount)
                .GreaterThan(0)
                .WithMessage("Minimum Amount must be greater than 0");
        });

        When(x => x.MaximumDiscountAmount.HasValue, () => {
            RuleFor(x => x.MaximumDiscountAmount)
                .GreaterThan(0)
                .WithMessage("Maximum Discount Amount must be greater than 0");
        });
    }
}
