using Microsoft.Extensions.Logging;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
namespace PurchaseManager.Shared.Services.Promotions;

public class FrontMarginApiClient : BaseApiClient, IFrontMarginApiClient
{

    public FrontMarginApiClient(HttpClient httpClient, ILogger<BaseApiClient> logger) : base(
    httpClient, logger, "api/FrontMargin")
    {
    }

    public async Task<ApiResponseDto<List<PromotionSelectedDto>>> GetPromotionsByVendorItemsAsync(PromotionSelectedRequestDto request)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto<List<PromotionSelectedDto>>>(
        $"{rootApiPath}/by-vendor-items", request);
    }
}
