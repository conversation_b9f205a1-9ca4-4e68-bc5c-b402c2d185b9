@inject IFrontMarginUIService FrontMarginService

<div class="front-margin-indicator">
    @if (IsLoading)
    {
        <MudProgressCircular Size="Size.Small" Indeterminate="true"/>
        <span class="ml-2">Checking Front Margin...</span>
    }
    else if (HasFrontMargin)
    {
        <MudChip T="string" Color="Color.Success" Size="Size.Small" Icon="@Icons.Material.Filled.LocalOffer">
            Front Margin Available
        </MudChip>

        @if (ShowDetails && ApplicablePromotions.Any())
        {
            <MudCard Class="mt-2">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">Available Front Margin Promotions</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    @foreach (var promotion in ApplicablePromotions)
                    {
                        <MudAlert Severity="Severity.Info" Class="mb-2">
                            <div>
                                <strong>@promotion.ProgramNumber</strong> - @promotion.ItemName
                            </div>
                            <div>
                                <small>
                                    @promotion.DiscountTypeName: @promotion.DiscountValue
                                    (@promotion.StartDate.ToString("dd/MM/yyyy") - @promotion.EndDate.ToString("dd/MM/yyyy"))
                                </small>
                            </div>
                        </MudAlert>
                    }
                </MudCardContent>
            </MudCard>
        }
    }
    else if (CheckCompleted)
    {
        <MudChip T="string" Color="Color.Default" Size="Size.Small" Icon="@Icons.Material.Filled.Info">
            No Front Margin
        </MudChip>
    }
</div>

@code {
    [Parameter] public string VendorCode { get; set; } = string.Empty;
    [Parameter] public DateTime OrderDate { get; set; } = DateTime.Now;
    [Parameter] public bool ShowDetails { get; set; }
    [Parameter] public EventCallback<bool> OnFrontMarginStatusChanged { get; set; }

    private bool IsLoading;
    private bool HasFrontMargin;
    private bool CheckCompleted;
    private List<FrontMarginPromotionInfo> ApplicablePromotions = new List<FrontMarginPromotionInfo>();

    protected override async Task OnParametersSetAsync()
    {
        if (!string.IsNullOrEmpty(VendorCode))
        {
            await CheckFrontMarginAsync();
        }
    }

    private async Task CheckFrontMarginAsync()
    {
        IsLoading = true;
        CheckCompleted = false;
        StateHasChanged();

        try
        {
            HasFrontMargin = await FrontMarginService.HasActiveFrontMarginAsync(VendorCode);

            if (HasFrontMargin && ShowDetails)
            {
                ApplicablePromotions = await FrontMarginService.GetApplicablePromotionsAsync(VendorCode, OrderDate);
            }

            await OnFrontMarginStatusChanged.InvokeAsync(HasFrontMargin);
        }
        catch (Exception ex)
        {
            // Log error
            Console.WriteLine($"Error checking Front Margin: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
            CheckCompleted = true;
            StateHasChanged();
        }
    }

    public async Task RefreshAsync()
    {
        await CheckFrontMarginAsync();
    }
}

<style>
    .front-margin-indicator {
        display: flex;
        align-items: center;
        gap: 8px;
    }
</style>
