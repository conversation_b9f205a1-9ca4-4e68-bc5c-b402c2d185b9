﻿namespace PurchaseManager.Shared.Dto.Promotions.FrontMargins;

public class CreatePromotionFrontMarginDto
{
    public string ProgramNumber { get; set; } = null!;

    public int LineNumber { get; set; }

    public string ItemNumber { get; set; } = null!;

    public string ItemName { get; set; } = null!;

    public string UnitOfMeasure { get; set; } = null!;

    /// <summary>
    /// Loại chiết khấu: 1=Percentage, 2=FixedAmount, 3=SameItemGift, 4=DifferentItemGift
    /// </summary>
    public int DiscountType { get; set; } = 1;

    // CASE I.1: Percentage Discount
    public decimal DiscountPercentage { get; set; } = 0;

    // CASE I.2: Fixed Amount Discount
    public decimal? FixedDiscountAmount { get; set; }

    // CASE II: Same Item Gift (Buy X Get Y Free)
    public decimal? BuyQuantity { get; set; }
    public decimal? GiftQuantity { get; set; }

    // CASE III: Different Item Gift
    public string? GiftItemNumber { get; set; }
    public string? GiftItemName { get; set; }
    public string? GiftItemUOM { get; set; }
    public decimal? GiftItemQuantity { get; set; }

    // Conditions
    public decimal? MinimumQuantity { get; set; }
    public decimal? MinimumAmount { get; set; }
    public decimal? MaximumDiscountAmount { get; set; }

    // Progressive/Tiered Discount Fields
    /// <summary>
    /// Quantity threshold for tier bonus (e.g., 100 units)
    /// </summary>
    public decimal? TierQuantityThreshold { get; set; }

    /// <summary>
    /// Additional percentage discount when tier threshold is met (e.g., 5%)
    /// Used with DiscountType = 1 (Percentage)
    /// </summary>
    public decimal? TierBonusPercentage { get; set; }

    /// <summary>
    /// Additional fixed amount discount when tier threshold is met (e.g., 2,000,000)
    /// Used with DiscountType = 2 (Fixed Amount)
    /// </summary>
    public decimal? TierBonusAmount { get; set; }

    /// <summary>
    /// Gift calculation type: 1=Progressive, 2=Milestone
    /// Only used for DiscountType = 3 (Same Item Gift) and 4 (Different Item Gift)
    /// Progressive: Gift quantity is proportional to buy quantity (Luỹ tiến)
    /// Milestone: Gift quantity is fixed when threshold is reached (Mốc)
    /// </summary>
    public int? GiftCalculationType { get; set; } = 1; // Default to Progressive

    public string? Notes { get; set; }
}
